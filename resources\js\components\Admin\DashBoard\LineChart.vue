<template>
    <Line :key="chartKey" :data="chartConfig.data" :options="chartConfig.options" />
</template>

<script lang="ts">
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
} from 'chart.js'
import { Line } from 'vue-chartjs'
import * as chartConfig from './chartConfig.js'

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
)

export default {
    name: 'LineChart',
    components: {
        Line
    },
    data() {
        return {
            load: true,
            chartConfig,
            chartKey: 0
        };
    },
    async mounted () {
        this.loaded = true
        try {
            const response = await fetch('/api/created-cv-chart');
            const responseApply = await fetch('/api/created-apply-job-chart');
            console.log(responseApply);
            const {data} = await response.json();
            const {dataApply} = await responseApply.json();
            chartConfig.updateChartData([data,dataApply]);
            this.chartKey++;
        } catch (error) {
            console.error(error);
        }
    }

    }
</script>
<style scoped>
h3 {
    margin: 40px 0 0;
}
ul {
    list-style-type: none;
    padding: 0;
}
li {
    display: inline-block;
    margin: 0 10px;
}
a {
    color: #42b983;
}
</style>
