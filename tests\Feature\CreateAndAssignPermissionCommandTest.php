<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CreateAndAssignPermissionCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo một số role để test
        Role::create(['name' => 'admin', 'guard_name' => 'web']);
        Role::create(['name' => 'user', 'guard_name' => 'web']);
        Role::create(['name' => 'manager', 'guard_name' => 'web']);
    }

    public function test_command_creates_new_permission_and_assigns_to_all_roles()
    {
        $permissionName = 'test.new-permission';

        // Kiểm tra permission chưa tồn tại
        $this->assertDatabaseMissing('permissions', ['name' => $permissionName]);

        // Chạy command
        $this->artisan('permission:create-and-assign', ['permission_name' => $permissionName])
            ->expectsOutput("✓ Permission '{$permissionName}' created successfully.")
            ->expectsOutput("Found 3 role(s). Assigning permission...")
            ->expectsOutput("✓ Permission '{$permissionName}' assigned to 3 role(s) successfully.")
            ->expectsOutput("✓ Permission cache cleared.")
            ->assertExitCode(0);

        // Kiểm tra permission đã được tạo
        $this->assertDatabaseHas('permissions', [
            'name' => $permissionName,
            'guard_name' => 'web'
        ]);

        // Kiểm tra tất cả role đã có permission này
        $roles = Role::all();
        foreach ($roles as $role) {
            $this->assertTrue($role->hasPermissionTo($permissionName));
        }
    }

    public function test_command_handles_existing_permission()
    {
        $permissionName = 'test.existing-permission';

        // Tạo permission trước
        Permission::create(['name' => $permissionName, 'guard_name' => 'web']);

        // Chạy command
        $this->artisan('permission:create-and-assign', ['permission_name' => $permissionName])
            ->expectsOutput("✓ Permission '{$permissionName}' already exists.")
            ->expectsOutput("Found 3 role(s). Assigning permission...")
            ->expectsOutput("✓ Permission '{$permissionName}' assigned to 3 role(s) successfully.")
            ->assertExitCode(0);

        // Kiểm tra tất cả role đã có permission này
        $roles = Role::all();
        foreach ($roles as $role) {
            $this->assertTrue($role->hasPermissionTo($permissionName));
        }
    }

    public function test_command_handles_permission_already_assigned_to_some_roles()
    {
        $permissionName = 'test.partial-permission';

        // Tạo permission và assign cho một role
        $permission = Permission::create(['name' => $permissionName, 'guard_name' => 'web']);
        $adminRole = Role::where('name', 'admin')->first();
        $adminRole->givePermissionTo($permission);

        // Chạy command
        $this->artisan('permission:create-and-assign', ['permission_name' => $permissionName])
            ->expectsOutput("✓ Permission '{$permissionName}' already exists.")
            ->expectsOutput("Found 3 role(s). Assigning permission...")
            ->expectsOutput("  - Role 'admin' already has this permission")
            ->expectsOutput("  ✓ Assigned to role: user")
            ->expectsOutput("  ✓ Assigned to role: manager")
            ->expectsOutput("✓ Permission '{$permissionName}' assigned to 2 role(s) successfully.")
            ->assertExitCode(0);

        // Kiểm tra tất cả role đã có permission này
        $roles = Role::all();
        foreach ($roles as $role) {
            $this->assertTrue($role->hasPermissionTo($permissionName));
        }
    }

    public function test_command_handles_no_roles_in_system()
    {
        // Xóa tất cả role
        Role::query()->delete();

        $permissionName = 'test.no-roles-permission';

        // Chạy command
        $this->artisan('permission:create-and-assign', ['permission_name' => $permissionName])
            ->expectsOutput("✓ Permission '{$permissionName}' created successfully.")
            ->expectsOutput("No roles found in the system.")
            ->assertExitCode(0);

        // Kiểm tra permission đã được tạo
        $this->assertDatabaseHas('permissions', [
            'name' => $permissionName,
            'guard_name' => 'web'
        ]);
    }

    public function test_command_handles_all_roles_already_have_permission()
    {
        $permissionName = 'test.all-assigned-permission';

        // Tạo permission và assign cho tất cả role
        $permission = Permission::create(['name' => $permissionName, 'guard_name' => 'web']);
        $roles = Role::all();
        foreach ($roles as $role) {
            $role->givePermissionTo($permission);
        }

        // Chạy command
        $this->artisan('permission:create-and-assign', ['permission_name' => $permissionName])
            ->expectsOutput("✓ Permission '{$permissionName}' already exists.")
            ->expectsOutput("Found 3 role(s). Assigning permission...")
            ->expectsOutput("✓ All roles already have the permission '{$permissionName}'.")
            ->assertExitCode(0);
    }
}
