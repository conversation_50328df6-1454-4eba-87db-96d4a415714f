@extends(backpack_view('blank'))

@section('content')
    {{--    @dd($crud->entry)--}}

    <div class="contact__wrapper shadow-lg mt-n9">
        <div class="row no-gutters">
            <div class="col-lg-5 contact-info__wrapper gradient-brand-color p-5 order-lg-2">
                <div class="bg-danger d-lg-inline-block py-1-9 px-1-9 px-sm-6 mb-1-9 rounded py-2"
                     style="margin-bottom: 15px">
                    <h3 class="h2 text-white mb-0">{{ $entry->name }}</h3>
                </div>


                <ul class="list-unstyled mb-1-9 row">
                    <div class="col-md-6">
                        <li class="mb-2 mb-xl-3 display-28"><span
                                class="display-26 text-secondary me-2 font-weight-600">Email:</span>
                            {{ $entry->email }}</li>
                        <li class="mb-2 mb-xl-3 display-28"><span
                                class="display-26 text-secondary me-2 font-weight-600">Mobile:</span> {{ $entry->mobile }}
                        </li>
                        <li class="mb-2 mb-xl-3 display-28"><span
                                class="display-26 text-secondary me-2 font-weight-600">
                                                    Address:</span>{{ $entry->address }}</li>
                    </div>
                    <div class="col-md-6">
                        <li class="mb-2 mb-xl-3 display-28"><span
                                class="display-26 text-secondary me-2 font-weight-600">Size:</span>
                            {{ $entry->size }}</li>
                        <li class="mb-2 mb-xl-3 display-28"><span
                                class="display-26 text-secondary me-2 font-weight-600">Website:</span>{{ $entry->website }}
                        </li>
                        <li class="mb-2 mb-xl-3 display-28"><span
                                class="display-26 text-secondary me-2 font-weight-600">Number working year:</span>{{ $entry->number_working_year }}
                        </li>
                    </div>
                </ul>
                <div class="btn btn-primary d-lg-inline-block py-1-9 px-1-9 px-sm-6 mb-1-9 rounded py-2"
                     style="margin-bottom: 15px">
                    <a href="{{route('lead.create.from.company',['id' => $entry->id])}}" class="h2 text-white mb-0">Create Lead</a>
                </div>
            </div>

            <!-- End Contact Form Wrapper -->
        </div>
    </div>
    <div role="tabpanel" class="tab-pane py-4" id="tab_cv">

        <div class="row">
            @php
                $jobs = $entry->job;
            @endphp
            @if(count($jobs))
                <table
                    class="table table-striped table-hover nowrap rounded card-table table-vcenter card d-table shadow-xs border-xs dataTable dtr-inline"
                    id="table-cvs">
                    <thead>
                    <tr>
                        <th>Job title</th>
                        <th>Số lượng Job</th>
                        <th>Mô tả Job</th>
                        <th>Địa chỉ</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    @foreach ($jobs as $jb)
                        <tr>
                            <td>{{ $jb->title }}</td>
                            <td>{{ $jb->vacancy }}</td>
                            <td>{{ strlen($jb->description) > 50 ? substr($jb->description, 0, 50 ) .'...' : $jb->description}}</td>
                            <td>{{ $jb->address }}</td>

                            <td>
                                <a href="{{ route('job.show', $jb->id) }}" target="_blank"
                                   class="btn btn-sm btn-link">
                                    <span><i class="la la-edit"></i>Show </span>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </table>
            @endif

        </div>

    </div>

@endsection
@section('before_styles')
    <link href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css"/>
    <link href="https://cdn.datatables.net/fixedheader/3.3.1/css/fixedHeader.dataTables.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="https://cdn.datatables.net/responsive/2.4.0/css/responsive.dataTables.min.css" rel="stylesheet"
          type="text/css"/>
@endsection
@section('after_scripts')
    <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/fixedheader/3.3.1/js/dataTables.fixedHeader.min.js"></script>
@endsection
@push('after_scripts')
    <script>
        $(document).ready(function () {
            $('#table-cvs').dataTable();
        });
    </script>
@endpush
