<?php

namespace App\Http\Resources\Api\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CvResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
       return [
           'job'            => $this->job_title,
           'level'          => $this->level,
           'yoe'            => $this->yoe,
           'salary_expect'  => $this->salary_expect,
           'language'       => $this->careerLanguages->pluck('name_vi')->toArray(),
           'skill'          => $this->skills->pluck('name')->toArray(),
           'created_at'     => Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
           'cv_private'     => $this->cv_private,
           'cv_private_url' => $this->cv_private ? secure_file_url($this->id, 'Cv', 'cv_private') : null,
       ];
    }
}
