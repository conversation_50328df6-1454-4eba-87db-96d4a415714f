<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use App\Events\MeetingBookingCreatedEvent;
use Backpack\CRUD\app\Models\Traits\HasRelationshipFields;

class MeetingBooking extends Model
{
    use HasFactory, SoftDeletes, CrudTrait, HasRelationshipFields;

    protected $fillable = [
        'title',
        'description',
        'meeting_room_id',
        'created_by',
        'start_time',
        'end_time',
        'status',
        'meeting_link',
        'agenda'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime'
    ];

    protected $dispatchesEvents = [
        'created' => MeetingBookingCreatedEvent::class,
    ];

    /**
     * Get the meeting room for the booking.
     */
    public function meetingRoom()
    {
        return $this->belongsTo(MeetingRoom::class);
    }

    /**
     * Get the creator of the booking.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the participants for the booking.
     */
    public function participants()
    {
        return $this->hasMany(MeetingParticipant::class);
    }

    /**
     * Get the participant users.
     */
    public function participantUsers()
    {
        return $this->belongsToMany(User::class, 'meeting_participants')
            ->withPivot(['status', 'is_organizer', 'email_sent'])
            ->withTimestamps();
    }

    /**
     * Check if a user is a participant
     */
    public function isParticipant($userId)
    {
        return $this->participants()->where('user_id', $userId)->exists();
    }

    /**
     * Add a participant to the meeting
     */
    public function addParticipant($userId, $isOrganizer = false)
    {
        return $this->participants()->create([
            'user_id' => $userId,
            'is_organizer' => $isOrganizer,
            'status' => 'invited'
        ]);
    }
}
