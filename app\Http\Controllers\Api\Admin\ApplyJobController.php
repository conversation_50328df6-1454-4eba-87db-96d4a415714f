<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Admin\ApplyJobRequest;
use App\Http\Resources\Api\Admin\LogChangeApplyJobResource;
use App\Repositories\ApplyJobRepository;
use App\Services\ApplyJobService;
use App\Services\LogChangeStatusApplyService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApplyJobController extends Controller
{
    protected $applyJobService;
    protected $logChangeStatusApplyService;

    protected $applyJobRepo;

    public function __construct(ApplyJobService $applyJobService, LogChangeStatusApplyService $logChangeStatusApplyService, ApplyJobRepository $applyJobRepo)
    {
        $this->applyJobService = $applyJobService;
        $this->logChangeStatusApplyService = $logChangeStatusApplyService;
        $this->applyJobRepo = $applyJobRepo;
    }

    public function create(ApplyJobRequest $request)
    {
        try {
            DB::beginTransaction();
            $data = $request->only(['status', 'date', 'note','apply_job_id']);
            $this->applyJobService->update($request->apply_job_id, $data);
            $data['user_id'] = backpack_auth()->id();
            $data['status_id'] = $data['status'];
            unset($data['status']);
            $this->logChangeStatusApplyService->create($data);
            DB::commit();

            return response()->json(['success' => 'Apply job successfully']);
        } catch (\Exception $e) {
            // dd($e);
            DB::rollBack();
            return response()->json(['error' => 'Apply job successfully'], $e->getCode());
        }

    }

    public function getLogApplyJob($id)
    {
        return response()->json(['data' => LogChangeApplyJobResource::collection($this->logChangeStatusApplyService->queryList(['apply_job_id' => $id])->get())]);
    }

    public function getApplyJobChart()
    {
        return response()->json(['dataApply' => $this->applyJobRepo->getLineChartWeek()]);
    }

}
