<?php

namespace App\Services;

use App\Models\LogTask;

class LogTaskService
{
    public function queryList(array $filters = [])
    {
        return LogTask::query()
            ->when(!empty($filters['task_id']), function ($query) use ($filters) {
                $query->where('task_id', $filters['task_id']);
            })
            ->latest('id');
    }

    public function create(array $data)
    {
        return LogTask::create($data);
    }

}
