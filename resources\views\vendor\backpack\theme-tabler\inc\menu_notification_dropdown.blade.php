@if(true)
    <div id="overlay">
        <div class="loader"></div>
    </div>
    <div class="nav-item dropdown">

        <a href="#" class="nav-link d-flex lh-1 text-reset p-0 notification" data-bs-toggle="dropdown" aria-label="Open notification">
            <img class="avatar-img" src="{{ backpack_avatar_url(backpack_auth()->user()) }}"
                 alt="{{ backpack_auth()->user()->name }}" onerror="this.style.display='none'"
                 style="margin: 0;position: absolute;left: 0;z-index: 1;">
            <span class="backpack-avatar-menu-container text-center"
                  style="position: absolute;left: 0;width: 100%;background-color: #00a65a;border-radius: 50%;color: #FFF;line-height: 35px;font-size: 85%;font-weight: 300;">
    <svg xmlns="http://www.w3.org/2000/svg" height="16" fill="white" width="14" viewBox="0 0 448 512"><!--!Font Awesome Free 6.5.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2023 Fonticons, Inc.--><path
        d="M224 0c-17.7 0-32 14.3-32 32V51.2C119 66 64 130.6 64 208v25.4c0 45.4-15.5 89.5-43.8 124.9L5.3 377c-5.8 7.2-6.9 17.1-2.9 25.4S14.8 416 24 416H424c9.2 0 17.6-5.3 21.6-13.6s2.9-18.2-2.9-25.4l-14.9-18.6C399.5 322.9 384 278.8 384 233.4V208c0-77.4-55-142-128-156.8V32c0-17.7-14.3-32-32-32zm0 96c61.9 0 112 50.1 112 112v25.4c0 47.9 13.9 94.6 39.7 134.6H72.3C98.1 328 112 281.3 112 233.4V208c0-61.9 50.1-112 112-112zm64 352H224 160c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7s18.7-28.3 18.7-45.3z"/></svg>
                @if($notifications->where('is_watched',0)->count() > 0)
                    <span class="badge">
                    {{ $notifications->where('is_watched',0)->count() }}</span>
                @endif
        </span>
        </a>
        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow dropdown-notification">
            @if(count($notifications) > 0 )
                @foreach($notifications as $notification)
                    <a  @if($notification->is_watched == 1) href="{{ $notification->link ??'javascript:void(0);' }}" @else href="javascript:void(0);"  @endif class="dropdown-item @if($notification->is_watched == 0)check-noti not-is-watched @endif"><i class="la la-bell me-2"></i>{{ $notification->content }}</a>
                    <div class="dropdown-divider"></div>
                @endforeach
                <a class="dropdown-item text-center check-all-noti" href="javascript:void(0);">Đánh dấu đọc tất cả</a>
            @endif
        </div>
    </div>
    <style>
        .notification .badge {
            position: absolute;
            top: -10px;
            right: -10px;
            padding: 5px 10px;
            border-radius: 50%;
            background: red;
            color: white;
        }

        .not-is-watched {
            font-weight: bold;
        }

        .not-is-watched > i {
            color: red;
        }

        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5); /* Semi-transparent black overlay */
            z-index: 2; /* Ensure overlay is above the content */
        }

        .loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: translate(-50%, -50%) rotate(0deg);
            }
            100% {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }
    </style>
    @push('after_scripts')
        <script>
            $(document).ready(function () {
                $('.check-all-noti').on('click', function () {
                    $('#overlay').fadeIn();
                    $.ajax({
                        type: 'POST',
                        url: '{{ route('notify.update-watched') }}',
                        data: {
                            check_all: 1
                        },
                        success: function (data) {
                            window.location.reload();

                        }
                    });
                });
                $('.check-noti').on('click', function () {
                    $('#overlay').fadeIn();

                    let id = $(this).data('id');
                    let href = $(this).data('href');
                    console.log()
                    $.ajax({
                        type: 'POST',
                        url: '{{ route('notify.update-watched') }}',
                        data: {
                            id: id
                        },
                        success: function (data) {
                            if(href !==''){
                                window.location.href = href;
                            }
                            window.location.reload();
                        }
                    });
                })
            });
        </script>
    @endpush
@endif


