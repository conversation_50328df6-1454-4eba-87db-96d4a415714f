<?php

namespace App\Rules;

use App\Services\ApplyJobService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckExitsApplyJob implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty(request('id')) && (new ApplyJobService)->checkExitsApplyJob(request('job_id'),request('candidate_id'))) {
            ;
            $fail('Ứng viên đã được apply vào job');
        }
    }

}
