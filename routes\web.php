<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application.
| These routes are loaded by the RouteServiceProvider and assigned to the "web" middleware group.
| Make something great!
|
*/

Route::get('/', function () {
    return redirect('/admin/dashboard');
});

Route::group(
    [
        'prefix'     => config('backpack.base.route_prefix', 'admin'),
        'middleware' => array_merge(
            (array)config('backpack.base.web_middleware', 'web'),
            (array)config('backpack.base.middleware_key', 'admin')
        ),
        'namespace'  => 'App\Http\Controllers\Admin',
    ],
    function () { // custom admin routes
        Route::get('lead/{id}/create-company', 'LeadController@createCompany')->name('lead.create-company');
        Route::post('apply-job/store-ajax', 'ApplyJobCrudController@storeAjax')->name('apply-job.store-ajax');
        Route::get('download/cv-private/aplly-job/{id}', 'DownloadController@applyJobCvPrivate')->name('download.cv-private.apply-job');
        Route::get('lead/create-from-company/{id}', 'CompanyCrudController@createLeadFromCompany')->name('lead.create.from.company');
        Route::post('notify/change-notify', 'NotificationController@changeNotify')->name('notify.update-watched');
        Route::get('report/report-team', 'ReportController@teamApply')->name('report.index');
        Route::get('report/report-team', 'ReportController@teamApply')->name('report.only-team');
        Route::get('report/report-lead-by-source', 'ReportController@leadBySource')->name('report.lead-by-source');
        Route::get('report/report-lead-by-user', 'ReportController@leadByUser')->name('report.lead-by-user');
        Route::get('report/apply-bodyshop', 'ReportController@applyBodyshop')->name('report.apply-bodyshop');
        Route::post('lead/import', 'LeadCrudController@import');
        Route::get('statistical/change-status-lead', 'StatisticalController@changeStatusLead')->name('statistical.change-status-lead');
        Route::get('apply-job/match-score/{id}', 'ApplyJobCvController@matchScore')->name('apply-job.match-score');
        Route::get('apply-job/match-score/by-apply-id/{id}', 'ApplyJobCvController@matchScoreByApplyId')->name('apply-job.match-score.by-apply-id');
        Route::get('apply-job/create-match-score/{id}', 'ApplyJobCvController@createMatchScore')->name('apply-job.create-match-score');

        Route::get('secure-file/download', [App\Http\Controllers\FileDownloadController::class, 'download'])
            ->name('secure-file.download');
    }
);

// // Secure file download routes
// Route::middleware(['web', 'auth'])->group(function () {
// });
