# Workflow: <PERSON><PERSON><PERSON> bản Job - 27/01/2025

## <PERSON><PERSON> tả

Thêm tính năng nhân bản job trong trang danh sách job với các yêu cầu:

-   <PERSON><PERSON><PERSON> tra quyền "apply-job.create"
-   Thêm button "Nhân bản job"
-   Nhân bản job với toàn bộ thông tin trừ "created_by" sẽ là id của người nhân bản
-   Nhân bản cả dữ liệu trong các bảng liên quan

## Các thay đổi thực hiện

### 1. File: `app/Http/Controllers/Admin/JobCrudController.php`

#### Thêm Import

-   Thêm `use Illuminate\Support\Facades\DB;`
-   Thêm `use Prologue\Alerts\Facades\Alert;`

#### Thêm button nhân bản trong setupListOperation()

```php
// Thêm button nhân bản job
if (backpack_user()->can('apply-job.create')) {
    CRUD::addButtonFromModelFunction('line', 'duplicate_job', 'duplicateJobButton', 'beginning');
}
```

#### Thêm method duplicate()

-   Kiểm tra quyền "apply-job.create"
-   Sử dụng transaction để đảm bảo data integrity
-   Nhân bản job chính với replicate()
-   Cập nhật created_by thành id của người nhân bản
-   Thêm "(Bản sao)" vào title
-   Reset job_code để tự động tạo mới
-   Nhân bản tất cả relationship:
    -   languages (bảng job_languages)
    -   levels (bảng job_levels)
    -   skills (bảng job_skills)
    -   recTeam (bảng jobs_rec_team)
    -   recerJob (bảng jobs_recer)

### 2. File: `app/Models/Job.php`

#### Thêm method duplicateJobButton()

```php
public function duplicateJobButton($crud = false)
{
    return '<a class="btn btn-sm btn-link"
               href="' . backpack_url('job/' . $this->id . '/duplicate') . '"
               data-button-type="duplicate"
               title="Nhân bản job"
               onclick="return confirm(\'Bạn có chắc chắn muốn nhân bản job này không?\')">
               <i class="la la-copy"></i> Nhân bản
            </a>';
}
```

### 3. File: `routes/backpack/custom.php`

#### Thêm route cho duplicate

```php
Route::get('job/{id}/duplicate', 'JobCrudController@duplicate')->name('job.duplicate');
```

## Luồng hoạt động

1. **Hiển thị button**: Button chỉ hiển thị khi user có quyền "apply-job.create"
2. **Click button**: Hiển thị confirm dialog
3. **Xử lý duplicate**:
    - Kiểm tra quyền
    - Load job gốc với tất cả relationship
    - Bắt đầu transaction
    - Replicate job chính
    - Cập nhật thông tin cần thiết
    - Attach các relationship
    - Commit transaction
    - Redirect đến trang edit job mới
4. **Xử lý lỗi**: Rollback transaction và hiển thị thông báo lỗi

## Các bảng được nhân bản

1. **jobs** - Bảng chính
2. **job_languages** - Ngoại ngữ yêu cầu
3. **job_levels** - Level career yêu cầu
4. **job_skills** - Kỹ năng yêu cầu
5. **jobs_rec_team** - Team phụ trách recruit
6. **jobs_recer** - Recer phụ trách

## Lưu ý

-   Job mới sẽ có job_code được tự động tạo
-   Title sẽ có thêm "(Bản sao)"
-   created_by sẽ là id của người thực hiện nhân bản
-   Tất cả thông tin khác giữ nguyên từ job gốc
