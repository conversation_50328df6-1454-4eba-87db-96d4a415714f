<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>tie\Permission\Models\Permission;

class ReUpdateData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:re-update-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

    }

    public function addBackpackPermission() {
        # khong con su dung nua nhung cu de cho vui :))
        $permissions = Permission::where('name', 'like', '%backpack%')->get();
        $role = \Spatie\Permission\Models\Role::all();
        foreach ($role as $item) {
            $item->syncPermissions($permissions);
            $this->info($item->name . ' updated');
        }
    }
}
