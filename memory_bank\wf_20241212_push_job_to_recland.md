# Workflow: Push Job to Recland Feature
**Ng<PERSON>y tạo:** 12/12/2024  
**<PERSON><PERSON> tả:** Th<PERSON><PERSON> t<PERSON>h năng Push job to Recland với modal Vue.js component

## Tổng quan yêu cầu
1. <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> "Push job to Recland" vào trang chi tiết job
2. Tạo modal Vue.js component với các input:
   - select company_id
   - select user_id
   - select career_id
   - select skill_id
   - select level_id
   - select recruitment_type_id
   - input "bonus" (số tiền)
3. Tạo migration để thêm 2 trường "requirement" và "benefit" vào bảng jobs

## Các tác vụ đã thực hiện

### 1. Tạo Migration cho bảng jobs ✅
**File:** `database/migrations/2025_06_12_141754_add_requirement_and_benefit_to_jobs_table.php`
- Thêm cột `requirement` (text, nullable) sau cột `description`
- Thêm cột `benefit` (text, nullable) sau cột `requirement`
- <PERSON><PERSON> comment mô tả cho từng cột

### 2. Tạo Vue Component cho Modal ✅
**File:** `resources/js/components/Admin/Job/PushJobToReclandModal.vue`
- Component modal sử dụng Vuetify
- Các input fields theo yêu cầu:
  - Select company_id
  - Select user_id
  - Select career_id
  - Select skill_id
  - Select level_id
  - Select recruitment_type_id
  - Input bonus (number)
- Validation form
- Xử lý API calls cho việc load data và submit
- Events: close, success, error

### 3. Đăng ký Component trong app.js ✅
**File:** `resources/js/app.js`
- Import PushJobToReclandModal component
- Đăng ký component với tên 'push-job-to-recland-modal'

### 4. Thêm nút và modal vào trang chi tiết job ✅
**File:** `resources/views/admins/jobs/show.blade.php`
- Thêm nút "Push job to Recland" với class `btn btn-success`
- Thêm Vue app container với modal component
- Thêm JavaScript để khởi tạo Vue app và xử lý sự kiện click

## Cần thực hiện tiếp theo

### Backend API Routes & Controllers
Cần tạo các route và controller để:
1. Lấy dữ liệu cho các select box:
   - `/api/companies`
   - `/api/users`
   - `/api/careers`
   - `/api/skills`
   - `/api/levels`
   - `/api/recruitment-types`

2. API endpoint để push job:
   - `POST /api/push-job-to-recland`

### Chạy Migration
```bash
php artisan migrate
```

### Build Asset
```bash
npm run dev
# hoặc
npm run build
```

## Cấu trúc file đã tạo/sửa đổi
```
database/migrations/
├── 2025_06_12_141754_add_requirement_and_benefit_to_jobs_table.php

resources/js/
├── app.js (đã sửa)
└── components/Admin/Job/
    └── PushJobToReclandModal.vue (mới)

resources/views/admins/jobs/
└── show.blade.php (đã sửa)

memory_bank/
└── wf_20241212_push_job_to_recland.md (mới)
```

## Ghi chú kỹ thuật
- Sử dụng Vuetify cho UI components
- Axios để gọi API
- Noty.js để hiển thị thông báo
- Vue 3 composition API
- Laravel migration với after() để đặt cột đúng vị trí

## Testing checklist
- [ ] Migration chạy thành công
- [ ] Nút hiển thị đúng trên trang chi tiết job
- [ ] Modal mở được khi click nút
- [ ] Các select box load được dữ liệu
- [ ] Form validation hoạt động
- [ ] Submit form thành công
- [ ] Hiển thị thông báo success/error 