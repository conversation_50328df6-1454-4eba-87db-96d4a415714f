{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "backpack/crud": "^6.7", "backpack/permissionmanager": "^7.2", "backpack/pro": "2.0.11", "backpack/theme-tabler": "^1.2", "laravel/framework": "^11.9", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.28", "league/html-to-markdown": "^5.1", "livewire/livewire": "^3.5", "owen-it/laravel-auditing": "^13.6", "phpoffice/phpspreadsheet": "^2.2", "predis/predis": "^2.2", "yajra/laravel-datatables-editor": "^11.0", "yajra/laravel-datatables-export": "^11.1", "yajra/laravel-datatables-fractal": "^11.0", "yajra/laravel-datatables-html": "^11.4", "yajra/laravel-datatables-oracle": "^11.1", "zoha/laravel-meta": "^2.0"}, "require-dev": {"backpack/generators": "^4.0", "barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/function.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "post-install-cmd": ["@php artisan storage:link --quiet"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "repositories": [{"type": "composer", "url": "https://repo.backpackforlaravel.com/"}], "minimum-stability": "stable", "prefer-stable": true}