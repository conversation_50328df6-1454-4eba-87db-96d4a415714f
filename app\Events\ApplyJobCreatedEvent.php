<?php

namespace App\Events;

use App\Models\ApplyJob;
use App\Models\Task;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ApplyJobCreatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $apply_job;
    /**
     * Create a new event instance.
     */
    public function __construct(ApplyJob $apply_job)
    {
        $this->apply_job = $apply_job;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
