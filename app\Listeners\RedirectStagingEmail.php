<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSending;

class RedirectStagingEmail
{
    protected $no_emails = ['<EMAIL>'];


    public function handle(MessageSending $event)
    {
        // return true;
        // dd(config('app.debug_email'));
        if (app()->environment('local')) {
            $original = [];
            foreach (['Cc', 'Bcc'] as $type) {
                if ($address = $event->message->{'get' . $type}()) {
                    $original[$type] = $address;
                    $event->message->{strtolower($type)}(null);
                    // $event->message->{'set' . $type}(null);
                }
            }

            $event->message->to(config('app.debug_email'));

            $event->message->getHeaders()->addTextHeader(
                'X-Original-Emails',
                json_encode($original)
            );
        } else {
            // # check has email "<EMAIL>" in address then remove
            $original_all = [];

            foreach (['Cc', 'Bcc', 'To'] as $type) {
                if ($address = $event->message->{'get' . $type}()) {
                    if (count($address)) {
                        foreach ($address as $key => $value) {
                            if (!in_array($value->getAddress(), $this->no_emails)) {
                                $original_all[$value->getAddress()] = $value->getName();
                            }
                        }
                    }
                }
            }
            if (count($original_all) > 0) {
                $mail_index = 0;
                foreach ($original_all as $email => $name) {
                    if ($mail_index == 0) {
                        $event->message->to($email);
                    } elseif ($mail_index == 1) {
                        $event->message->cc($email);
                    } else {
                        $event->message->addCc($email);
                    }
                    $mail_index++;
                }
            }
        }
    }
}
