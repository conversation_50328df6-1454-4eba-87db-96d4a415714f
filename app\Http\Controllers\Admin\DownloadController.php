<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Models\ApplyJob;
use App\Models\Notification;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\FileAccessLog;
use App\Services\FileSecurityService;
use Illuminate\Support\Facades\Log;

class DownloadController extends Controller
{
    public function applyJobCvPrivate(Request $request)
    {
        $ip = $request->ip();
        $userId = auth()->id();
        $applyJobId = $request->id;

        try {
            // 1. Kiểm tra IP có bị block không
            if (FileSecurityService::isIpBlocked($ip)) {
                FileAccessLog::createLog([
                    'file_path' => 'BLOCKED_IP',
                    'model_type' => 'ApplyJob',
                    'record_id' => $applyJobId,
                    'field_name' => 'cv_private',
                    'hash_token' => 'N/A',
                    'is_successful' => false,
                    'error_message' => 'IP blocked due to suspicious activity'
                ]);

                return response()->json([
                    'error' => 'Truy cập bị từ chối do hoạt động đáng ngờ. Vui lòng liên hệ admin.'
                ], 403);
            }

            // 2. Kiểm tra rate limiting
            if (!FileSecurityService::checkRateLimit($ip)) {
                // Detect suspicious patterns
                $suspiciousPatterns = FileSecurityService::detectSuspiciousActivity($ip, $userId);

                // Block IP if suspicious activity detected
                if (!empty($suspiciousPatterns)) {
                    FileSecurityService::blockIpTemporarily($ip, 120); // Block for 2 hours
                }

                // Send alert email
                FileSecurityService::sendRateLimitAlert($ip, $userId, [
                    'endpoint' => 'download.cv-private.apply-job',
                    'apply_job_id' => $applyJobId,
                    'suspicious_patterns' => $suspiciousPatterns,
                    'user_agent' => $request->userAgent()
                ]);

                FileAccessLog::createLog([
                    'file_path' => 'RATE_LIMITED',
                    'model_type' => 'ApplyJob',
                    'record_id' => $applyJobId,
                    'field_name' => 'cv_private',
                    'hash_token' => 'N/A',
                    'is_successful' => false,
                    'error_message' => 'Rate limit exceeded: ' . $ip
                ]);

                return response()->json([
                    'error' => 'Quá nhiều yêu cầu download. Vui lòng thử lại sau 10 phút.'
                ], 429);
            }

            // 3. Lấy thông tin ApplyJob
            $apply = ApplyJob::findOrFail($applyJobId);

            // 4. Kiểm tra quyền truy cập (tương tự như trong FileDownloadController)
            $user = auth()->user();
            if ($user && method_exists($user, 'canShowDetailJob')) {
                if (!$user->canShowDetailJob($apply->job_id)) {
                    FileAccessLog::createLog([
                        'file_path' => 'PERMISSION_DENIED',
                        'model_type' => 'ApplyJob',
                        'record_id' => $applyJobId,
                        'field_name' => 'cv_private',
                        'hash_token' => 'N/A',
                        'is_successful' => false,
                        'error_message' => 'User does not have permission to access this job'
                    ]);

                    return response()->json([
                        'error' => 'Bạn không có quyền truy cập CV này.'
                    ], 403);
                }
            }

            $candidate_name = optional($apply->cv)->name;
            $job_title = $apply->job->title;

            // 5. Kiểm tra và ưu tiên sử dụng CV trong metadata nếu có
            $cv_private_path = $apply->getMeta('lasted_cv_private');
            $fieldName = 'lasted_cv_private';

            if (empty($cv_private_path)) {
                // Nếu không có trong metadata, sử dụng CV private từ model Cv
                if (!$apply->cv || empty($apply->cv->cv_private)) {
                    FileAccessLog::createLog([
                        'file_path' => 'CV_NOT_FOUND',
                        'model_type' => 'ApplyJob',
                        'record_id' => $applyJobId,
                        'field_name' => 'cv_private',
                        'hash_token' => 'N/A',
                        'is_successful' => false,
                        'error_message' => 'CV private file not found'
                    ]);

                    return redirect()->back()->with('error', 'CV not found');
                }
                $cv_private_path = $apply->cv->cv_private_org;
                $fieldName = 'cv_private';
            }

            // 6. Tạo file name
            $file_name = 'HRI - ' . Utils::cleanFileName(Utils::shortName($candidate_name)) . ' - ' . Utils::cleanFileName($job_title) . '.pdf';

            // 7. Tạo signed URL trực tiếp cho download
            if (str_starts_with($cv_private_path, 'http')) {
                $url = $cv_private_path;
            } else {
                // $url = \Storage::disk('s3')->temporaryUrl($cv_private_path, now()->addHour());
                $url = gen_url_file_s3($cv_private_path);
            }
            // dd($url);

            // 8. Download file content
            $client = new Client();
            $response = $client->get($url);
            $content = $response->getBody()->getContents();

            // 9. Log successful download
            FileAccessLog::createLog([
                'file_path' => $cv_private_path,
                'model_type' => 'ApplyJob',
                'record_id' => $applyJobId,
                'field_name' => $fieldName,
                'hash_token' => 'DIRECT_DOWNLOAD',
                'is_successful' => true,
                'error_message' => null
            ]);

            // 10. Detect suspicious activity after successful download
            $suspiciousPatterns = FileSecurityService::detectSuspiciousActivity($ip, $userId);
            if (!empty($suspiciousPatterns)) {
                Log::warning('Suspicious activity detected during CV download', [
                    'ip' => $ip,
                    'user_id' => $userId,
                    'apply_job_id' => $applyJobId,
                    'patterns' => $suspiciousPatterns
                ]);

                // Send alert but don't block the current request
                FileSecurityService::sendRateLimitAlert($ip, $userId, [
                    'endpoint' => 'download.cv-private.apply-job',
                    'apply_job_id' => $applyJobId,
                    'suspicious_patterns' => $suspiciousPatterns,
                    'user_agent' => $request->userAgent(),
                    'note' => 'Suspicious patterns detected after successful download'
                ]);
            }

            // 11. Trả về response với nội dung file để download
            return response($content)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $file_name . '"');
        } catch (\Exception $e) {
            // Log error
            FileAccessLog::createLog([
                'file_path' => $cv_private_path ?? 'UNKNOWN',
                'model_type' => 'ApplyJob',
                'record_id' => $applyJobId,
                'field_name' => $fieldName ?? 'cv_private',
                'hash_token' => 'ERROR',
                'is_successful' => false,
                'error_message' => $e->getMessage()
            ]);

            Log::error('Error in applyJobCvPrivate download', [
                'error' => $e->getMessage(),
                'apply_job_id' => $applyJobId,
                'ip' => $ip,
                'user_id' => $userId
            ]);

            return redirect()->back()->with('error', 'Có lỗi xảy ra khi tải CV. Vui lòng thử lại.');
        }
    }
}
