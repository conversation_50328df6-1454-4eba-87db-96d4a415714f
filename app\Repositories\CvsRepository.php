<?php

namespace App\Repositories;

use App\Models\Cv;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CvsRepository extends BaseRepository
{
    const MODEL = Cv::class;

    public function getLineChartWeek()
    {
        $data = $this->query()
            ->select(DB::raw('COUNT(id) as count'), DB::raw('DATE_FORMAT(created_at, "%v") as formatted_date'))
            ->where('created_at', '>=', '2024-01-01')
            ->groupBy(DB::raw('DATE_FORMAT(created_at, "%v")'))
            ->get();
        $result = [];
        foreach ($data as $item) {
            $year = date('Y');
            $start_date = date('d/m', strtotime($year . 'W' . $item->formatted_date));
            $end_date = date('d/m', strtotime($year . 'W' . $item->formatted_date) + 6 * 24 * 3600);
            $result[] = [
                'count' => $item->count,
                'formatted_date' => $start_date . ' - ' . $end_date,
            ];
        }
        return $result;

    }


}
