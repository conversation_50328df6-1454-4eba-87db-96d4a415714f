<template>
    <!--    <div class="row">-->
    <loading v-model:active="isLoading" :is-full-page="fullPage" />
    <v-stepper alt-labels>
        <v-stepper-header>
            <template v-for="(status, index) in listStatusTask">
                <v-stepper-item :color="updateStyle(status.id, index) ? 'info' : ''"
                    :complete="updateStyle(status.id, index)" :title="status.name" :value="index + 1"
                    @click="setStatus(status.id)" editable></v-stepper-item>
                <v-divider v-if="index < listStatusTask.length - 1"></v-divider>
            </template>
        </v-stepper-header>
    </v-stepper>
    <!--        <div class="col-12" v-if="listStatusTask.length > 0">-->
    <!--            <div class="s-stepper">-->
    <!--                <div :id="`stage-` + `${index + 1}` + `-step`" :class="updateStyle(status.id, index)"-->
    <!--                    @click="setStatus(status.id)" v-for="(status, index) in listStatusTask">-->
    <!--                    <span class="s-step-counter"></span>-->
    <!--                    <small>{{ status.name }}</small>-->
    <!--                </div>-->

    <!--            </div>-->
    <!--        </div>-->


    <!--    </div>-->
    <v-card class="mt-3">
        <v-card-text>
            <div class="tab-pane fade show active profile-overview" id="profile-overview" role="tabpanel">
                <div class="tab-pane fade show active profile-overview" id="profile-overview" role="tabpanel">
                    <div>
                        <!-- <h5 class="card-title ">Thông tin task</h5> -->
                        <h5 class="card-title mb-2">
                            <H2>{{ this.task.name }}</H2>
                        </h5>
                    </div>

                    <!-- <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label ">Tên task</div>
                        <div class="col-lg-9 col-md-8"><h4>{{ JSON.parse(this.task).name }}</h4></div>
                    </div> -->
                    <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label ">Ngày bắt đầu:</div>
                        <div class="col-lg-9 col-md-8">{{ moment(this.task.start_date).format('DD/MM/YYYY') }}</div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label">Người tạo task</div>
                        <div class="col-lg-9 col-md-8">{{ this.task.created_by_email }}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label">Người thực hiện:</div>
                        <div class="col-lg-9 col-md-8">{{ this.task.user_email }} ({{ this.task.user_name }})</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label">Người theo dõi:</div>
                        <div class="col-lg-9 col-md-8">
                            <span v-for="(user, index) in this.task.follower" :key="index">
                                <span v-if="index == 0">
                                    {{ user.email }} ({{ user.name }})
                                </span>
                                <span v-else>
                                    , {{ user.email }} ({{ user.name }})
                                </span>
                            </span>
                        </div>
                    </div>
                    <h3 class="card-title mt-4">Nội dung task</h3>
                    <p class="mt-2 fs-7" v-html="this.task.description"></p>
                    <v-btn :href="`/admin/task/` + this.task.id + `/edit`" class="float-right ma-10"
                        color="deep-purple-accent-2">
                        Edit
                        <span class="mdi mdi-pencil-circle text-h6"></span>
                    </v-btn>
                </div>
            </div>
        </v-card-text>
    </v-card>

    <v-card class="mt-3">
        <v-card-text>
            <div>
                <h3>Sub Task <span style="font-size: 12px; color: gray; font-weight: 400;">(Tích chọn để đánh dấu đã hoàn thành task)</span></h3>
                <div id="checklist-task">

                    <div v-for="(title, index) in task.subTasks_title" :key="index">
                        <input :id="'input-' + index" type="checkbox" :value="index " @change="taskActive(index)"
                            :checked="task.subTasks_active[index] === 0">
                        <label :for="'input-' + index">{{ title }}</label>
                    </div>

                </div>
            </div>
        </v-card-text>
    </v-card>

    <!--    <v-card class="mt-3">-->
    <div class="mt-3">
        <div class="col-12">
            <div class="row d-flex justify-content-center align-items-center h-100">
                <div class="col">
                    <v-form @reset="resetForm()" @submit="submitData">
                        <div class="card" id="list1"
                            style="border-radius: .75rem; background-color: #eff1f2; border: 0;">
                            <div class="">
                                <div class="pb-2">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex flex-row align-items-center">
                                                <v-textarea label="Nội dung cập nhật" variant="outlined"
                                                    class="form-control form-control-lg" id="content"
                                                    v-model="dataSubmit.content"></v-textarea>
                                                <a href="#!" data-mdb-toggle="tooltip" title="Set due date"><i
                                                        class="fas fa-calendar-alt fa-lg me-3"></i></a>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-end mr-10 mb-5">
                                            <v-btn type="submit" color="blue-lighten-1">Submit
                                                <span class="mdi mdi-send-circle text-h6"></span>
                                            </v-btn>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </v-form>
                </div>
            </div>
        </div>
    </div>
    <!--    </v-card>-->

    <v-card class="mt-3">
        <v-card-text>
            <b-card>
                <div class=" 2">
                    <h3 class="pb-3"> Lịch sử thay đổi</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <td>#</td>
                                <td>Tạo bởi</td>
                                <td>Nội dung</td>
                                <td>Thời gian tạo</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(log, index) in listLogTasks" :key="index">
                                <td>{{ index + 1 }}</td>
                                <td>{{ log.user_name }}</td>
                                <td v-html="log.content_html"></td>
                                <td>{{ log.created_at }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </b-card>
        </v-card-text>
    </v-card>
</template>

<script>

import moment from 'moment'
import axios from 'axios'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';
// import vSelect from "vue-select";
// import 'vue-select/dist/vue-select.css';
// import FlatPicker from 'vue-flatpickr-component';
// import 'flatpickr/dist/flatpickr.css';
// vSelect, Loading, FlatPicker

export default {
    name: "DetailTask",
    components: {Loading},
    props: {
        task_id: {
            required: true,
        },
        user: {
            required: true,
        },
    },
    data() {
        return {
            moment: moment,
            showModal: false,
            keyStatus: null,
            listStatusTask: [],
            listLogTasks: [],
            task: [],
            isLoading: true,
            fullPage: true,
            dataSubmit: {
                content: null,
                task_id: this.task_id,
            },
            error: {
                note: null,
            },
        }
    },
    mounted() {
        this.getListStatusTask();
        this.getLogTask();
    },
    methods: {
        getTaskDetail() {
            this.isLoading = true;
            axios.get('/api/task/' + this.task_id).then(res => {
                this.isLoading = false;
                this.task = res.data.data;
                this.setKeyActive(this.task.status_id);
            }).catch(err => {
                this.isLoading = false;
                alert('(getTaskDetail) Có lỗi xảy ra')
            });
        },
        async changeStatus(status) {
            this.isLoading = true;
            await axios.post('/api/change-status-task/' + this.task.id, {status: status}).then(res => {
                this.isLoading = false;
                this.getLogTask();
                new Noty({type: "success", text: res.data.success,}).show();
            }).catch(err => {
                alert('Có lỗi xảy ra ');
            });

        },
        getListStatusTask() {
            this.isLoading = true;
            axios.post('/api/status-parent/status-task').then(res => {
                if (res && res.data && res.data.data && res.data.data.length > 0) {
                    this.listStatusTask = res.data.data;
                    this.getTaskDetail();
                    this.isLoading = false;
                }
            }).catch(err => {
                this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        getLogTask() {
            this.isLoading = true;
            axios.get('/api/get-log-task/' + this.task_id).then(res => {
                this.listLogTasks = res.data.data;
                this.isLoading = false;
            }).catch(err => {
                // this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        resetForm() {
            this.showModal = false;
            this.dataSubmit = {
                content: null,
                task_id: this.task.id,
            };
            this.error = {content: null};
        },
        setKeyActive(status) {
            this.listStatusTask.forEach((item, index) => {
                if (item.id == status) {
                    this.keyStatus = index;
                }
            });
        },
        setStatus(status) {

            if (this.dataSubmit.status != status) {
                this.dataSubmit.status = status;
                this.changeStatus(status);
                this.setKeyActive(status);
            }
        },
        async submitData(e) {
            e.preventDefault();
            this.isLoading = true;
            await axios.post('/api/create-log-task', this.dataSubmit).then(res => {
                this.isLoading = false;
                this.getLogTask();
                this.resetForm();
                new Noty({type: "success", text: res.data.success,}).show();
            }).catch(err => {
                this.isLoading = false;
                if (err.response.data.errors.content) {
                    this.error.content = err.response.data.errors.content[0];
                } else {
                    alert('Có lỗi xảy ra ');
                }

            });

        },
        updateStyle(status, key) {
            let keyParent = this.keyStatus;
            if (key == keyParent || status <= this.status) {
                return 's-step active';
            }
            if (keyParent > key) {
                return 's-step mr-2-custom';
            }
            return false;
            return 's-step';

        },

        async taskActive(index) {
            const isCompleted = this.task.subTasks_active[index] === 0;
            const newActiveState = isCompleted ? 1 : 0;
            try {
                const response = await fetch('/api/change-subtask-active', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        subtask_id: this.task.subTasks_id[index],
                        active: newActiveState
                    })
                });

                if (response.ok) {
                    this.$set(this.task.subTasks_active, index, active);
                } else {
                    console.error('Failed to update subtask active state');
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

    }
}
</script>
<style  scoped>
.task-title {
    border-bottom: 2px solid #C2C2C2;
    padding-bottom: 8px;
    margin-bottom: 10px;
}

.label {
    font-weight: bold;
    font-size: 14px;
    color: #666666;
}

.fs-7 {
    font-size: 14px;
}

#list1 .form-control {
    border-color: transparent;
}

#list1 .form-control:focus {
    border-color: transparent;
    box-shadow: none;
}

#list1 .select-input.form-control[readonly]:not([disabled]) {
    background-color: #fbfbfb;
}

#checklist-task {
    --background: #ffffff;
    --text: #414856;
    --check: #4F29F0;
    --disabled: #C3C8DE;
    --height: 140px;
    --border-radius: 10px;
    background: var(--background);
    border-radius: var(--border-radius);
    position: relative;
    padding: 0px 30px;
    display: block;
    grid-template-columns: 30px auto;
    align-items: center;
    label {
        color: var(--text);
        position: relative;
        cursor: pointer;
        align-items: center;
        width: fit-content;
        transition: color .3s ease;
        &::before,
        &::after {
            content:"";
            position: absolute;
        }
        &::before {
            height: 3px;
            width: 15px;
            left: -27px;
            top: 14px;
            background: var(--check);
            border-radius: 2px;
            transition: background .3s ease;
        }
        &:after {
            height: 4px;
            width: 4px;
            top: 8px;
            left: -25px;
            border-radius: 50%;
        }
    }
    input[type="checkbox"] {
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        height: 15px;
        width: 15px;
        outline: none;
        border: 0;
        margin: 0 15px 0 0;
        cursor: pointer;
        background: var(--background);
        align-items: center;
        &::before,
        &::after {
            content:"";
            position: absolute;
            height: 2px;
            top: auto;
            background: var(--check);
            border-radius: 2px;
        }
        &::before {
            width: 0px;
            right: 60%;
            transform-origin: right bottom;
        }
        &::after {
            width: 0px;
            left: 40%;
            transform-origin: left bottom;
        }
        &:checked {
            &::before {
                animation: check-01 .4s ease forwards;
            }
            &::after {
                animation: check-02 .4s ease forwards;
            }
            + label {
                color: var(--disabled);
                animation: move .3s ease .1s forwards;
                &::before {
                    background: var(--disabled);
                    animation: slice .4s ease forwards;
                }
                &::after {
                    animation: firework .5s ease forwards .1s;
                }
            }
        }
    }
}

@keyframes move {
    50% {
        padding-left: 8px;
        padding-right: 0px;
    }
    100% {
        padding-right: 4px;
    }
}
@keyframes slice {
    60% {
        width: 100%;
        left: 4px;
    }
    100% {
        width: 100%;
        left: -2px;
        padding-left: 0;
    }
}
@keyframes check-01 {
    0% {
        width: 4px;
        top: auto;
        transform: rotate(0);
    }
    50% {
        width: 0px;
        top: auto;
        transform: rotate(0);
    }
    51% {
        width: 0px;
        top: 8px;
        transform: rotate(45deg);
    }
    100% {
        width: 5px;
        top: 8px;
        transform: rotate(45deg);
    }
}
@keyframes check-02 {
    0% {
        width: 4px;
        top: auto;
        transform: rotate(0);
    }
    50% {
        width: 0px;
        top: auto;
        transform: rotate(0);
    }
    51% {
        width: 0px;
        top: 8px;
        transform: rotate(-45deg);
    }
    100% {
        width: 10px;
        top: 8px;
        transform: rotate(-45deg);
    }
}
@keyframes firework {
    0% {
        opacity: 1;
        box-shadow: 0 0 0 -2px #4F29F0, 0 0 0 -2px #4F29F0, 0 0 0 -2px #4F29F0, 0 0 0 -2px #4F29F0, 0 0 0 -2px #4F29F0, 0 0 0 -2px #4F29F0;
    }
    30% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        box-shadow: 0 -15px 0 0px #4F29F0, 14px -8px 0 0px #4F29F0, 14px 8px 0 0px #4F29F0, 0 15px 0 0px #4F29F0, -14px 8px 0 0px #4F29F0, -14px -8px 0 0px #4F29F0;
    }
}


</style>

<style  scoped>
.flatpickr-wrapper {
    width: 100% !important;
}
</style>
