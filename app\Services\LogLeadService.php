<?php

namespace App\Services;

use App\Models\LogLead;

class LogLeadService
{
    public function queryList(array $filters = [])
    {
        return LogLead::query()
            ->when(!empty($filters['lead_id']), function ($query) use ($filters) {
                $query->where('lead_id', $filters['lead_id']);
            })
            ->latest('id');
    }

    public function create(array $data)
    {
        return LogLead::create($data);
    }

}
