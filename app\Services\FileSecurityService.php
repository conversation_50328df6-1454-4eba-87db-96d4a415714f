<?php

namespace App\Services;

use App\Models\FileAccessLog;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class FileSecurityService
{
    const RATE_LIMIT_MINUTES = 10;
    const RATE_LIMIT_MAX_REQUESTS = 100;
    const ALERT_EMAIL = '<EMAIL>';
    
    /**
     * Kiểm tra rate limit cho IP address
     */
    public static function checkRateLimit($ip, $minutes = self::RATE_LIMIT_MINUTES, $maxRequests = self::RATE_LIMIT_MAX_REQUESTS)
    {
        return FileAccessLog::checkRateLimit($ip, $minutes, $maxRequests);
    }
    
    /**
     * Kiểm tra rate limit cho user
     */
    public static function checkUserRateLimit($userId, $minutes = self::RATE_LIMIT_MINUTES, $maxRequests = self::RATE_LIMIT_MAX_REQUESTS)
    {
        if (!$userId) {
            return true;
        }
        
        $count = FileAccessLog::byUser($userId)
            ->inTimeRange($minutes)
            ->where('is_successful', true)
            ->count();
            
        return $count < $maxRequests;
    }
    
    /**
     * Gửi email cảnh báo khi vượt rate limit
     */
    public static function sendRateLimitAlert($ip, $userId = null, $additionalInfo = [])
    {
        try {
            // Kiểm tra xem đã gửi alert cho IP này trong 1 giờ qua chưa
            $cacheKey = "rate_limit_alert_{$ip}";
            if (Cache::has($cacheKey)) {
                return; // Đã gửi rồi, không gửi lại
            }
            
            // Set cache để tránh spam email
            Cache::put($cacheKey, true, now()->addHour());
            
            $subject = '[CRM Alert] Rate Limit Exceeded - Possible Bot Activity';
            $userInfo = $userId ? "User ID: {$userId}" : "Anonymous user";
            $timestamp = now()->format('Y-m-d H:i:s');
            
            $message = "
SECURITY ALERT: Rate limit exceeded

Details:
- IP Address: {$ip}
- {$userInfo}
- Time: {$timestamp}
- Limit: " . self::RATE_LIMIT_MAX_REQUESTS . " requests in " . self::RATE_LIMIT_MINUTES . " minutes

Recent activity summary:
" . self::getRecentActivitySummary($ip) . "

Additional Info:
" . json_encode($additionalInfo, JSON_PRETTY_PRINT) . "

Please investigate this activity for potential security threats.
            ";
            
            Mail::raw($message, function ($mail) use ($subject) {
                $mail->to(self::ALERT_EMAIL)
                     ->subject($subject);
            });
            
            Log::warning('Rate limit alert sent', [
                'ip' => $ip,
                'user_id' => $userId,
                'additional_info' => $additionalInfo
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send rate limit alert', [
                'error' => $e->getMessage(),
                'ip' => $ip,
                'user_id' => $userId
            ]);
        }
    }
    
    /**
     * Lấy tóm tắt hoạt động gần đây của IP
     */
    private static function getRecentActivitySummary($ip)
    {
        $logs = FileAccessLog::byIp($ip)
            ->inTimeRange(60) // 1 giờ qua
            ->orderBy('accessed_at', 'desc')
            ->limit(20)
            ->get(['model_type', 'record_id', 'field_name', 'is_successful', 'accessed_at']);
            
        if ($logs->isEmpty()) {
            return "No recent activity found.";
        }
        
        $summary = "Recent " . $logs->count() . " requests:\n";
        foreach ($logs as $log) {
            $status = $log->is_successful ? 'SUCCESS' : 'FAILED';
            $summary .= "- {$log->accessed_at}: {$log->model_type}#{$log->record_id}.{$log->field_name} [{$status}]\n";
        }
        
        return $summary;
    }
    
    /**
     * Kiểm tra suspicious activity patterns
     */
    public static function detectSuspiciousActivity($ip, $userId = null)
    {
        $suspiciousPatterns = [];
        
        // Pattern 1: Quá nhiều request thất bại
        $failedCount = FileAccessLog::byIp($ip)
            ->inTimeRange(30)
            ->where('is_successful', false)
            ->count();
            
        if ($failedCount > 20) {
            $suspiciousPatterns[] = "High failure rate: {$failedCount} failed requests in 30 minutes";
        }
        
        // Pattern 2: Truy cập nhiều model khác nhau trong thời gian ngắn
        $modelTypes = FileAccessLog::byIp($ip)
            ->inTimeRange(5)
            ->distinct('model_type')
            ->pluck('model_type')
            ->count();
            
        if ($modelTypes > 5) {
            $suspiciousPatterns[] = "Accessing {$modelTypes} different model types in 5 minutes";
        }
        
        // Pattern 3: Truy cập sequential IDs (có thể là enumeration attack)
        $recentIds = FileAccessLog::byIp($ip)
            ->inTimeRange(10)
            ->where('is_successful', true)
            ->orderBy('accessed_at')
            ->pluck('record_id')
            ->toArray();
            
        if (count($recentIds) > 10) {
            $isSequential = true;
            for ($i = 1; $i < count($recentIds); $i++) {
                if ($recentIds[$i] != $recentIds[$i-1] + 1) {
                    $isSequential = false;
                    break;
                }
            }
            
            if ($isSequential) {
                $suspiciousPatterns[] = "Sequential ID enumeration detected";
            }
        }
        
        return $suspiciousPatterns;
    }
    
    /**
     * Block IP tạm thời
     */
    public static function blockIpTemporarily($ip, $minutes = 60)
    {
        $cacheKey = "blocked_ip_{$ip}";
        Cache::put($cacheKey, true, now()->addMinutes($minutes));
        
        Log::warning("IP temporarily blocked", [
            'ip' => $ip,
            'duration_minutes' => $minutes
        ]);
    }
    
    /**
     * Kiểm tra IP có bị block không
     */
    public static function isIpBlocked($ip)
    {
        $cacheKey = "blocked_ip_{$ip}";
        return Cache::has($cacheKey);
    }
    
    /**
     * Generate secure hash token
     */
    public static function generateHash($id, $model, $field)
    {
        $data = $id . '|' . $model . '|' . $field . '|' . config('app.key');
        return hash('sha256', $data);
    }
    
    /**
     * Verify hash token
     */
    public static function verifyHash($id, $model, $field, $hash)
    {
        $expectedHash = self::generateHash($id, $model, $field);
        return hash_equals($expectedHash, $hash);
    }
}