<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;


class Candidate extends Model  implements AuditableContract
{
    use CrudTrait, HasFactory, Auditable;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'candidates';

    protected $guarded = ['id'];


    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->created_by)) {
                $model->created_by = backpack_auth()->id();
            }
        });
    }
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */


    public function applies()
    {
        return $this->hasMany(ApplyJob::class);
    }

    public function cvs()
    {
        return $this->hasMany(Cv::class);
    }

    public function raw_data(): MorphOne
    {
        return $this->morphOne(RawData::class, 'object');
    }
    public function attachment(): MorphMany
    {
        return $this->MorphMany(Attachment::class, 'object');
    }
    public function comment(): MorphMany
    {
        return $this->MorphMany(Comment::class, 'object');
    }
    public function created_by_user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function statusGender()
    {
        return $this->belongsTo(Status::class, 'gender');
    }
    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */
    public function scopeRoleData($query)
    {

        return $query->where(function ($q) {
            if (backpack_user()->can('candidate.all-data')) {
                return $q;
            } else {
                $userIds = [backpack_user()->id];
                if (backpack_user()->can('candidate.only-company')) {
                    $userIds = User::where('internal_company_id', backpack_user()->internal_company_id)->pluck('id')->toArray();
                } elseif (backpack_user()->can('candidate.only-team')) {
                    $userIds = User::where('department_id', backpack_user()->department_id)->pluck('id')->toArray();
                };
                return $q->whereIn('id', $userIds);
            }
        });
    }

    public function scopeOutSide($query)
    {
        $user = backpack_user();
        if (!$user->canOutsideAccess()) {
            return $query->where('created_by', $user->id);
        }
        return $query;
    }

    public function getNameEmailAttribute()
    {
        return $this->name . ' - ' . $this->email;
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public function getJiraInfo()
    {

        $first_cv = $this->cvs->first();
        if ($first_cv) {
            if ($first_cv->source_name == 'JIRA') {
                $jira = JiraCv::find($first_cv->source_id);
                return $jira;
            }
            # code...
        }
        return null;
    }
    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}