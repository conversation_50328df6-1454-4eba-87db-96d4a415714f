<template>
    <div class="meeting-calendar">
        <v-card>
            <v-card-title class="d-flex justify-space-between align-center">
                <span>Lịch phòng họp</span>
                <div class="d-flex align-center ga-2">
                    <v-progress-circular
                        v-if="!dataLoaded"
                        indeterminate
                        size="20"
                        width="2"
                        color="primary"
                    ></v-progress-circular>
                    <v-btn color="primary" @click="openCreateMeetingModal">
                        <v-icon left>mdi-plus</v-icon>
                        Đặt lịch mới
                    </v-btn>
                </div>
            </v-card-title>
            <v-card-text>
                <div v-if="!dataLoaded" class="text-center py-8">
                    <v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
                    <div class="mt-4 text-h6">Đang tải lịch họp...</div>
                </div>
                <CalendarView
                    v-else
                    ref="calendar"
                    :show-date="showDate"
                    :items="calendarItems"
                    :show-times="true"
                    :enable-drag-drop="false"
                    :time-format-options="{ hour: '2-digit', minute: '2-digit' }"
                    class="theme-default"
                    @click-item="showEvent"
                    @click-date="handleDateClick"
                >
                    <template #header="{ headerProps }">
                        <CalendarViewHeader :header-props="headerProps" @input="setShowDate" />
                    </template>
                </CalendarView>
            </v-card-text>
        </v-card>
        <!-- Event Detail Dialog -->
        <v-dialog v-model="selectedOpen" max-width="700" persistent>
            <v-card v-if="selectedEvent" class="meeting-detail-card">
                <!-- Header -->
                <v-card-title class="meeting-detail-header">
                    <div class="d-flex align-center w-100">
                        <v-icon color="primary" size="28" class="mr-3">mdi-calendar-account</v-icon>
                        <div class="flex-grow-1">
                            <h2 class="text-h5 font-weight-bold mb-1">{{ selectedEvent.name }}</h2>
                            <v-chip
                                :color="getStatusChipColor(selectedEvent.status)"
                                size="small"
                                variant="flat"
                                class="text-caption"
                            >
                                {{ getStatusText(selectedEvent.status) }}
                            </v-chip>
                        </div>
                        <v-btn
                            icon="mdi-close"
                            variant="text"
                            size="small"
                            @click="selectedOpen = false"
                        ></v-btn>
                    </div>
                </v-card-title>

                <v-divider></v-divider>

                <!-- Content -->
                <v-card-text class="pa-6">
                    <v-container class="pa-0">
                        <!-- Meeting Info Grid -->
                        <v-row class="mb-4">
                            <v-col cols="12" md="6">
                                <div class="info-item">
                                    <v-icon color="primary" class="info-icon">mdi-map-marker</v-icon>
                                    <div class="info-content">
                                        <div class="info-label">Phòng họp</div>
                                        <div class="info-value">{{ selectedEvent.room }}</div>
                                    </div>
                                </div>
                            </v-col>
                            <v-col cols="12" md="6">
                                <div class="info-item">
                                    <v-icon color="primary" class="info-icon">mdi-clock-outline</v-icon>
                                    <div class="info-content">
                                        <div class="info-label">Thời gian</div>
                                        <div class="info-value">
                                            {{ formatDateTime(selectedEvent.start) }}
                                            <br>
                                            <span class="text-caption text-medium-emphasis">đến {{ formatDateTime(selectedEvent.end) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </v-col>
                        </v-row>

                        <v-row class="mb-4">
                            <v-col cols="12" md="6">
                                <div class="info-item">
                                    <v-icon color="primary" class="info-icon">mdi-account-circle</v-icon>
                                    <div class="info-content">
                                        <div class="info-label">Người tạo</div>
                                        <div class="info-value">{{ selectedEvent.creator }}</div>
                                    </div>
                                </div>
                            </v-col>
                            <v-col cols="12" md="6">
                                <div class="info-item">
                                    <v-icon color="primary" class="info-icon">mdi-calendar-clock</v-icon>
                                    <div class="info-content">
                                        <div class="info-label">Thời lượng</div>
                                        <div class="info-value">{{ getMeetingDuration(selectedEvent.start, selectedEvent.end) }}</div>
                                    </div>
                                </div>
                            </v-col>
                        </v-row>

                        <!-- Description -->
                        <div v-if="selectedEvent.description" class="mb-4">
                            <div class="info-item">
                                <v-icon color="primary" class="info-icon">mdi-text-box-outline</v-icon>
                                <div class="info-content">
                                    <div class="info-label">Mô tả</div>
                                    <div class="info-value description-text">{{ selectedEvent.description }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Participants Section -->
                        <div class="participants-section">
                            <div class="section-header">
                                <v-icon color="primary" class="mr-2">mdi-account-group</v-icon>
                                <span class="text-h6 font-weight-medium">Người tham gia</span>
                                <v-chip size="small" variant="outlined" class="ml-2">
                                    {{ (selectedEvent.participants && selectedEvent.participants.length) || 0 }}
                                </v-chip>
                            </div>

                            <div v-if="selectedEvent.participants && selectedEvent.participants.length > 0" class="participants-list">
                                <v-row>
                                    <v-col
                                        v-for="participant in selectedEvent.participants"
                                        :key="participant.id || participant.user_id"
                                        cols="12"
                                        sm="6"
                                    >
                                        <v-card variant="outlined" class="participant-card">
                                            <v-card-text class="pa-3">
                                                <div class="d-flex align-center">
                                                    <v-avatar size="32" class="mr-3" color="primary">
                                                        <v-img
                                                            v-if="participant.avatar"
                                                            :src="participant.avatar"
                                                        ></v-img>
                                                        <span v-else class="text-caption text-white">
                                                            {{ getInitials(participant.name || 'N/A') }}
                                                        </span>
                                                    </v-avatar>
                                                    <div class="flex-grow-1">
                                                        <div class="text-body-2 font-weight-medium">
                                                            {{ participant.name || 'Không có tên' }}
                                                            <v-chip
                                                                v-if="participant.is_organizer"
                                                                size="x-small"
                                                                color="primary"
                                                                variant="flat"
                                                                class="ml-1"
                                                            >
                                                                Chủ trì
                                                            </v-chip>
                                                        </div>
                                                        <div class="text-caption text-medium-emphasis">
                                                            {{ participant.email || 'Không có email' }}
                                                        </div>
                                                        <v-chip
                                                            :color="getParticipantStatusColor(participant.status)"
                                                            size="x-small"
                                                            variant="flat"
                                                            class="mt-1"
                                                        >
                                                            {{ getParticipantStatusText(participant.status) }}
                                                        </v-chip>
                                                    </div>
                                                </div>
                                            </v-card-text>
                                        </v-card>
                                    </v-col>
                                </v-row>
                            </div>

                            <div v-else class="text-center py-4">
                                <v-icon size="48" color="grey-lighten-1">mdi-account-question</v-icon>
                                <div class="text-body-2 text-medium-emphasis mt-2">
                                    Chưa có thông tin người tham gia
                                </div>
                                <div class="text-caption text-medium-emphasis mt-1">
                                    Có thể do cuộc họp được tạo trước khi có chức năng quản lý người tham gia
                                </div>
                            </div>
                        </div>
                    </v-container>
                </v-card-text>

                <v-divider></v-divider>

                <!-- Actions -->
                <v-card-actions class="pa-4">
                    <v-btn
                        variant="outlined"
                        prepend-icon="mdi-link"
                        @click="copyMeetingLink(selectedEvent.id)"
                    >
                        Copy Link
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-btn
                        variant="text"
                        @click="selectedOpen = false"
                    >
                        Đóng
                    </v-btn>
                    <v-btn
                        color="primary"
                        variant="elevated"
                        prepend-icon="mdi-pencil"
                        :href="`/admin/meeting-booking/${selectedEvent.id}/edit`"
                    >
                        Chỉnh sửa
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Create Meeting Dialog -->
        <v-dialog v-model="createMeetingOpen" max-width="800" >
            <v-card>
                <v-card-title class="d-flex justify-space-between align-center">
                    <span class="text-h5">Tạo cuộc họp mới</span>
                    <v-btn icon="mdi-close" variant="text" size="small" @click="createMeetingOpen = false"></v-btn>
                </v-card-title>

                <v-divider style="margin: 0px;"></v-divider>

                <v-card-text class="">
                    <v-form ref="createMeetingForm" v-model="formValid" @submit.prevent="createMeeting">
                        <v-container>
                            <v-row>
                                <v-col cols="12" md="6">
                                    <v-text-field v-model="newMeeting.title" label="Tiêu đề cuộc họp" variant="outlined"
                                        density="comfortable" :rules="[v => !!v || 'Tiêu đề là bắt buộc']" required
                                        clearable></v-text-field>
                                </v-col>
                                <v-col cols="12" md="6">
                                    <v-select v-model="newMeeting.room_id" :items="meetingRooms" item-title="name"
                                        item-value="id" label="Phòng họp" variant="outlined" density="comfortable"
                                        :rules="[v => !!v || 'Phòng họp là bắt buộc']" required clearable>
                                        <template v-slot:item="{ props, item }">
                                            <v-list-item v-bind="props">
                                                <!-- <v-list-item-title>{{ item.raw.name }}</v-list-item-title> -->
                                                <v-list-item-subtitle v-if="item.raw.location">
                                                    {{ item.raw.location }} - Sức chứa: {{ item.raw.capacity }} người
                                                </v-list-item-subtitle>
                                            </v-list-item>
                                        </template>
                                    </v-select>
                                </v-col>
                            </v-row>

                            <v-row>
                                <v-col cols="12" md="6">
                                    <v-text-field v-model="newMeeting.date" label="Ngày" type="date" variant="outlined"
                                        density="comfortable" :rules="[v => !!v || 'Ngày là bắt buộc']"
                                        required></v-text-field>
                                </v-col>
                                <v-col cols="12" md="3">
                                    <v-text-field v-model="newMeeting.start_time" label="Giờ bắt đầu" type="time"
                                        variant="outlined" density="comfortable"
                                        :rules="[v => !!v || 'Giờ bắt đầu là bắt buộc']" required></v-text-field>
                                </v-col>
                                <v-col cols="12" md="3">
                                    <v-text-field v-model="newMeeting.end_time" label="Giờ kết thúc" type="time"
                                        variant="outlined" density="comfortable" :rules="[
                                            v => !!v || 'Giờ kết thúc là bắt buộc',
                                            v => !newMeeting.start_time || v > newMeeting.start_time || 'Giờ kết thúc phải sau giờ bắt đầu'
                                        ]" required></v-text-field>
                                </v-col>
                            </v-row>

                            <v-row>
                                <v-col cols="12">
                                    <v-textarea v-model="newMeeting.description" label="Mô tả (tùy chọn)"
                                        variant="outlined" density="comfortable" rows="3" auto-grow
                                        clearable></v-textarea>
                                </v-col>
                            </v-row>

                            <!-- Participants Section -->
                            <v-row>
                                <v-col cols="12">
                                    <div class="participants-form-section">
                                        <div class="section-header mb-3">
                                            <v-icon color="primary" class="mr-2">mdi-account-group</v-icon>
                                            <span class="text-h6 font-weight-medium">Người tham gia</span>
                                        </div>

                                        <v-autocomplete
                                            v-model="newMeeting.participants"
                                            :items="availableUsers"
                                            item-title="name"
                                            item-value="id"
                                            label="Chọn người tham gia"
                                            variant="outlined"
                                            density="comfortable"
                                            multiple
                                            chips
                                            closable-chips
                                            clearable
                                            :loading="loadingUsers"
                                            @update:search="searchUsers"
                                        >
                                            <template v-slot:chip="{ props, item }">
                                                <v-chip v-bind="props" size="small">
                                                    <v-avatar start size="20">
                                                        <v-img v-if="item.raw.avatar" :src="item.raw.avatar"></v-img>
                                                        <span v-else class="text-caption">{{ getInitials(item.raw.name) }}</span>
                                                    </v-avatar>
                                                    {{ item.raw.name }}
                                                </v-chip>
                                            </template>

                                            <template v-slot:item="{ props, item }">
                                                <v-list-item v-bind="props">
                                                    <!-- <template v-slot:prepend>
                                                        <v-avatar size="32">
                                                            <v-img v-if="item.raw.avatar" :src="item.raw.avatar"></v-img>
                                                            <span v-else class="text-caption">{{ getInitials(item.raw.name) }}</span>
                                                        </v-avatar>
                                                    </template> -->
                                                    <!-- <v-list-item-title>{{ item.raw.name }}</v-list-item-title> -->
                                                    <v-list-item-subtitle>{{ item.raw.email }}</v-list-item-subtitle>
                                                </v-list-item>
                                            </template>
                                        </v-autocomplete>

                                        <!-- External Participants -->
                                        <!-- <div class="mt-4">
                                            <v-text-field
                                                v-model="externalParticipantEmail"
                                                label="Thêm email người tham gia bên ngoài"
                                                variant="outlined"
                                                density="comfortable"
                                                type="email"
                                                append-inner-icon="mdi-plus"
                                                @click:append-inner="addExternalParticipant"
                                                @keyup.enter="addExternalParticipant"
                                                hint="Nhập email và nhấn Enter hoặc click nút +"
                                                persistent-hint
                                            ></v-text-field>
                                        </div> -->

                                        <!-- External Participants List -->
                                        <!-- <div v-if="newMeeting.externalParticipants.length > 0" class="mt-3">
                                            <div class="text-caption text-medium-emphasis mb-2">Người tham gia bên ngoài:</div>
                                            <div class="d-flex flex-wrap ga-2">
                                                <v-chip
                                                    v-for="(email, index) in newMeeting.externalParticipants"
                                                    :key="index"
                                                    size="small"
                                                    variant="outlined"
                                                    closable
                                                    @click:close="removeExternalParticipant(index)"
                                                >
                                                    <v-icon start size="16">mdi-email</v-icon>
                                                    {{ email }}
                                                </v-chip>
                                            </div>
                                        </div> -->
                                    </div>
                                </v-col>
                            </v-row>
                        </v-container>
                    </v-form>
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions class="pa-4">
                    <v-spacer></v-spacer>
                    <v-btn variant="text" @click="createMeetingOpen = false" :disabled="isCreating">
                        Hủy
                    </v-btn>
                    <v-btn color="primary" variant="elevated" :disabled="!formValid || isCreating" :loading="isCreating"
                        @click="createMeeting">
                        <v-icon start>mdi-calendar-plus</v-icon>
                        Tạo cuộc họp
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Copy Link Dialog -->
        <v-dialog v-model="copyLinkDialog.show" max-width="600">
            <v-card>
                <v-card-title>
                    <v-icon class="mr-2">mdi-link</v-icon>
                    Sao chép link cuộc họp
                </v-card-title>
                <v-card-text>
                    <p class="mb-4">Vui lòng copy link sau để chia sẻ cuộc họp:</p>
                    <v-text-field
                        ref="copyLinkInput"
                        v-model="copyLinkDialog.link"
                        readonly
                        variant="outlined"
                        density="comfortable"
                        append-inner-icon="mdi-content-copy"
                        @click:append-inner="copyFromDialog"
                        @focus="$event.target.select()"
                    ></v-text-field>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn variant="text" @click="copyLinkDialog.show = false">
                        Đóng
                    </v-btn>
                    <v-btn color="primary" @click="copyFromDialog">
                        <v-icon class="mr-1">mdi-content-copy</v-icon>
                        Copy
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Notification Snackbar -->
        <v-snackbar
            v-model="notification.show"
            :color="notification.color"
            :timeout="notification.timeout"
            location="top right"
        >
            {{ notification.message }}
            <template v-slot:actions>
                <v-btn
                    variant="text"
                    @click="notification.show = false"
                >
                    Đóng
                </v-btn>
            </template>
        </v-snackbar>
    </div>
</template>

<script>
import { CalendarView, CalendarViewHeader } from 'vue-simple-calendar'
import 'vue-simple-calendar/dist/vue-simple-calendar.css'
import 'vue-simple-calendar/dist/css/default.css'

export default {
    name: 'MeetingCalendar',
    components: {
        CalendarView,
        CalendarViewHeader,
    },
    data: () => ({
        showDate: new Date(),
        events: [],
        calendarItems: [],
        selectedEvent: null,
        selectedOpen: false,
        createMeetingOpen: false,
        formValid: false,
        isCreating: false,
        meetingRooms: [],
        availableUsers: [],
        loadingUsers: false,
        externalParticipantEmail: '',
        pendingMeetingId: null, // For deep linking
        dataLoaded: false, // Track if calendar data is loaded
        notification: {
            show: false,
            message: '',
            color: 'info',
            timeout: 4000
        },
        copyLinkDialog: {
            show: false,
            link: ''
        },
        newMeeting: {
            title: '',
            room_id: null,
            date: '',
            start_time: '09:00',
            end_time: '10:00',
            description: '',
            participants: [],
            externalParticipants: []
        }
    }),
    mounted() {
        // Check for deep linking parameter
        this.checkDeepLinkParameter();

        // Load data
        this.fetchEvents();
        this.fetchMeetingRooms();
        this.fetchUsers();
    },
    methods: {
        async fetchEvents() {
            try {
                const response = await axios.get('/api/admin/meeting-bookings/calendar');
                console.log('Raw API response:', response.data);

                // Debug participants data
                response.data.forEach(booking => {
                    console.log(`Meeting "${booking.title}" participants:`, booking.participants);
                });

                this.events = response.data.map(booking => ({
                    id: booking.id,
                    name: booking.title,
                    start: new Date(booking.start_time),
                    end: new Date(booking.end_time),
                    color: this.getStatusColor(booking.status),
                    room: booking.meeting_room.name,
                    description: booking.description,
                    creator: booking.creator.name,
                    status: booking.status,
                    participants: booking.participants ? booking.participants.map(p => ({
                        id: p.user_id || p.id,
                        name: p.user ? p.user.name : p.name,
                        email: p.user ? p.user.email : p.email,
                        status: p.status || 'pending',
                        is_organizer: p.is_organizer || false,
                        avatar: p.user ? p.user.avatar : null
                    })) : []
                }));

                console.log('Processed events:', this.events);

                // Mark data as loaded
                this.dataLoaded = true;

                // Handle deep linking if there's a pending meeting ID
                if (this.pendingMeetingId) {
                    this.$nextTick(() => {
                        this.openMeetingById(this.pendingMeetingId);
                        this.pendingMeetingId = null;
                    });
                }

                // Convert events to vue-simple-calendar format
                this.calendarItems = this.events.map(event => ({
                    id: event.id,
                    title: event.name,
                    startDate: event.start,
                    endDate: event.end,
                    classes: ['meeting-item'],
                    style: `background-color: ${event.color}; color: white;`,
                    room: event.room,
                    description: event.description,
                    creator: event.creator,
                    status: event.status,
                    originalEvent: event
                }));

                console.log('Calendar items for vue-simple-calendar:', this.calendarItems);
            } catch (error) {
                console.error('Error fetching events:', error);
            }
        },
        setShowDate(date) {
            this.showDate = date;
        },
        getStatusColor(status) {
            const colors = {
                pending: 'orange',
                approved: '#00897B',
                rejected: 'red',
                cancelled: 'grey'
            };
            return colors[status] || 'blue';
        },
        showEvent(calendarItem, windowEvent) {
            const open = () => {
                // Find the original event with all details
                const event = this.events.find(e => e.id === calendarItem.id);
                this.selectedEvent = event;
                this.selectedOpen = true;
                if (windowEvent) windowEvent.stopPropagation();
            };

            if (this.selectedOpen) {
                this.selectedOpen = false;
                setTimeout(open, 10);
            } else {
                open();
            }
        },
        handleDateClick(date) {
            console.log('Date clicked:', date);
            // Format date for input field (YYYY-MM-DD)
            const formattedDate = date.toISOString().split('T')[0];
            console.log('Formatted date:', formattedDate);

            this.newMeeting = {
                title: '',
                room_id: null,
                date: formattedDate,
                start_time: '09:00',
                end_time: '10:00',
                description: ''
            };

            console.log('Opening create meeting modal...');
            this.createMeetingOpen = true;
            console.log('createMeetingOpen:', this.createMeetingOpen);
        },
        openCreateMeetingModal() {
            console.log('openCreateMeetingModal called');
            this.resetMeetingForm();
            console.log('Setting createMeetingOpen to true');
            this.createMeetingOpen = true;
            console.log('createMeetingOpen:', this.createMeetingOpen);
        },
        createNewBooking() {
            window.location.href = '/admin/meeting-booking/create';
        },
        async fetchMeetingRooms() {
            try {
                console.log('Fetching meeting rooms from API...');
                const response = await axios.get('/api/admin/meeting-rooms');
                console.log('Meeting rooms response:', response.data);
                this.meetingRooms = response.data;
            } catch (error) {
                console.error('Error fetching meeting rooms:', error);
                // Fallback to mock data if API fails
                console.warn('Using mock data for meeting rooms');
                this.meetingRooms = [
                    { id: 1, name: 'Phòng họp A', location: 'Tầng 1', capacity: 10 },
                    { id: 2, name: 'Phòng họp B', location: 'Tầng 2', capacity: 8 },
                    { id: 3, name: 'Phòng họp C', location: 'Tầng 3', capacity: 15 },
                    { id: 4, name: 'Phòng hội thảo', location: 'Tầng 4', capacity: 50 },
                    { id: 5, name: 'Phòng họp VIP', location: 'Tầng 5', capacity: 6 }
                ];
            }
        },
        async createMeeting() {
            if (!this.formValid) {
                console.warn('Form is not valid');
                return;
            }

            this.isCreating = true;
            try {
                // Validate form manually
                const form = this.$refs.createMeetingForm;
                if (form) {
                    const isValid = await form.validate();
                    if (!isValid.valid) {
                        console.warn('Form validation failed');
                        this.isCreating = false;
                        return;
                    }
                }

                const meetingData = {
                    title: this.newMeeting.title.trim(),
                    meeting_room_id: this.newMeeting.room_id,
                    start_time: `${this.newMeeting.date} ${this.newMeeting.start_time}:00`,
                    end_time: `${this.newMeeting.date} ${this.newMeeting.end_time}:00`,
                    description: this.newMeeting.description ? this.newMeeting.description.trim() : '',
                    participants: this.newMeeting.participants,
                    external_participants: this.newMeeting.externalParticipants
                };

                console.log('Creating meeting with data:', meetingData);

                const response = await axios.post('/api/admin/meeting-bookings', meetingData);

                console.log('Meeting created successfully:', response.data);

                // Refresh events after creating
                await this.fetchEvents();

                // Close dialog and emit success event
                this.createMeetingOpen = false;
                this.$emit('meeting-created', response.data);

                // Reset form
                this.resetMeetingForm();

                // Show success message (you can replace with your notification system)
                alert('Cuộc họp đã được tạo thành công!');

            } catch (error) {
                console.error('Error creating meeting:', error);

                let errorMessage = 'Có lỗi xảy ra khi tạo cuộc họp';

                if (error.response) {
                    if (error.response.status === 422) {
                        // Validation errors
                        const errors = error.response.data.errors;
                        if (errors) {
                            errorMessage = Object.values(errors).flat().join('\n');
                        } else {
                            errorMessage = error.response.data.message || errorMessage;
                        }
                    } else if (error.response.status === 409) {
                        // Room not available
                        errorMessage = error.response.data.message || 'Phòng họp không có sẵn trong thời gian này';
                    } else {
                        errorMessage = error.response.data.message || errorMessage;
                    }
                }

                // Show error message (you can replace with your notification system)
                alert(errorMessage);

            } finally {
                this.isCreating = false;
            }
        },
        resetMeetingForm() {
            this.newMeeting = {
                title: '',
                room_id: null,
                date: new Date().toISOString().split('T')[0],
                start_time: '09:00',
                end_time: '10:00',
                description: '',
                participants: [],
                externalParticipants: []
            };

            this.externalParticipantEmail = '';

            // Reset form validation
            if (this.$refs.createMeetingForm) {
                this.$refs.createMeetingForm.reset();
                this.$refs.createMeetingForm.resetValidation();
            }
        },
        async fetchUsers() {
            try {
                this.loadingUsers = true;
                const response = await axios.get('/api/admin/users');
                this.availableUsers = response.data;
            } catch (error) {
                console.error('Error fetching users:', error);
                // Fallback to mock data
                this.availableUsers = [
                    { id: 1, name: 'Nguyễn Văn A', email: '<EMAIL>', avatar: null },
                    { id: 2, name: 'Trần Thị B', email: '<EMAIL>', avatar: null },
                    { id: 3, name: 'Lê Văn C', email: '<EMAIL>', avatar: null }
                ];
            } finally {
                this.loadingUsers = false;
            }
        },
        searchUsers(query) {
            // This can be enhanced to search users from API
            if (!query) return;
            // For now, we'll use the existing users list
        },
        addExternalParticipant() {
            const email = this.externalParticipantEmail.trim();
            if (email && this.isValidEmail(email)) {
                if (!this.newMeeting.externalParticipants.includes(email)) {
                    this.newMeeting.externalParticipants.push(email);
                    this.externalParticipantEmail = '';
                } else {
                    alert('Email này đã được thêm');
                }
            } else {
                alert('Vui lòng nhập email hợp lệ');
            }
        },
        removeExternalParticipant(index) {
            this.newMeeting.externalParticipants.splice(index, 1);
        },
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        getInitials(name) {
            if (!name) return '';
            return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
        },
        getStatusChipColor(status) {
            const colors = {
                pending: 'orange',
                approved: 'green',
                rejected: 'red',
                cancelled: 'grey'
            };
            return colors[status] || 'blue';
        },
        getStatusText(status) {
            const texts = {
                pending: 'Chờ duyệt',
                approved: 'Đã duyệt',
                rejected: 'Từ chối',
                cancelled: 'Đã hủy'
            };
            return texts[status] || 'Không xác định';
        },
        getParticipantStatusColor(status) {
            const colors = {
                invited: 'orange',
                accepted: 'green',
                declined: 'red',
                pending: 'grey'
            };
            return colors[status] || 'grey';
        },
        getParticipantStatusText(status) {
            const texts = {
                invited: 'Đã mời',
                accepted: 'Đã chấp nhận',
                declined: 'Từ chối',
                pending: 'Chờ phản hồi'
            };
            return texts[status] || 'Chờ phản hồi';
        },
        getMeetingDuration(start, end) {
            const startTime = new Date(start);
            const endTime = new Date(end);
            const diffMs = endTime - startTime;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            if (diffHours > 0) {
                return `${diffHours} giờ ${diffMinutes > 0 ? diffMinutes + ' phút' : ''}`;
            } else {
                return `${diffMinutes} phút`;
            }
        },
        /**
         * Check for deep linking parameter in URL
         */
        checkDeepLinkParameter() {
            const urlParams = new URLSearchParams(window.location.search);
            const meetingId = urlParams.get('id');

            if (meetingId) {
                console.log('Deep link detected for meeting ID:', meetingId);
                this.pendingMeetingId = parseInt(meetingId);

                // Remove the parameter from URL to clean it up
                const url = new URL(window.location);
                url.searchParams.delete('id');
                window.history.replaceState({}, document.title, url.pathname + url.search);
            }
        },
        /**
         * Open meeting modal by ID (for deep linking)
         */
        openMeetingById(meetingId) {
            console.log('Attempting to open meeting by ID:', meetingId);

            if (!this.dataLoaded) {
                console.log('Data not loaded yet, storing meeting ID for later');
                this.pendingMeetingId = meetingId;
                this.showNotification('Đang tải dữ liệu cuộc họp...', 'info');
                return;
            }

            // Find the meeting in events
            const meeting = this.events.find(event => event.id == meetingId);

            if (meeting) {
                console.log('Meeting found, opening modal:', meeting);
                this.selectedEvent = meeting;
                this.selectedOpen = true;

                // Optional: Highlight the meeting in calendar
                this.highlightMeeting(meeting);

                // Show success message
                this.showNotification('Đã mở chi tiết cuộc họp', 'success');
            } else {
                console.log('Meeting not found with ID:', meetingId);

                // Try to fetch the specific meeting from API if not found in current events
                this.fetchSpecificMeeting(meetingId);
            }
        },
        /**
         * Fetch a specific meeting by ID from API (fallback for deep linking)
         */
        async fetchSpecificMeeting(meetingId) {
            try {
                this.showNotification('Đang tìm kiếm cuộc họp...', 'info');

                const response = await axios.get(`/api/admin/meeting-bookings/${meetingId}`);
                const booking = response.data;

                // Process the meeting data similar to fetchEvents
                const meeting = {
                    id: booking.id,
                    name: booking.title,
                    start: new Date(booking.start_time),
                    end: new Date(booking.end_time),
                    color: this.getStatusColor(booking.status),
                    room: booking.meeting_room.name,
                    description: booking.description,
                    creator: booking.creator.name,
                    status: booking.status,
                    participants: booking.participants ? booking.participants.map(p => ({
                        id: p.user_id || p.id,
                        name: p.user ? p.user.name : p.name,
                        email: p.user ? p.user.email : p.email,
                        status: p.status || 'pending',
                        is_organizer: p.is_organizer || false,
                        avatar: p.user ? p.user.avatar : null
                    })) : []
                };

                // Open the meeting modal
                this.selectedEvent = meeting;
                this.selectedOpen = true;
                this.highlightMeeting(meeting);
                this.showNotification('Đã tìm thấy và mở cuộc họp', 'success');

            } catch (error) {
                console.error('Error fetching specific meeting:', error);

                if (error.response && error.response.status === 404) {
                    this.showNotification('Cuộc họp không tồn tại', 'error');
                } else if (error.response && error.response.status === 403) {
                    this.showNotification('Bạn không có quyền xem cuộc họp này', 'error');
                } else {
                    this.showNotification('Không thể tải thông tin cuộc họp', 'error');
                }
            }
        },
        /**
         * Highlight meeting in calendar (optional visual enhancement)
         */
        highlightMeeting(meeting) {
            // Navigate to the month containing the meeting
            if (meeting.start) {
                this.showDate = new Date(meeting.start);
            }
        },
        /**
         * Show notification to user
         */
        showNotification(message, type = 'info') {
            const colorMap = {
                'success': 'green',
                'error': 'red',
                'warning': 'orange',
                'info': 'blue'
            };

            this.notification = {
                show: true,
                message: message,
                color: colorMap[type] || 'info',
                timeout: type === 'error' ? 6000 : 4000
            };

            // Also log to console
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        },
        /**
         * Generate deep link URL for a meeting
         */
        generateMeetingLink(meetingId) {
            const baseUrl = window.location.origin + window.location.pathname;
            return `${baseUrl}?id=${meetingId}`;
        },
        /**
         * Copy meeting link to clipboard
         */
        async copyMeetingLink(meetingId) {
            const link = this.generateMeetingLink(meetingId);

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                try {
                    await navigator.clipboard.writeText(link);
                    this.showNotification('Đã sao chép link cuộc họp', 'success');
                    return;
                } catch (error) {
                    console.warn('Modern clipboard API failed, falling back to execCommand:', error);
                }
            }

            // Fallback to execCommand method
            try {
                // Create a temporary input element to copy the link
                const tempInput = document.createElement('input');
                tempInput.style.position = 'fixed';
                tempInput.style.left = '-999999px';
                tempInput.style.top = '-999999px';
                tempInput.value = link;
                document.body.appendChild(tempInput);

                // Select and copy the text
                tempInput.focus();
                tempInput.select();
                tempInput.setSelectionRange(0, 99999); // For mobile devices

                const successful = document.execCommand('copy');

                // Remove the temporary input
                document.body.removeChild(tempInput);

                if (successful) {
                    this.showNotification('Đã sao chép link cuộc họp', 'success');
                } else {
                    throw new Error('execCommand copy failed');
                }
            } catch (error) {
                console.error('Failed to copy link:', error);

                // Final fallback - show the link for manual copy
                this.showCopyLinkDialog(link);
            }
        },
        /**
         * Show dialog with link for manual copy (final fallback)
         */
        showCopyLinkDialog(link) {
            this.copyLinkDialog.link = link;
            this.copyLinkDialog.show = true;

            // Auto-focus and select the input when dialog opens
            this.$nextTick(() => {
                if (this.$refs.copyLinkInput) {
                    this.$refs.copyLinkInput.focus();
                    this.$refs.copyLinkInput.select();
                }
            });
        },
        /**
         * Copy link from dialog input
         */
        copyFromDialog() {
            try {
                const element = this.$refs.copyLinkInput.$el.querySelector('input');
                if (element) {
                    element.focus();
                    element.select();
                    element.setSelectionRange(0, 99999);

                    const successful = document.execCommand('copy');

                    if (successful) {
                        this.showNotification('Đã sao chép link cuộc họp', 'success');
                        this.copyLinkDialog.show = false;
                    } else {
                        this.showNotification('Vui lòng copy link thủ công', 'warning');
                    }
                } else {
                    this.showNotification('Vui lòng copy link thủ công', 'warning');
                }
            } catch (error) {
                console.error('Failed to copy from dialog:', error);
                this.showNotification('Vui lòng copy link thủ công', 'warning');
            }
        },

        formatDateTime(date) {
            const d = new Date(date);
            return d.toLocaleString('vi-VN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }
};
</script>

<style scoped>
.meeting-calendar {
    padding: 20px;
}

/* Custom styles for vue-simple-calendar */
.meeting-calendar :deep(.cv-wrapper) {
    font-family: inherit;
    height: 600px;
}

/* Header styling */
.meeting-calendar :deep(.cv-header) {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    padding: 15px;
    border-radius: 4px 4px 0 0;
}

/* Navigation buttons styling */
.meeting-calendar :deep(.cv-header-nav) {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.meeting-calendar :deep(.cv-header-nav button) {
    background: #1976d2 !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    min-width: 40px !important;
    height: 36px !important;
    transition: background-color 0.2s ease !important;
}

.meeting-calendar :deep(.cv-header-nav button:hover) {
    background: #1565c0 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.meeting-calendar :deep(.cv-header-nav button:disabled) {
    background: #ccc !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* Period label styling */
.meeting-calendar :deep(.periodLabel) {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Day cells styling */
.meeting-calendar :deep(.cv-day) {
    border: 1px solid #e0e0e0;
    min-height: 100px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease;
}

.meeting-calendar :deep(.cv-day:hover) {
    background-color: #f5f5f5;
}

.meeting-calendar :deep(.cv-day.today) {
    background-color: #e3f2fd;
}

.meeting-calendar :deep(.cv-day.outsideOfMonth) {
    background-color: #fafafa;
    color: #999;
}

/* Day numbers */
.meeting-calendar :deep(.cv-day-number) {
    font-weight: bold;
    padding: 4px 8px;
    font-size: 14px;
}

/* Calendar items styling */
.meeting-calendar :deep(.cv-item) {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
    margin-bottom: 1px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: opacity 0.2s ease;
}

.meeting-calendar :deep(.cv-item:hover) {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* Meeting item classes for different statuses */
.meeting-calendar :deep(.cv-item.meeting-item) {
    font-weight: 500;
}

/* Modal styling improvements */
.meeting-calendar :deep(.v-dialog .v-card) {
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.meeting-calendar :deep(.v-dialog .v-card-title) {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 16px 24px;
}

.meeting-calendar :deep(.v-dialog .v-card-text) {
    padding: 0;
}

.meeting-calendar :deep(.v-dialog .v-card-actions) {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Form field improvements */
.meeting-calendar :deep(.v-text-field .v-field) {
    border-radius: 6px;
}

.meeting-calendar :deep(.v-select .v-field) {
    border-radius: 6px;
}

.meeting-calendar :deep(.v-textarea .v-field) {
    border-radius: 6px;
}

/* Loading state */
.meeting-calendar :deep(.v-btn--loading) {
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .meeting-calendar :deep(.v-dialog) {
        margin: 16px;
    }

    .meeting-calendar :deep(.v-dialog .v-card) {
        max-width: calc(100vw - 32px) !important;
    }
}

/* Meeting Detail Dialog Styles */
.meeting-detail-card {
    border-radius: 12px !important;
    overflow: hidden;
}

.meeting-detail-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 20px 24px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}

.info-icon {
    margin-right: 12px;
    margin-top: 2px;
}

.info-content {
    flex: 1;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 4px;
}

.info-value {
    font-size: 1rem;
    color: #212529;
    line-height: 1.4;
}

.description-text {
    white-space: pre-wrap;
    word-break: break-word;
}

.participants-section {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.participants-list {
    margin-top: 16px;
}

.participant-card {
    transition: all 0.2s ease;
    border-radius: 8px;
}

.participant-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Participants Form Section */
.participants-form-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.participants-form-section .section-header {
    margin-bottom: 16px;
}

/* Custom chip styling */
.meeting-calendar :deep(.v-chip) {
    border-radius: 6px;
}

.meeting-calendar :deep(.v-chip--size-x-small) {
    font-size: 0.75rem;
    height: 20px;
}

/* Avatar improvements */
.meeting-calendar :deep(.v-avatar) {
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* List item improvements */
.meeting-calendar :deep(.v-list-item) {
    border-radius: 8px;
    margin-bottom: 4px;
}

.meeting-calendar :deep(.v-list-item:hover) {
    background-color: rgba(25, 118, 210, 0.04);
}
</style>

<style>
.meeting-calendar .theme-default .cv-item .startTime,
.meeting-calendar .theme-default .cv-item .endTime {
    color: #fff;
}
</style>