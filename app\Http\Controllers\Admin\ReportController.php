<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ReportService;
use App\Services\StatusService;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public function teamApply(Request $request)
    {
        // $token = backpack_auth()->user()->createToken('api-token')->plainTextToken;
        // $token = backpack_auth()->user()->currentAccessToken()->first()->plainTextToken;
        // $token = backpack_auth()->user()->tokens;
        // dd(backpack_auth()->user());
        // dd($token);
        // $token = rand(100000, 999999);
        // request()->cookie('item_name', $token, 3600);
        // $response = new \Illuminate\Http\Response(view('admins.report.report-team'));
        // $response->withCookie(cookie('test_cookie', $request->test_cookie, 45000));
        // return $response;
        return view('admins.report.report-team');
    }

    public function leadBySource(Request $request)
    {
        $start_date         = $request->get('start_date');
        $end_date           = $request->get('end_date');
        if (!$start_date) { 
            $start_date = date('Y-m-01');
            $start_date = date('Y-01-01');
        }
        if (!$end_date) {
            $end_date = date('Y-m-d');
        }
        $reportService     = new ReportService();
        $data              = $reportService->reportLeadBySource($start_date, $end_date);
        $data_by_source    = $data['data'];
        $status_of_month   = $data['status_of_month'];
        $max_week_of_month = $data['max_week_of_month'];
        $status_of_source  = $data['status_of_source'];
        $months             = $data['month'];
        $statusService     = new StatusService();
        $all_source            = $statusService->getStatusWithSlug('lead-source');
        $all_source['unknown'] = 'Trạng thái không xác định';
        $all_status            = $statusService->getStatusWithSlug('lead-status');
        $all_status['unknown'] = 'Trạng thái không xác định';
        return view('admins.report.report-lead-by-source', compact('data_by_source', 'max_week_of_month', 'start_date', 'end_date', 'data', 'status_of_month', 'status_of_source', 'months', 'all_status', 'all_source'));
    }
    public function leadByUser(Request $request)
    {
        $start_date         = $request->get('start_date');
        $end_date           = $request->get('end_date');
        if (!$start_date) { 
            $start_date = date('Y-m-01');
            $start_date = date('Y-01-01');
        }
        if (!$end_date) {
            $end_date = date('Y-m-d');
        }
        $reportService     = new ReportService();
        $data              = $reportService->reportLeadByUser($start_date, $end_date);
        // dd($data);
        $data_by_user          = $data['data'];
        $status_of_month       = $data['status_of_month'];
        $max_week_of_month     = $data['max_week_of_month'];
        $status_of_user        = $data['status_of_user'];
        $months                = $data['month'];
        $statusService         = new StatusService();
        $all_status            = $statusService->getStatusWithSlug('lead-status');
        $all_status['unknown'] = 'Trạng thái không xác định';
        $all_status['created'] = 'Số lead được tạo mới';
        $user_ids_raw          = array_keys($status_of_user);
        $all_user              = \App\Models\User::whereIn('id', $user_ids_raw)->get()->pluck('name', 'id')->toArray();
        $all_user['unknown']   = 'Trạng thái không xác định';
        return view('admins.report.report-lead-by-user', compact('data_by_user', 'all_user', 'max_week_of_month', 'start_date', 'end_date', 'data', 'status_of_month', 'status_of_user', 'months', 'all_status'));
    }
    public function applyBodyshop(Request $request)
    {
        $reportService = resolve(ReportService::class);
        $reportData = $reportService->applyBodyshop($request);
        return view('admins.report.job_application_report', compact('reportData'));
    }
}
