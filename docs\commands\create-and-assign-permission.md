# Create and Assign Permission Command

## <PERSON><PERSON> tả

Command `permission:create-and-assign` được sử dụng để tự động tạo một permission mới và assign permission đó vào toàn bộ role có trong hệ thống.

## Cú pháp

```bash
php artisan permission:create-and-assign {permission_name}
```

### Tham số

- `permission_name` (bắt buộc): Tên của permission cần tạo

## Chức năng

1. **Tạo Permission**: Tạo một permission mới với tên được chỉ định. Nếu permission đã tồn tại, command sẽ sử dụng permission hiện có.

2. **Assign vào tất cả Role**: Tự động assign permission vào tất cả các role có trong hệ thống.

3. **Kiểm tra trùng lặp**: Command sẽ kiểm tra và chỉ assign permission vào những role chưa có permission đó.

4. **Clear Cache**: Tự động clear permission cache sau khi hoàn thành.

## Ví dụ sử dụng

### Tạo permission mới

```bash
php artisan permission:create-and-assign "user.view-profile"
```

Output:
```
✓ Permission 'user.view-profile' created successfully.
Found 5 role(s). Assigning permission...
  ✓ Assigned to role: admin
  ✓ Assigned to role: manager
  ✓ Assigned to role: user
  ✓ Assigned to role: guest
  ✓ Assigned to role: super-admin
✓ Permission 'user.view-profile' assigned to 5 role(s) successfully.
✓ Permission cache cleared.
```

### Permission đã tồn tại

```bash
php artisan permission:create-and-assign "user.view-profile"
```

Output:
```
✓ Permission 'user.view-profile' already exists.
Found 5 role(s). Assigning permission...
  - Role 'admin' already has this permission
  - Role 'manager' already has this permission
  - Role 'user' already has this permission
  - Role 'guest' already has this permission
  - Role 'super-admin' already has this permission
✓ All roles already have the permission 'user.view-profile'.
✓ Permission cache cleared.
```

### Một số role đã có permission

```bash
php artisan permission:create-and-assign "user.edit-profile"
```

Output:
```
✓ Permission 'user.edit-profile' already exists.
Found 5 role(s). Assigning permission...
  - Role 'admin' already has this permission
  - Role 'super-admin' already has this permission
  ✓ Assigned to role: manager
  ✓ Assigned to role: user
  ✓ Assigned to role: guest
✓ Permission 'user.edit-profile' assigned to 3 role(s) successfully.
✓ Permission cache cleared.
```

## Trường hợp đặc biệt

### Không có role nào trong hệ thống

```bash
php artisan permission:create-and-assign "test.permission"
```

Output:
```
✓ Permission 'test.permission' created successfully.
No roles found in the system.
```

## Lưu ý

1. **Guard Name**: Command sử dụng guard name mặc định là `web`.

2. **Permission Cache**: Command tự động clear permission cache sau khi hoàn thành để đảm bảo các thay đổi được áp dụng ngay lập tức.

3. **Error Handling**: Command có xử lý lỗi và sẽ hiển thị thông báo lỗi chi tiết nếu có vấn đề xảy ra.

4. **Idempotent**: Command có thể chạy nhiều lần mà không gây ra lỗi hoặc duplicate data.

## Testing

Để test command, chạy:

```bash
php artisan test tests/Feature/CreateAndAssignPermissionCommandTest.php
```

## Các command liên quan

- `app:set-permision-to-role`: Assign permission cụ thể vào role cụ thể
- `create:permission`: Tạo permission từ config file
