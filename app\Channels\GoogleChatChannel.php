<?php

namespace App\Channels;

use Illuminate\Notifications\Notification;

class GoogleChatChannel
{
    /**
     * <PERSON><PERSON><PERSON> thông báo qua channel Google Chat.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return array|null
     */
    public function send($notifiable, Notification $notification)
    {
        if (method_exists($notification, 'toGoogleChat')) {
            return $notification->toGoogleChat($notifiable);
        }

        return null;
    }
}
