<?php

namespace App\Console\Commands;

use App\Models\Job;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CreatePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:permission';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        foreach (config('role-permission-list') as $permission) {

            \Spatie\Permission\Models\Permission::updateOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);
        }

        $role = \Spatie\Permission\Models\Role::updateOrCreate([
            'name' => 'super-admin',
            'guard_name' => 'web'
        ]);

        $role->syncPermissions( \Spatie\Permission\Models\Permission::all());
        print 'success';
    }
}
