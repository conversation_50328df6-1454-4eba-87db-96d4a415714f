<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CvProcessed extends Model
{
    protected $table = 'cv_processed';
    protected $fillable = [
        'cv_id',
        'public_path',
        'private_path',
        'created_file_private',
        'base_file_name',
        'raw_data',
        'push_to_recland'
    ];

    protected $casts = [
        'push_to_recland' => 'boolean',
        // 'raw_data' => 'json',
        // 'created_at' => 'datetime',
        // 'updated_at' => 'datetime'
    ];

    public function cv()
    {
        return $this->belongsTo(Cv::class, 'cv_id', 'id');
    }
} 