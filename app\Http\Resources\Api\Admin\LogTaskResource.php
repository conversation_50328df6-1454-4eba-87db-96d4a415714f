<?php

namespace App\Http\Resources\Api\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LogTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $content_html = $this->content;
        $content_html = str_replace("\n", "<br>", $content_html);
        $content_html = str_replace("\r", "<br>", $content_html);
        // Tìm và thay thế URL bằng thẻ <a>
        $pattern = '/(https?:\/\/[^\s<]+)/';
        $content_html = preg_replace($pattern, '<a href="$1" target="_blank">$1</a>', $content_html);
        return [
            'user_name' => optional($this->user)->name,
            'content' => $this->content,
            'content_html' => $content_html,
            'created_at' => Carbon::parse($this->created_at)->format('d-m-Y H:i:s')
        ];
    }
}
