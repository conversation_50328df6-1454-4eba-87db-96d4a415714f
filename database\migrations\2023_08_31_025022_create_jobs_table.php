<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->tinyInteger('priority');
            $table->integer('type')->comment('Loai Job');
            $table->tinyInteger('vacancy')->comment('So luong');
            $table->text('description')->nullable();
            $table->integer('user_id')->comment('Nguoi phu trach');
            $table->integer('status');
            $table->integer('created_by');
            $table->integer('company_id');
            $table->string('note')->nullable();
            $table->string('path')->nullable();
            $table->string('address')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jobs');
    }

};
