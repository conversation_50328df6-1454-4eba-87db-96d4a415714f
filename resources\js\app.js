import './bootstrap';
// import "bootstrap";

import { createApp } from 'vue';
// import BootstrapVue3 from "bootstrap-vue-next";
import StatusApplyJob from './components/Admin/ApplyJob/StatusApplyJob.vue';

import StatusLead from "@/components/Admin/Lead/StatusLead.vue";
import DetailTask from "@/components/Admin/Task/DetailTask.vue";
// import PushJobToReclandModal from "@/components/Admin/Job/PushJobToReclandModal.vue";
// import { default as DetailTask } from "@/components/Admin/Task/DetailTask.vue";
// const DetailTask = () => import(/* webpackChunkName: "unique-chunk-name" */ '@/components/Admin/Task/DetailTask.vue')


// import "bootstrap/dist/css/bootstrap.css";
// import "bootstrap-vue-next/dist/bootstrap-vue-next.css";
import Home from "@/components/Admin/DashBoard/Home.vue";
import ReportTeam from "@/components/Admin/Report/ReportTeam.vue";
import MeetingCalendar from "@/components/Admin/Meeting/MeetingCalendar.vue";
// import { Quasar } from 'quasar'
// import quasarLang from 'quasar/lang/vi'

// // Import icon libraries
// import '@quasar/extras/material-icons/material-icons.css'

// // Import Quasar css
// import 'quasar/dist/quasar.css'

// Vuetify
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'
// import { VCalendar } from 'vuetify/labs/VCalendar'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

const vuetify = createVuetify({
    components,
    directives
})

const app = createApp({
    components: {
        'status-apply-job': StatusApplyJob,
        'status-lead': StatusLead,
        'detail-task': DetailTask,
        'dashboard': Home,
        'report-team': ReportTeam,
        'meeting-calendar': MeetingCalendar,
        // 'push-job-to-recland-modal': PushJobToReclandModal,
    }
});
app.use(vuetify);
// app.use(BootstrapVue3);
// app.use(Quasar, {
//     plugins: {}, // import Quasar plugins and add here
//     lang: quasarLang,
// });
app.mount('#app');
