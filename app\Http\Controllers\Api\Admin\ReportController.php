<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Admin\StatusResource;
use App\Services\ReportService;
use App\Services\StatusService;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    protected $service;

    public function __construct(StatusService $service)
    {
        $this->service = $service;
    }

    public function totalApplyStatusByMonth(Request $request)
    {
        // $token = $request->bearerToken();
        // if (!$token) {
        //     return response()->json(['message' => 'Unauthorized'], 401);
        // }
        // $token = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
        // $user = $token->tokenable;
        // $user = $request->user();
        // dd($user);
        $reportService = resolve(ReportService::class);
        $data = $reportService->totalApplyStatusByMonth($request);
        return response()->json(['data' => $data]);
    }

    public function totalRecerApplyStatusByMonth(Request $request)
    {
        $reportService = resolve(ReportService::class);
        $data = $reportService->totalRecerApplyStatusByMonth($request);
        return response()->json(['data' => $data]);
    }

}
