@if (true)
    @if (backpack_user()->hasAnyPermission(['task.index', 'lead.index', 'company.index', 'job.index']))
        <x-backpack::menu-dropdown title="Công việc" icon="la la-tasks">
            @if (backpack_user()->can('task.index'))
                <x-backpack::menu-dropdown-item title="Tasks" icon="la la-tasks" :link="backpack_url('task')" />
            @endif
            @if (backpack_user()->can('lead.index'))
                <x-backpack::menu-dropdown-item title="Leads" icon="la la-phone-alt" :link="backpack_url('lead')" />
            @endif
            @if (backpack_user()->can('company.index'))
                <x-backpack::menu-dropdown-item title="Khách hàng" icon="la la-user-circle" :link="backpack_url('company')" />
            @endif
            @if (backpack_user()->can('job.index'))
                <x-backpack::menu-dropdown-item title="Jobs" icon="la la-briefcase" :link="backpack_url('job')" />
            @endif
        </x-backpack::menu-dropdown>
    @endif

    {{-- <x-backpack::menu-dropdown title="Khách hàng" icon="la la-landmark"> --}}
    {{-- <x-backpack::menu-dropdown-header /> --}}
    {{-- </x-backpack::menu-dropdown> --}}
    @if (backpack_user()->hasAnyPermission(['apply-job.index', 'candidate.index', 'cv.index']))
        <x-backpack::menu-dropdown title="Quản lý ứng viên" icon="la la-warehouse">
            @if (backpack_user()->can('apply-job.index'))
                <x-backpack::menu-dropdown-item title="Apply jobs" icon="la la-question" :link="backpack_url('apply-job')" />
            @endif
            @if (backpack_user()->can('candidate.index'))
                <x-backpack::menu-dropdown-item title="Ứng viên" icon="la la-question" :link="backpack_url('candidate')" />
            @endif
            @if (backpack_user()->can('cv.index'))
                <x-backpack::menu-dropdown-item title="CV ứng viên" icon="la la-question" :link="backpack_url('cv')" />
            @endif
        </x-backpack::menu-dropdown>
    @endif

    @if (backpack_user()->hasAnyPermission([
            'status.index',
            'academic-level.index',
            'career-language.index',
            'career-level.index',
            'skill.index',
        ]))
        <x-backpack::menu-dropdown title="Master" icon="la la-fire">
            {{-- <x-backpack::menu-dropdown-header  /> --}}
            @if (backpack_user()->can('status.index'))
                <x-backpack::menu-dropdown-item title="Statuses" icon="la la-question" :link="backpack_url('status')" />
            @endif
            @if (backpack_user()->can('academic-level.index'))
                <x-backpack::menu-dropdown-item title="Academic levels" icon="la la-question" :link="backpack_url('academic-level')" />
            @endif
            @if (backpack_user()->can('career-language.index'))
                <x-backpack::menu-dropdown-item title="Career languages" icon="la la-question" :link="backpack_url('career-language')" />
            @endif
            @if (backpack_user()->can('career-level.index'))
                <x-backpack::menu-dropdown-item title="Career levels" icon="la la-question" :link="backpack_url('career-level')" />
            @endif
            @if (backpack_user()->can('skill.index'))
                <x-backpack::menu-dropdown-item title="Skills" icon="la la-question" :link="backpack_url('skill')" />
            @endif
        </x-backpack::menu-dropdown>
    @endif

    @if (backpack_user()->hasAnyPermission([
            'internal-company.index',
            'department.index',
            'user.index',
            'role.index',
            'permission.index',
        ]))
        <x-backpack::menu-dropdown title="Nội bộ" icon="la la-puzzle-piece" x-if="false">
            {{-- <x-backpack::menu-dropdown-header title="" /> --}}
            @if (backpack_user()->can('internal-company.index'))
                <x-backpack::menu-dropdown-item title="Công ty" icon="la la-building" :link="backpack_url('internal-company')" />
            @endif
            @if (backpack_user()->can('department.index'))
                <x-backpack::menu-dropdown-item title="Phòng ban" icon="la la-object-ungroup" :link="backpack_url('department')" />
            @endif
            @if (backpack_user()->can('user.index'))
                <x-backpack::menu-dropdown-item title="Người dùng" icon="la la-user" :link="backpack_url('user')" />
            @endif
            @if (backpack_user()->can('role.index'))
                <x-backpack::menu-dropdown-item title="Vai trò" icon="la la-group" :link="backpack_url('role')" />
            @endif
            @if (backpack_user()->can('permission.index'))
                <x-backpack::menu-dropdown-item title="Quản lý quyền" icon="la la-key" :link="backpack_url('permission')" />
            @endif
        </x-backpack::menu-dropdown>
    @endif

    @if (backpack_user()->hasAnyPermission(['report.index', 'report.only-team']))
        <x-backpack::menu-dropdown title="Thống kê" icon="la la-bar-chart" x-if="false">
            @if (backpack_user()->can('report.only-team'))
                <x-backpack::menu-dropdown-item title="Thống kê Apply theo team" icon="la la-pie-chart"
                    :link="route('report.only-team')" />
            @endif
            @if (backpack_user()->can('report.lead'))
                <x-backpack::menu-dropdown-item title="Thống kê lead theo Nguồn" icon="la la-pie-chart"
                    :link="route('report.lead-by-source')" />
                <x-backpack::menu-dropdown-item title="Thống kê lead theo người chăm" icon="la la-pie-chart"
                    :link="route('report.lead-by-user')" />
            @endif
        </x-backpack::menu-dropdown>
    @endif
    @if (backpack_user()->can('statistical.change-status-lead'))
        <x-backpack::menu-dropdown-item title="Thống kê lead theo trạng thái" icon="la la-pie-chart"
            :link="route('statistical.change-status-lead')" />
    @endif

    {{-- Meeting Management --}}
    <x-backpack::menu-dropdown title="Quản lý phòng họp" icon="la la-calendar">
        <x-backpack::menu-dropdown-item title="Lịch phòng họp" icon="la la-calendar-check" :link="route('meeting.calendar')" />
        <x-backpack::menu-dropdown-item title="Đặt lịch họp" icon="la la-calendar-plus" :link="backpack_url('meeting-booking')" />
        @if (backpack_user()->can('meeting-room.manager'))
        <x-backpack::menu-dropdown-item title="Phòng họp" icon="la la-door-open" :link="backpack_url('meeting-room')" />
        @endif
    </x-backpack::menu-dropdown>
@endif
