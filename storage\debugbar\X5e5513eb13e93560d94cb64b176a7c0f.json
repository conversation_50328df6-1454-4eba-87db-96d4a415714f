{"__meta": {"id": "X5e5513eb13e93560d94cb64b176a7c0f", "datetime": "2025-08-05 17:32:27", "utime": 1754389947.413136, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754389944.618447, "end": 1754389947.413165, "duration": 2.794718027114868, "duration_str": "2.79s", "measures": [{"label": "Booting", "start": 1754389944.618447, "relative_start": 0, "end": 1754389944.911646, "relative_end": 1754389944.911646, "duration": 0.293198823928833, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754389944.911656, "relative_start": 0.2932088375091553, "end": 1754389947.413168, "relative_end": 2.86102294921875e-06, "duration": 2.501512050628662, "duration_str": "2.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 37695560, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 104, "templates": [{"name": "1x admins.home.index", "param_count": null, "params": [], "start": **********.344548, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admins.home.index"}, {"name": "1x backpack.theme-tabler::blank", "param_count": null, "params": [], "start": **********.367991, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/blank.blade.phpbackpack.theme-tabler::blank", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::blank"}, {"name": "4x backpack.theme-tabler::inc.widgets", "param_count": null, "params": [], "start": **********.368906, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/widgets.blade.phpbackpack.theme-tabler::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack.theme-tabler::inc.widgets"}, {"name": "1x backpack.theme-tabler::layouts.horizontal_dark", "param_count": null, "params": [], "start": **********.370669, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/horizontal_dark.blade.phpbackpack.theme-tabler::layouts.horizontal_dark", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fhorizontal_dark.blade.php&line=1", "ajax": false, "filename": "horizontal_dark.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.horizontal_dark"}, {"name": "1x backpack.theme-tabler::inc.head", "param_count": null, "params": [], "start": **********.371524, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/head.blade.phpbackpack.theme-tabler::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.head"}, {"name": "1x backpack.theme-tabler::inc.theme_styles", "param_count": null, "params": [], "start": **********.372664, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_styles.blade.phpbackpack.theme-tabler::inc.theme_styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_styles.blade.php&line=1", "ajax": false, "filename": "theme_styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_styles"}, {"name": "8x __components::723fe3f0e44fe2b3529303522562360e", "param_count": null, "params": [], "start": **********.376719, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/723fe3f0e44fe2b3529303522562360e.blade.php__components::723fe3f0e44fe2b3529303522562360e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F723fe3f0e44fe2b3529303522562360e.blade.php&line=1", "ajax": false, "filename": "723fe3f0e44fe2b3529303522562360e.blade.php", "line": "?"}, "render_count": 8, "name_original": "__components::723fe3f0e44fe2b3529303522562360e"}, {"name": "1x backpack.ui::inc.styles", "param_count": null, "params": [], "start": **********.468327, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/styles.blade.phpbackpack.ui::inc.styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.styles"}, {"name": "1x backpack.theme-tabler::layouts.partials.light_dark_mode_logic", "param_count": null, "params": [], "start": **********.477232, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/light_dark_mode_logic.blade.phpbackpack.theme-tabler::layouts.partials.light_dark_mode_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Flight_dark_mode_logic.blade.php&line=1", "ajax": false, "filename": "light_dark_mode_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.light_dark_mode_logic"}, {"name": "1x backpack.theme-tabler::layouts._horizontal_dark.menu_container", "param_count": null, "params": [], "start": **********.478408, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal_dark/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal_dark.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal_dark%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal_dark.menu_container"}, {"name": "1x backpack.theme-tabler::layouts._horizontal.menu_container", "param_count": null, "params": [], "start": **********.47929, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal.menu_container"}, {"name": "2x backpack.theme-tabler::inc.sidebar_content", "param_count": null, "params": [], "start": **********.480357, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/sidebar_content.blade.phpbackpack.theme-tabler::inc.sidebar_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fsidebar_content.blade.php&line=1", "ajax": false, "filename": "sidebar_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.sidebar_content"}, {"name": "2x backpack.ui::inc.menu_items", "param_count": null, "params": [], "start": **********.481918, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/vendor/backpack/ui/inc/menu_items.blade.phpbackpack.ui::inc.menu_items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fui%2Finc%2Fmenu_items.blade.php&line=1", "ajax": false, "filename": "menu_items.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.ui::inc.menu_items"}, {"name": "44x backpack.theme-tabler::components.menu-dropdown-item", "param_count": null, "params": [], "start": 1754389946.214203, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown-item.blade.phpbackpack.theme-tabler::components.menu-dropdown-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown-item.blade.php&line=1", "ajax": false, "filename": "menu-dropdown-item.blade.php", "line": "?"}, "render_count": 44, "name_original": "backpack.theme-tabler::components.menu-dropdown-item"}, {"name": "12x backpack.theme-tabler::components.menu-dropdown", "param_count": null, "params": [], "start": 1754389946.223066, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown.blade.phpbackpack.theme-tabler::components.menu-dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown.blade.php&line=1", "ajax": false, "filename": "menu-dropdown.blade.php", "line": "?"}, "render_count": 12, "name_original": "backpack.theme-tabler::components.menu-dropdown"}, {"name": "1x backpack.theme-tabler::inc.menu", "param_count": null, "params": [], "start": 1754389946.327757, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu.blade.phpbackpack.theme-tabler::inc.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu"}, {"name": "2x backpack.theme-tabler::inc.topbar_left_content", "param_count": null, "params": [], "start": 1754389946.329667, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_left_content.blade.phpbackpack.theme-tabler::inc.topbar_left_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_left_content.blade.php&line=1", "ajax": false, "filename": "topbar_left_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_left_content"}, {"name": "2x backpack.theme-tabler::layouts.partials.switch_theme", "param_count": null, "params": [], "start": 1754389946.331133, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/switch_theme.blade.phpbackpack.theme-tabler::layouts.partials.switch_theme", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fswitch_theme.blade.php&line=1", "ajax": false, "filename": "switch_theme.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::layouts.partials.switch_theme"}, {"name": "1x backpack.theme-tabler::inc.menu_notification_dropdown", "param_count": null, "params": [], "start": 1754389946.34379, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu_notification_dropdown.blade.phpbackpack.theme-tabler::inc.menu_notification_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu_notification_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_notification_dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu_notification_dropdown"}, {"name": "2x backpack.theme-tabler::inc.topbar_right_content", "param_count": null, "params": [], "start": 1754389947.233657, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_right_content.blade.phpbackpack.theme-tabler::inc.topbar_right_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_right_content.blade.php&line=1", "ajax": false, "filename": "topbar_right_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_right_content"}, {"name": "2x backpack.theme-tabler::inc.menu_user_dropdown", "param_count": null, "params": [], "start": 1754389947.235018, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/menu_user_dropdown.blade.phpbackpack.theme-tabler::inc.menu_user_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fmenu_user_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_user_dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.menu_user_dropdown"}, {"name": "1x backpack.theme-tabler::layouts.partials.mobile_toggle_btn", "param_count": null, "params": [], "start": 1754389947.25508, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/mobile_toggle_btn.blade.phpbackpack.theme-tabler::layouts.partials.mobile_toggle_btn", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fmobile_toggle_btn.blade.php&line=1", "ajax": false, "filename": "mobile_toggle_btn.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.mobile_toggle_btn"}, {"name": "1x backpack.theme-tabler::inc.footer", "param_count": null, "params": [], "start": 1754389947.292008, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/footer.blade.phpbackpack.theme-tabler::inc.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.footer"}, {"name": "1x backpack.ui::inc.scripts", "param_count": null, "params": [], "start": 1754389947.293552, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/scripts.blade.phpbackpack.ui::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.scripts"}, {"name": "7x __components::8a07812e6d8f9d2826754abad88e5380", "param_count": null, "params": [], "start": 1754389947.294776, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/8a07812e6d8f9d2826754abad88e5380.blade.php__components::8a07812e6d8f9d2826754abad88e5380", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F8a07812e6d8f9d2826754abad88e5380.blade.php&line=1", "ajax": false, "filename": "8a07812e6d8f9d2826754abad88e5380.blade.php", "line": "?"}, "render_count": 7, "name_original": "__components::8a07812e6d8f9d2826754abad88e5380"}, {"name": "1x backpack.theme-tabler::inc.alerts", "param_count": null, "params": [], "start": 1754389947.297155, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/alerts.blade.phpbackpack.theme-tabler::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": 1754389947.362859, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}, {"name": "1x backpack.theme-tabler::inc.theme_scripts", "param_count": null, "params": [], "start": 1754389947.380513, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_scripts.blade.phpbackpack.theme-tabler::inc.theme_scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_scripts.blade.php&line=1", "ajax": false, "filename": "theme_scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_scripts"}]}, "route": {"uri": "GET admin/dashboard", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\Admin\\HomeController@show", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "as": "home.show", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FHomeController.php&line=25\" onclick=\"\">app/Http/Controllers/Admin/HomeController.php:25-29</a>"}, "queries": {"nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07282, "accumulated_duration_str": "72.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 73}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 206}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 177}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}], "start": **********.135166, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = '1' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 206}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 177}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.152643, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:75", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=75", "ajax": false, "filename": "EloquentUserProvider.php", "line": "75"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 3.557}, {"sql": "select `c`.`name`, `j`.`title` as `job_title`, `company`.`name` as `company_name`, `apply_jobs`.`status`, `status`.`name` as `status_name`, `apply_jobs`.`updated_at`, `users`.`name` as `users_updated_by`, `apply_jobs`.`note` from `apply_jobs` inner join `cvs` as `c` on `apply_jobs`.`cv_id` = `c`.`id` inner join `jobs` as `j` on `apply_jobs`.`job_id` = `j`.`id` inner join `companies` as `company` on `j`.`company_id` = `company`.`id` inner join `status` on `apply_jobs`.`status` = `status`.`id` inner join `users` on `apply_jobs`.`updated_by` = `users`.`id` order by `apply_jobs`.`updated_at` desc limit 20", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/HomeController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\HomeController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.220471, "duration": 0.013349999999999999, "duration_str": "13.35ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:24", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=24", "ajax": false, "filename": "ApplyJobRepository.php", "line": "24"}, "connection": "c_hri", "explain": null, "start_percent": 3.557, "width_percent": 18.333}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 430}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.7062001, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "c_hri", "explain": null, "start_percent": 21.89, "width_percent": 3.049}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 430}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.733009, "duration": 0.012240000000000001, "duration_str": "12.24ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "c_hri", "explain": null, "start_percent": 24.938, "width_percent": 16.809}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 277}, {"index": 24, "namespace": "view", "name": "backpack.ui::inc.menu_items", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/vendor/backpack/ui/inc/menu_items.blade.php", "line": 2}], "start": 1754389946.011368, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "c_hri", "explain": null, "start_percent": 41.747, "width_percent": 1.744}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 277}], "start": 1754389946.015411, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "c_hri", "explain": null, "start_percent": 43.491, "width_percent": 1.524}, {"sql": "select * from `notifications` where `user_id` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": 1754389946.339851, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ClientViewComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FViewComposers%2FClientViewComposer.php&line=19", "ajax": false, "filename": "ClientViewComposer.php", "line": "19"}, "connection": "c_hri", "explain": null, "start_percent": 45.015, "width_percent": 1.758}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1754389946.3944058, "duration": 0.01728, "duration_str": "17.28ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 46.773, "width_percent": 23.73}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1754389947.235968, "duration": 0.01661, "duration_str": "16.61ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 70.503, "width_percent": 22.81}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1754389947.2567081, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 93.312, "width_percent": 6.688}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1784, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 197, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\ApplyJob": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FApplyJob.php&line=1", "ajax": false, "filename": "ApplyJob.php", "line": "?"}}, "App\\Models\\Notification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FNotification.php&line=1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2012, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 42, "messages": [{"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-405849599 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405849599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.129292, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1787839532 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787839532\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.217045, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1061529086 data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061529086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.219327, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1301016977 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301016977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.221913, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-416265721 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416265721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.225836, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-450266681 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450266681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.226659, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1469631928 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469631928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.227544, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-348959386 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348959386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.228972, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-538312413 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538312413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.229853, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-229922661 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229922661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.230705, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1234172200 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234172200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.231561, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.23241, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.234669, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.235872, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-25831588 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25831588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.236582, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176806274 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176806274\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.237538, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944213680 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944213680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.238482, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1524414688 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524414688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.24139, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1928566683 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928566683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389946.285377, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-183112248 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183112248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1754389946.320385, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-762993772 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762993772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1754389946.325446, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2054438226 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054438226\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.267252, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1771796168 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771796168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.268316, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.2692, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-63003297 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63003297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.270112, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-330045912 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330045912\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.271893, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-297044845 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297044845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.27267, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-367934276 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367934276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.273436, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-621049273 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621049273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.274742, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1653125948 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653125948\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.275632, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1600296179 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600296179\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.276424, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-618834905 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618834905\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.277233, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2073894640 data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073894640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.278061, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1045970556 data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045970556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.280016, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1166247440 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166247440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.281088, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1336627556 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336627556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.281762, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-340817704 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340817704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.282699, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1617090655 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617090655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.283617, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-955184504 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955184504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.286238, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2131354859 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131354859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754389947.287417, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-241712313 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241712313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1754389947.2892, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1256799672 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256799672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1754389947.290745, "xdebug_link": null}]}, "session": {"_token": "iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-597672861 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-597672861\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2117817937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2117817937\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1090240550 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1090240550\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1058245525 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InVmeUtnYlh0N1YzaTNSVHYrbklOS3c9PSIsInZhbHVlIjoiS0lKc1hqT2hyNGE5ODA3QUltZ2lJMHBDM3F5MlB6ZnVNTWRHUXdad1BNMzA4K2FWdDd5TUZFWXhFL0JRSHFuRjJyZlk4dzY5YU9ReU40Lzl1TWFxY1F1NzU4bzhUNWZtYzR5UDlBTi9pOUZTeWtZQ3poUWJFMXZPaEc2M2lwWEUiLCJtYWMiOiIzNjc0MjU4MGNmMTFlNTI0MDRlN2FmMzc4NzhlMWM5ZDUwNmIwZDJmYjQzOTQ3MzhiNGQ4MGY3MjU0OTk2NjZkIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IlRhRUVYK3hBM3VKNzBLUDZVVVdaalE9PSIsInZhbHVlIjoienZtdzEyNU1rUVc0R0dadDNCOGI0aWhGZGplcEdjeXhObUxzZ0VwcURocmRMci9kaSs2YzV0STdsQkZwQk15MnJuWVFtMnZONCtWb242L0NweVlvSzJnZVd6U05wTkZGcldmbmhXOTZMSXZpR0tBSzFUR1V4UlhyZGJqQkF6VngiLCJtYWMiOiIyNTk2NDJmYjljOTI1MmQ0Y2Q5NWY0ODFkODA5N2E2MTc3OWU3ZDVkZjBiN2Y4OThiNGU4NDcyNDExNzZlYWZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058245525\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1109481990 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">macvktI9GRiX6BmmcsgNV2HO5sBrI3rV1ISgmWmq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109481990\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-562363387 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 10:32:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InVIR1hXaTFVd082Qy9tUUN1ZDFlZ1E9PSIsInZhbHVlIjoiR1hhbTBDTGJTSkpKTVZWQzIwa0RZSmg2T2dqZjFQYkdaWTY0Y3BhM2tzdjRxTFRvd1RNL1AzNGdaZkJ2MlNveGlXOW00Qi9nWHk5YlEwVUJUSlVFQkc4M2UzU29LUjJzMzJremlKTkZGSmMxeFNSUGdoaVVYNTM2ZVAxaEwrZm4iLCJtYWMiOiI5ZDViZWEwNTIxNTg1NWJjZDA1NjY1YmZiMzU5MWNiMzk3NDJkNDQ2NWU2YTQ4M2Q4Zjc5ZTU4ZmQ0MTkwMzdhIiwidGFnIjoiIn0%3D; expires=Tue, 05 Aug 2025 12:32:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6Ik1lQlVpNEFOUnZLSGNWZlJVTlpBSlE9PSIsInZhbHVlIjoiSzRLdE4xOHBkbk1RanZONHlSeDlMMmdBazRZcGNvQ01vZ1prUzJZUllSNDV5czJqSlQ2dDRBKzdXWEJ0eGJacG5aK09Lbi9PdWpRM0NFekRvTVRveVpUODRvZVlhOFdPc0xicjVMT3dyMFVueVpvMjJjZzAzSnpqVUQ3NFhpVGUiLCJtYWMiOiJjNTE2YjkwNWIwMTM0NTVmM2MwNzgxZjc3MDZiNzkwYmZkMzUyN2Y5YjlkZjRlZGI1YmVhMmJlOGFiY2UyZTg3IiwidGFnIjoiIn0%3D; expires=Tue, 05 Aug 2025 12:32:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InVIR1hXaTFVd082Qy9tUUN1ZDFlZ1E9PSIsInZhbHVlIjoiR1hhbTBDTGJTSkpKTVZWQzIwa0RZSmg2T2dqZjFQYkdaWTY0Y3BhM2tzdjRxTFRvd1RNL1AzNGdaZkJ2MlNveGlXOW00Qi9nWHk5YlEwVUJUSlVFQkc4M2UzU29LUjJzMzJremlKTkZGSmMxeFNSUGdoaVVYNTM2ZVAxaEwrZm4iLCJtYWMiOiI5ZDViZWEwNTIxNTg1NWJjZDA1NjY1YmZiMzU5MWNiMzk3NDJkNDQ2NWU2YTQ4M2Q4Zjc5ZTU4ZmQ0MTkwMzdhIiwidGFnIjoiIn0%3D; expires=Tue, 05-Aug-2025 12:32:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6Ik1lQlVpNEFOUnZLSGNWZlJVTlpBSlE9PSIsInZhbHVlIjoiSzRLdE4xOHBkbk1RanZONHlSeDlMMmdBazRZcGNvQ01vZ1prUzJZUllSNDV5czJqSlQ2dDRBKzdXWEJ0eGJacG5aK09Lbi9PdWpRM0NFekRvTVRveVpUODRvZVlhOFdPc0xicjVMT3dyMFVueVpvMjJjZzAzSnpqVUQ3NFhpVGUiLCJtYWMiOiJjNTE2YjkwNWIwMTM0NTVmM2MwNzgxZjc3MDZiNzkwYmZkMzUyN2Y5YjlkZjRlZGI1YmVhMmJlOGFiY2UyZTg3IiwidGFnIjoiIn0%3D; expires=Tue, 05-Aug-2025 12:32:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562363387\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-618890666 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618890666\", {\"maxDepth\":0})</script>\n"}}