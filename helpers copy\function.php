<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

if (!function_exists('fieldColumnData')) {
    function fieldColumnData($name, $label, $type, $classCss, $validationRules = '', $value = null)
    {
        return [
            'name'            => $name,
            'label'           => $label,
            'type'            => $type,
            'value'           => $value,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'validationRules' => $validationRules,
        ];
    }
}

if (!function_exists('select2ModelData')) {
    function select2ModelData($label, $type, $name, $model, $column, $classCss = '', $validationRules = '', $value = null)
    {
        return [  // Select2
            'label'           => $label,
            'type'            => $type,
            'name'            => $name,     // the db column for the foreign key

            // optional
            'entity'          => $name,     // the method that defines the relationship in your Model
            'model'           => $model,    // foreign key model
            'attribute'       => $column,   // foreign key attribute that is shown to user
            'validationRules' => $validationRules,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'default'         => $value,
            'placeholder'     => 'Choose option',
            'allows_clear'    => true,
            'options'         => (function ($query) use ($column) {
                return $query->select('id', $column)->get();
            }),
        ];
    }
}
if (!function_exists('selectFormArrayData')) {
    function selectFormArrayData($name, $label, $classCss, $options, $validationRules)
    {
        return [
            'name'            => $name,
            'label'           => $label,
            'type'            => 'select_from_array',
            'allows_null'     => false,
            'options'         => ['' => '---'] + $options,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'validationRules' => $validationRules,
        ];
    }
}

if (!function_exists('inputUpload')) {
    function inputUpload($name, $label, $classCss, $disk = 's3', $validationRules = [])
    {
        return [   // Upload
            'name'            => $name,
            'label'           => $label,
            'type'            => 'upload',
            'upload'          => true,
            'path'            => 'cv',
            'disk'            => $disk,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'validationRules' => $validationRules,
        ];
    }
}

if (!function_exists('gen_url_file_s3')) {
    function gen_url_file_s3($path, $pathDefault = '', $version = true): string
    {
        if ($path) {
            if (Str::startsWith($path, 'http')) {
                return $path;
            }

            if (file_exists(public_path($path))) {
                return asset2($path);
            }

            return Storage::disk('s3')->url($path) . ($version ? version_asset() : null);
        }

        if ($pathDefault && Storage::disk('s3')->exists($pathDefault)) {
            return Storage::disk('s3')->url($pathDefault) . ($version ? version_asset() : null);
        }

        return $pathDefault ? asset2($pathDefault, $version) : '';
    }
}

if (!function_exists('version_asset')) {
    function version_asset(): string
    {
        $version = Cache::remember('version', 86400, function () {
            if (!file_exists(public_path('version.txt'))) {
                file_put_contents(public_path('version.txt'), time());
            }
            return file_get_contents(public_path('version.txt'));
        });

        return '?v=' . $version;
    }
}

if (!function_exists('asset2')) {
    function asset2($path, $version = true): string
    {
        return asset($path) . ($version ? version_asset() : null);
    }
}
