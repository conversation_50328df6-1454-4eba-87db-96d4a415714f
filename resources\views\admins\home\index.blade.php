@extends(backpack_view('blank'))

@php
    //    if (backpack_theme_config('show_getting_started')) {
    //        $widgets['before_content'][] = [
    //            'type'        => 'view',
    //            'view'        => backpack_view('inc.getting_started'),
    //        ];
    //    } else {
    //        $widgets['before_content'][] = [
    //            'type'        => 'jumbotron',
    //            'heading'     => trans('backpack::base.welcome'),
    //            'content'     => trans('backpack::base.use_sidebar'),
    //            'button_link' => backpack_url('logout'),
    //            'button_text' => trans('backpack::base.logout'),
    //        ];
    //    }
@endphp

@section('content')
    <div >
{{--        <h2 style="font-weight: 600">Top 20 Apply Job <span class="badge bg-danger">New</span></h2>--}}
{{--        <ul class="responsive-table">--}}
{{--            <li class="table-header">--}}
{{--                <div class="col col-1">Candidate</div>--}}
{{--                <div class="col col-2">Job title</div>--}}
{{--                <div class="col col-3">Company</div>--}}
{{--                <div class="col col-4">Status</div>--}}
{{--                <div class="col col-5">Updated at</div>--}}
{{--                <div class="col col-6">Users updated by</div>--}}
{{--                <div class="col col-7">Note</div>--}}
{{--            </li>--}}
{{--            @foreach($applyJobs as $apply)--}}
{{--                <li class="table-row">--}}
{{--                    <div class="col col-1" >{{$apply->name}}</div>--}}
{{--                    <div class="col col-2" >{{$apply->job_title}}</div>--}}
{{--                    <div class="col col-3" >{{$apply->company_name}}</div>--}}
{{--                    <div class="col col-4" >{{$apply->status_name}}</div>--}}
{{--                    <div class="col col-5" >{{\Carbon\Carbon::parse($apply->updated_at)->format('d/m/Y H:i')}}</div>--}}
{{--                    <div class="col col-6" >{{$apply->users_updated_by}}</div>--}}
{{--                    <div class="col col-7" >{{$apply->note}}</div>--}}
{{--                </li>--}}
{{--            @endforeach--}}

{{--        </ul>--}}
        <div class="col-12" id="app" >
            <dashboard></dashboard>
        </div>
    </div>
@endsection

@section('after_scripts')
    @vite('resources/js/app.js')
@endsection
