<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ContactService;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    protected $contactService;
    public function __construct(ContactService $contactService){
        $this->contactService = $contactService;
    }
    public function getContact(Request $request){
        return $this->contactService->getContact($request);
    }
}
