<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RawCv extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'raw_cvs';

    /**
     * <PERSON><PERSON><PERSON> trường có thể được gán hàng lo<PERSON>t
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'raw_data',
        'cv_public',
        'cv_private',
        'push_to_recland',
        'source',
        'source_id'
    ];

    /**
     * Các trường sẽ được cast sang kiểu dữ liệu khác
     */
    protected $casts = [
        'raw_data' => 'array',
        'push_to_recland' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
} 