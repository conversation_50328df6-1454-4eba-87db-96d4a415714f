<?php

namespace App\Helpers;

use Storage;
use Intervention\Image\ImageManagerStatic as Image;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use GuzzleHttp\Client;
use Illuminate\Support\Str;


class Utils
{

    # get username before @ in email
    public static function getUsernameFromEmail($email)
    {
        return explode('@', $email)[0];
    }
    public static function isEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }


    public static function getCities()
    {
        $cities = [];
        foreach (config('constant.cities') as $city) {
            $cities[Str::slug($city)] = $city;
        }
        return $cities;
    }

    public static function htmlToText($html)
    {
        $description = str_replace('<br>', "\n\r", $html);
        $description = str_replace('</p>', "\n\r", $description);
        $description = str_replace('<p>', "\n\r", $description);
        $description = str_replace('<br />', "\n\r", $description);
        $description = str_replace('<br/>', "\n\r", $description);
        $description = str_replace('<br>', "\n\r", $description);
        $description = str_replace('</br>', "\n\r", $description);
        $description = str_replace('</br/>', "\n\r", $description);
        $description = html_entity_decode($description);
        $description = strip_tags($description);
        return $description;
    }

    public static function money_format($var)
    {
        if (!is_numeric($var)) {
            return $var;
        }
        return number_format($var);
    }
    # short name "Bui Duc Chi Thanh" to "B.D.C Thanh"
    public static function shortName($name)
    {
        $name = explode(' ', $name);
        $shortName = '';
        $ho_ten_dem = [];
        foreach ($name as $key => $value) {
            if ($key == count($name) - 1) {
                $shortName = $value;
            } else {
                $value = strtoupper($value);
                $value = str_replace(
                    'Đ',
                    'D',
                    $value
                );
                #get first character of each word
                $ho_ten_dem[] = strtoupper(substr($value, 0, 1));

                // $ho_ten_dem[] = strtoupper($value[0]);
            }
        }
        return implode('.', $ho_ten_dem) . ' ' . $shortName;
    }
    # xóa các ký tự đặc biệt trong file name nhưng vẫn giữ unicode
    public static function cleanFileName($fileName)
    {
        # chỉ loại bỏ các ký tự đặc biệt như: !@#$%^&*()+=[]{}|\:;"'<>,?/~`
        $fileName = preg_replace('/[\!@#\$%\^&\*\+=\[\]\{\}\|\\\:\;"\'<>,\?\/~\`]/', ' ', $fileName);
        # loại bỏ nhiều dấu cách liên tiếp
        $fileName = preg_replace('/\s+/', ' ', $fileName);
        # loại bỏ nhiều dấu gạch ngang liên tiếp
        $fileName = preg_replace('/-+/', '-', $fileName);
        # loại bỏ dấu gạch ngang ở đầu và cuối
        $fileName = trim($fileName, '-');
        # loại bỏ khoảng trắng thừa
        $fileName = preg_replace('/\s+/', ' ', $fileName);
        return $fileName;
    }
    public static function cleanPhone($value, $country_code = '')
    {
        $tel = preg_replace('/[^0-9]/', '', $value);
        $tel = ltrim($tel, '0');
        if (trim($country_code) != '') {
            $len_country = strlen($country_code);
            if (substr($tel, 0, $len_country) == $country_code && strlen($tel) > 10) {
                $tel = substr($tel, $len_country);
            }
            $tel = $country_code . $tel;
            return $tel;
        } else {
            if (substr($tel, 0, 2) == '84' && strlen($tel) > 10) {
                $tel = substr($tel, 2);
            }
            return '0' . $tel;
        }
    }
    public static function isValidDate($date)
    {
        return (bool)strtotime($date);
    }
    public static function getTenNumberPhone($phone)
    {
        $change_array = array(
            array('84', '0'),
            array('+84', '0'),
            array('016', '03'),
            array('0120', '070'),
            array('0121', '079'),
            array('0122', '077'),
            array('0126', '076'),
            array('0128', '078'),
            array('0123', '083'),
            array('0124', '084'),
            array('0125', '085'),
            array('0127', '081'),
            array('0129', '082'),
            array('0188', '058'),
            array('0186', '056'),
            array('0199', '059')
        ); //

        foreach ($change_array as $key => $value) {
            if (Str::startsWith($phone, $value[0])) {
                $phone = Str::replaceFirst($value[0], $value[1], $phone);
                break;
            }
        }
        return $phone;
    }

    /**
     * Ẩn một phần số điện thoại
     * 
     * @param string $phone Số điện thoại cần ẩn
     * @param int $visibleStart Số ký tự hiển thị ở đầu
     * @param int $visibleEnd Số ký tự hiển thị ở cuối
     * @param string $mask Ký tự dùng để ẩn
     * @return string Số điện thoại đã được ẩn một phần
     */
    public static function maskPhoneNumber($phone, $visibleStart = 3, $visibleEnd = 3, $mask = '*')
    {
        // Loại bỏ các ký tự không phải số
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Kiểm tra độ dài số điện thoại
        $length = strlen($phone);

        // Nếu số điện thoại quá ngắn, trả về nguyên bản
        if ($length <= ($visibleStart + $visibleEnd)) {
            return $phone;
        }

        // Lấy phần đầu và phần cuối của số điện thoại
        $start = substr($phone, 0, $visibleStart);
        $end = substr($phone, -$visibleEnd);

        // Tạo phần ẩn giữa số điện thoại
        $masked = str_repeat($mask, $length - $visibleStart - $visibleEnd);

        // Kết hợp các phần lại với nhau
        return $start . $masked . $end;
    }
}
