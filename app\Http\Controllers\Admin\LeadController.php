<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApplyJob;
use App\Models\Company;
use App\Models\Contact;
use App\Models\Lead;
use App\Models\Notification;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LeadController extends Controller
{
    public function createCompany(Request $request)
    {
        $lead = Lead::findOrFail($request->id);
        $exist_contact = Contact::where('email', $lead->email)->orWhere('phone', $lead->phone)->first();
        // dd($exist_contact);
        if ($exist_contact && $exist_contact->company) {
            $company = $exist_contact->company;
            \Alert::add('error', 'Đã có Công ty <strong>'.$company->name. '</strong> trùng với số điện thoại hoặc email<br>Vui lòng sửa lead để thêm thông tin công ty')->flash();
            return redirect()->route('lead.show', $lead->id);
        }

        $exist_company= Company::where('mobile', $lead->phone)->orWhere('email', $lead->email)->first();
        if ($exist_company) {
            \Alert::add('error', 'Đã có Công ty <strong>' . $exist_company->name . '</strong> trùng với số điện thoại hoặc email<br>Vui lòng sửa lead để thêm thông tin công ty')->flash();
            return redirect()->route('lead.show', $lead->id);
        }
        $company = Company::create([
            'name' => $lead->company_name,
            'email' => $lead->email,
            'mst' => '',
            'mobile' => $lead->phone,
            // 'address' => $lead->address,
            'created_by' => backpack_user()->id,
        ]);

        $company->contact()->create([
            'name' => $lead->contact_name,
            'email' => $lead->email,
            'phone' => $lead->phone,
            // 'position' => $lead->position,
            'first_contact_by' => backpack_user()->id,
        ]);

        \Alert::add('info', 'Yêu cầu nhập đủ MST và các thông tin công ty')->flash();
        return redirect()->route('company.edit', $company->id);
    }

}
