<?php

namespace App\Notifications;

use App\Helpers\Utils;
use App\Models\ApplyJob;
use App\Models\GoogleChatData;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class NewFilePrivateApplyJobGoogleChatNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $applyJob;
    protected $data_notify;
    /**
     * Create a new notification instance.
     */
    public function __construct(ApplyJob $applyJob, $data_notify = [])
    {
        $this->applyJob = $applyJob;
        $this->data_notify = $data_notify;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['googleChat'];
    }

    /**
     * G<PERSON>i thông báo đến Google Chat
     */
    public function toGoogleChat(object $notifiable): array
    {
        $job = $this->applyJob->job;
        $candidate = $this->applyJob->candidate;
        $cv = $this->applyJob->cv;
        $company = $job->company;
        $createdBy = Utils::getUsernameFromEmail(trim(optional($this->applyJob->createdBy)->email)) ?? 'Unknown';

        // $message = "🔔 *CÓ ỨNG TUYỂN MỚI* 🔔\n\n";
        $message = "📌 *FILE CV MỚI VỪA ĐƯỢC CẬP NHẬT*\n";
        // $message .= "ID: #{$this->applyJob->id}\n";
        $message .= "Ứng viên: *{$candidate->name}*\n";
        // $message .= "CV: *{$cv->name}*\n";
        $message .= "Vị trí: *{$job->title}*\n";
        if (!$job->hide_to_recer) {
            $message .= "Công ty: *{$company->name}*\n";
        }

        // if ($this->applyJob->desired_salary) {
        //     $message .= "Mức lương mong muốn: *{$this->applyJob->desired_salary}*\n";
        // }

        // if ($this->applyJob->date_onboard) {
        //     $message .= "Ngày có thể onboard: *{$this->applyJob->date_onboard}*\n";
        // }

        $message .= "Người tạo: *{$this->data_notify['user_name']}*\n";
        $message .= "\nXem chi tiết: " . backpack_url('apply-job/' . $this->applyJob->id . '/show');


        // $gg_space = GoogleChatData::where('space_key', 'like', '%' . 'update-cv-apply' . '%')->where('is_active', true)->first();
        return GoogleChatData::sendNotification($message, 'update-cv-apply');
    }
}
