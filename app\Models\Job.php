<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Zoha\Metable;

class Job extends Model implements AuditableContract
{
    use CrudTrait, HasFactory, Auditable, SoftDeletes, Notifiable, Metable;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();
        static::created(function ($model) {
            event(new \App\Events\JobCreatedEvent($model));

            if (empty($model->job_code)) {
                $model->job_code = 'J' . str_pad($model->id, 5, '0', STR_PAD_LEFT);
                $model->saveQuietly();
            }
        });
        static::saving(function ($model) {
            if (empty($model->currency)) {
                $model->currency = Status::where('group', 'currency')->where('slug_value', 'vnd')->value('id');
            }
        });
    }

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function languages(): BelongsToMany
    {
        return $this->belongsToMany(CareerLanguage::class, 'job_languages');
    }

    public function levels(): BelongsToMany
    {
        return $this->belongsToMany(CareerLevel::class, 'job_levels', 'job_id', 'level_id');
    }

    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(Skill::class, 'job_skills', 'job_id', 'skill_id');
    }

    public function typeJob(): BelongsTo
    {
        return $this->belongsTo(Status::class, 'type', 'id');
    }
    public function priorityJob(): BelongsTo
    {
        return $this->belongsTo(Status::class, 'priority', 'id');
    }
    public function statusJob(): BelongsTo
    {
        return $this->belongsTo(Status::class, 'status', 'id');
    }

    public function contractJob()
    {
        return $this->belongsTo(Status::class, 'form_contract', 'id');
    }
    public function recTeam(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, 'jobs_rec_team', 'job_id', 'department_id');
    }

    public function recerJob(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'jobs_recer', 'job_id', 'recer_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function currencyJob()
    {
        return $this->belongsTo(Status::class, 'currency', 'id');
    }


    public function routeNotificationForMail($notification)
    {
        $recJob = [];
        $managers = [];

        $user = User::where('id', $this->user->id)->pluck('name', 'email')->toArray();

        if ($this->recerJob) {
            $recJob = User::whereIn('id', $this->recerJob->pluck('recer_id')->toArray())
                ->pluck('name', 'email')->toArray();
        }

        if ($this->recTeam) {
            $managerIds = Department::whereIn('id', $this->recTeam->pluck('department_id')->toArray())
                ->pluck('manager')
                ->toArray();
            $managers = User::whereIn('id', $managerIds)->pluck('name', 'email')->toArray();
        }

        return $user + $managers + $recJob;
    }

    public function countByApplyStatusLog()
    {
        // Cache kết quả với TTL = 30 giây
        $cacheKey = 'job_apply_status_log_' . $this->id;
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        } else {
            $sql = "SELECT COUNT(status_id) AS total, status_id, s.slug_value FROM (SELECT DISTINCT status_id,apply_job_id FROM apply_jobs as ap
            INNER JOIN log_change_status_applies AS log ON ap.id = log.apply_job_id
            WHERE ap.job_id = ?
            GROUP BY apply_job_id, status_id) AS tmp INNER JOIN `status` AS s ON tmp.status_id = s.id GROUP BY status_id, s.slug_value;";
            $data = \DB::select($sql, [$this->id]);
            $data = collect($data)->pluck('total', 'slug_value')->toArray();
            Cache::put($cacheKey, $data, 30);
            return $data;
        }
    }

    public function countByApplyStatus($slug = 'slug_value')
    {
        $cacheKey = 'job_apply_status_' . $this->id . '_' . $slug;
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        } else {
            $data = $this->hasMany(ApplyJob::class, 'job_id', 'id')
                ->whereHas('statusApply', function ($query) use ($slug) {
                    $query->where('slug_value', $slug);
                })->count();
            Cache::put($cacheKey, $data, 30);
            return $data;
        }
    }





    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeRoleData($query)
    {
        // return $query;
        return $query->where(function ($q) {
            $user = backpack_user();
            if ($user->can('job.all-data')) {
                return $q;
            } else {
                $userIds = [$user->id];
                if ($user->can('job.only-company')) {
                    $userIds = User::where('internal_company_id', $user->internal_company_id)
                        ->pluck('id')->toArray();
                } elseif ($user->can('job.only-team')) {
                    $userIds = User::where('department_id', $user->department_id)
                        ->pluck('id')->toArray();
                }
                $q->where('jobs.created_by', $user->id)
                    ->orWhereIn('jobs.created_by', $userIds);

                $q->orWhereHas('recerJob', function ($q2) use ($userIds) {
                    $q2->whereIn('recer_id', $userIds);
                });
                $q->orWhereHas('recTeam', function ($q3) {
                    $q3->where('department_id', backpack_user()->department_id);
                });
                return $q;
            }
        });
    }



    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | BUTTONS
    |--------------------------------------------------------------------------
    */
    public function duplicateJobButton($crud = false)
    {
        return '<a class="btn btn-sm btn-link" 
                   href="' . backpack_url('job/' . $this->id . '/duplicate') . '" 
                   data-button-type="duplicate"
                   title="Nhân bản job"
                   onclick="return confirm(\'Bạn có chắc chắn muốn nhân bản job này không?\')">
                   <i class="la la-copy"></i> Nhân bản
                </a>';
    }

    public function getHideToRecerAttribute()
    {
        // Nếu job_type = config job type bodyshop thì trả về true, ngược lại trả về false
        // Sử dụng config('constant.job_type.bodyshop') để lấy giá trị bodyshop từ config
        return $this->type == config('constant.job_type.bodyshop');
    }
}
