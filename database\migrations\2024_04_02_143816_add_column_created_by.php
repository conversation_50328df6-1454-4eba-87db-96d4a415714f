<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cvs', function (Blueprint $table) {
            $table->integer('created_by')->after('cv_private')->nullable();
        });
        Schema::table('candidates', function (Blueprint $table) {
            $table->integer('created_by')->after('old_company')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cvs', function (Blueprint $table) {
            $table->dropColumn('created_by');
        });
        Schema::table('candidates', function (Blueprint $table) {
            $table->dropColumn('created_by');
        });
    }
};
