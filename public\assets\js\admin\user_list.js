$(document).ready(function () {
    $('body').on('change', '.status-switch', function () {
        var userId = $(this).data('id');
        var isChecked = $(this).is(':checked');

        $.ajax({
            url: '/admin/user/' + userId + '/toggle-status',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                if (response.success) {
                    new Noty({
                        type: "success",
                        text: "User status updated successfully"
                    }).show();
                }
            },
            error: function () {
                $(this).prop('checked', !isChecked);
                new Noty({
                    type: "error",
                    text: "Error updating user status"
                }).show();
            }
        });
    });
});