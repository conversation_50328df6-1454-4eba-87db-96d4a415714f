<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\UserRequest;
use App\Models\Department;
use App\Models\InternalCompany;
use App\Models\User;
use \Backpack\PermissionManager\app\Http\Controllers\UserCrudController as BaseUserController;
use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
use Backpack\CRUD\app\Library\Widget;


/**
 * Class UserCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class UserCrudController extends BaseUserController
{
    use FetchOperation;

    public function setupListOperation()
    {
        $user = backpack_user();
        if (!$user->can('user.index')) {
            abort(403, 'Bạn không có quyền thêm mới người dùng');
        }
        Widget::add()->type('script')->content('assets/js/admin/user_list.js');
        $this->crud->addColumns([
            [
                'name'  => 'name',
                'label' => trans('backpack::permissionmanager.name'),
                'type'  => 'text',
            ],
            [
                'name'  => 'email',
                'label' => trans('backpack::permissionmanager.email'),
                'type'  => 'email',
            ],
            [
                'name'  => 'department',
                'label' => 'Bộ phận',
            ],
            [
                'name'  => 'internalCompany',
                'label' => 'Công ty',
            ], [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'checkbox',
                // 'label'          => ' <input type="checkbox" class="crud_bulk_actions_main_checkbox" style="width: 16px; height: 16px;" />',
                'options' => [
                    0 => 'Inactive',
                    1 => 'Active'
                ]
            ],
            [ // n-n relationship (with pivot table)
                'label'     => trans('backpack::permissionmanager.roles'), // Table column heading
                'type'      => 'select_multiple',
                'name'      => 'roles', // the method that defines the relationship in your Model
                'entity'    => 'roles', // the method that defines the relationship in your Model
                'attribute' => 'name', // foreign key attribute that is shown to user
                'model'     => config('permission.models.role'), // foreign key model
            ],
            [ // n-n relationship (with pivot table)
                'label'     => trans('backpack::permissionmanager.extra_permissions'), // Table column heading
                'type'      => 'select_multiple',
                'name'      => 'permissions', // the method that defines the relationship in your Model
                'entity'    => 'permissions', // the method that defines the relationship in your Model
                'attribute' => 'name', // foreign key attribute that is shown to user
                'model'     => config('permission.models.permission'), // foreign key model
            ],
        ]);

        if (backpack_pro()) {
            // Role Filter
            $this->crud->addFilter(
                [
                    'name'  => 'status',
                    'type'  => 'dropdown',
                    'label' => 'Trạng thái',
                ],
                [
                    '0' => 'Chưa kích hoạt',
                    '1' => 'Kích hoạt',
                ],
                function ($value) {
                    $this->crud->addClause('where', 'status', $value);
                }
            );
            $this->crud->addFilter(
                [
                    'name'  => 'role',
                    'type'  => 'dropdown',
                    'label' => trans('backpack::permissionmanager.role'),
                ],
                config('permission.models.role')::all()->pluck('name', 'id')->toArray(),
                function ($value) { // if the filter is active
                    $this->crud->addClause('whereHas', 'roles', function ($query) use ($value) {
                        $query->where('role_id', '=', $value);
                    });
                }
            );

            // Extra Permission Filter
            $this->crud->addFilter(
                [
                    'name'  => 'permissions',
                    'type'  => 'select2',
                    'label' => trans('backpack::permissionmanager.extra_permissions'),
                ],
                config('permission.models.permission')::all()->pluck('name', 'id')->toArray(),
                function ($value) { // if the filter is active
                    $this->crud->addClause('whereHas', 'permissions', function ($query) use ($value) {
                        $query->where('permission_id', '=', $value);
                    });
                }
            );
        }
    }
    protected function addUserFields()
    {
        $user = backpack_user();
        if (!$user->canOutsideAccess()) {
            abort(403, 'Bạn không thể truy cập CRM từ mạng Internet bên ngoài công ty. Nếu cần xử lý gấp, vui lòng gọi điện 0932329007');
        }
        if (!$user->can('user.store')) {
            abort(403, 'Bạn không có quyền thêm mới người dùng');
        }
        $this->crud->addFields([
            [
                'name'    => 'name',
                'label'   => trans('backpack::permissionmanager.name'),
                'type'    => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
            ],
            [
                'name'    => 'email',
                'label'   => trans('backpack::permissionmanager.email'),
                'type'    => 'email',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
            ],
            [
                'type'                 => "relationship",
                'name'                 => 'internalCompany',
                'label'                => 'Công ty',
                'ajax'                 => true,
                'placeholder'          => 'Choose option',
                'minimum_input_length' => 0,
                'wrapper'              => [
                    'class' => 'form-group col-md-4',
                ],
                'validationRules'      => [
                    'required',
                ],
                'inline_create'        => [                                         // specify the entity in singular
                    'modal_class' => 'modal-dialog modal-xl modal-dialog-centered', // use modal-sm, modal-lg to change width
                ],

            ],
            [
                'type'                 => "relationship",
                'name'                 => 'department',
                'label'                => 'Phòng ban',
                'ajax'                 => true,
                'placeholder'          => 'Choose option',
                'minimum_input_length' => 0,
                'wrapper'              => [
                    'class' => 'form-group col-md-4',
                ],
                'validationRules'      => [
                    'required',
                ],
                'inline_create'        => [                                         // specify the entity in singular
                    'modal_class' => 'modal-dialog modal-xl modal-dialog-centered', // use modal-sm, modal-lg to change width
                ],

            ],
            [
                'name'    => 'password',
                'label'   => trans('backpack::permissionmanager.password'),
                'type'    => 'password',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
            ],
            [
                'name'    => 'password_confirmation',
                'label'   => trans('backpack::permissionmanager.password_confirmation'),
                'type'    => 'password',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
            ],
            [   // Switch
                'name'  => 'status',
                'type'  => 'switch',
                'label'    => 'Trạng thái kích hoạt',

                // optional
                'color'    => 'primary', // May be any bootstrap color class or an hex color
                'onLabel' => '✓',
                'offLabel' => '✕',
            ],
            [   // Switch
                'name'  => 'outside_access',
                'type'  => 'switch',
                'label'    => 'Truy cập từ mạng bên ngoài',

                // optional
                'color'    => 'primary', // May be any bootstrap color class or an hex color
                'onLabel' => '✓',
                'offLabel' => '✕',
            ],
            [
                // two interconnected entities
                'label'             => trans('backpack::permissionmanager.user_role_permission'),
                'field_unique_name' => 'user_role_permission',
                'type'              => 'checklist_dependency',
                'name'              => 'roles,permissions',
                'subfields'         => [
                    'primary'   => [
                        'label'            => trans('backpack::permissionmanager.roles'),
                        'name'             => 'roles',                          // the method that defines the relationship in your Model
                        'entity'           => 'roles',                          // the method that defines the relationship in your Model
                        'entity_secondary' => 'permissions',                    // the method that defines the relationship in your Model
                        'attribute'        => 'name',                           // foreign key attribute that is shown to user
                        'model'            => config('permission.models.role'), // foreign key model
                        'pivot'            => true,                             // on create&update, do you need to add/delete pivot table entries?]
                        'number_columns'   => 3,                                //can be 1,2,3,4,6
                    ],
                    'secondary' => [
                        'label'          => mb_ucfirst(trans('backpack::permissionmanager.permission_plural')),
                        'name'           => 'permissions',                          // the method that defines the relationship in your Model
                        'entity'         => 'permissions',                          // the method that defines the relationship in your Model
                        'entity_primary' => 'roles',                                // the method that defines the relationship in your Model
                        'attribute'      => 'name',                                 // foreign key attribute that is shown to user
                        'model'          => config('permission.models.permission'), // foreign key model
                        'pivot'          => true,                                   // on create&update, do you need to add/delete pivot table entries?]
                        'number_columns' => 3,
                    ],
                ],
            ],
        ]);
    }

    public function fetchUserManager()
    {
        return $this->fetch(User::class);
    }

    public function fetchDepartment()
    {
        return $this->fetch(Department::class);
    }

    public function fetchInternalCompany()
    {
        return $this->fetch(InternalCompany::class);
    }
    public function toggleStatus($id)
    {
        $user = User::findOrFail($id);
        $user->status = !$user->status;
        $user->save();

        return response()->json(['success' => true]);
    }
}
