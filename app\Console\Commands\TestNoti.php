<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TestNoti extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-noti';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $log_task = \App\Models\LogTask::find(26);
        $log_task->notify(new \App\Notifications\LogTaskCreated());
        $this->info('Done');
    }
}
