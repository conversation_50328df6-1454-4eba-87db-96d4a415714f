// get access token from local storage, if not request new token from server
function getAccessToken() {
    // localStorage.removeItem('access_token');
    var token = localStorage.getItem('access_token');
    if (token == null) {
        $.ajax({
            type: 'GET',
            url: '/api/get-access-token',
            success: function (data) {
                localStorage.setItem('access_token', data.access_token);
            },
            error: function (data) {
                console.log(data);
            }
        });
    }
    return token;
}

getAccessToken();