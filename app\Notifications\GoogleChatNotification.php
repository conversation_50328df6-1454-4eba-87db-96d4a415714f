<?php

namespace App\Notifications;

use App\Models\GoogleChat;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class GoogleChatNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $message;
    protected $spaceId;

    /**
     * Create a new notification instance.
     */
    public function __construct($message, $spaceId = null)
    {
        $this->message = $message;
        $this->spaceId = $spaceId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['googleChat'];
    }

    /**
     * <PERSON><PERSON><PERSON> thông b<PERSON>o đến Google Chat
     */
    public function toGoogleChat(object $notifiable): array
    {
        return GoogleChat::sendNotification($this->message, $this->spaceId);
    }
}
