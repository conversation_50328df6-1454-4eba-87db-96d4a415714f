<?php

namespace App\Services;


use App\Models\ApplyJob;
use App\Models\Candidate;
use App\Models\Cv;
use App\Models\Job;
use App\Models\Note;
use App\Models\Status;
use App\Models\User;
use Illuminate\Http\Request;

class ApplyJobService
{

    public function queryList(array $filters = [])
    {
        $user = backpack_user();
        $job_id = $filters['job_id'] ?? null;
        if ($job_id) {
            $job = Job::find($job_id);
        }

        $data = ApplyJob::query()
            ->when(!empty($filters['search']['value']), function ($query) use ($filters) {
                $query->orWhereHas('candidate', function ($q) use ($filters) {
                    $q->where('candidates.name', 'like', '%' . $filters['search']['value'] . '%')
                        ->orWhere('email', 'like', '%' . $filters['search']['value'] . '%');
                });
            })
            ->when(!empty($filters['job_id']), function ($query) use ($filters) {
                $query->where('job_id', $filters['job_id']);
            })
            ->when(!empty($filters['created_by']), function ($query) use ($filters) {
                $query->where('created_by', $filters['created_by']);
            })
            ->when(!empty($filters['status']), function ($query) use ($filters) {
                $query->where('status', $filters['status']);
            });
        if ($job && backpack_user()->canShowDetailJob($job_id)) {
            // nếu xem được job thì lấy tất cả
        } elseif (!$user->canOutsideAccess()) {
            $data->where('created_by', $user->id);
        } else {
            if ($user->can('apply-job.all-data')) {
            } else if ($user->can('apply-job.only-team')) {
                $data->orWhereHas('createdBy', function ($q) use ($user) {
                    $department_ids = $user->department ? $user->department->pluck('id')->toArray() : [];
                    $q->whereIn('department_id', $department_ids);
                });
            } else {
                // $data->whereHas('job', function ($q) use ($user) {
                $data->where('created_by', $user->id);
                // });
            }
        }
        $data = $data->latest('id');
        // dd($data->toRawSql());

        // dd($data);


        // ->when($user->can('apply-job.only-user'), function ($query) use ($user) {
        //     $query->orWhereHas('job', function ($q) use ($user) {
        //         $q->where('user_id', $user->id)->orWhere('created_by', $user->id);
        //     });
        // })
        // ->when($user->can('apply-job.only-team'), function ($query) use ($user) {
        //     $query->orWhereHas('createdBy', function ($q) use ($user) {
        //         $department_ids = $user->department ? $user->department->pluck('id')->toArray() : [];
        //         $q->whereIn('department_id', $department_ids);
        //     });
        // })
        return $data;
    }


    public function create(array $data)
    {
        return ApplyJob::create($data);
    }
    public function update($applyJobId, array $data)
    {
        $apply = ApplyJob::findOrFail($applyJobId);
        $apply->update($data);
        return $apply;
    }

    public function checkExitsApplyJob($job_id, $candidate_id)
    {
        $skip_status = config('constant.apply-job.skip-check-duplicate');
        $status_ids = Status::where('group', 'apply-job')->whereIn('slug_value', $skip_status)->pluck('id')->toArray();
        return ApplyJob::where('job_id', $job_id)->where('candidate_id', $candidate_id)->whereNotIn('status', $status_ids)->exists();
    }
}
