<?php

namespace App\Console\Commands;

use App\Helpers\Utils;
use App\Models\Comment;
use App\Models\Company;
use App\Models\Contact;
use App\Models\Lead;
use Illuminate\Console\Command;

class ImportCompanyDataCsv extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-company-data-csv';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';
    protected $csvFile = 'crm_lead.csv';
    protected $source = 'import-misa-crm';
    protected $default_user_ids = 5;
    protected $default_status = 76;
    /**
     * Execute the console command.
     */
    public function handle()
    {
        // $file_path = 'D:/download/cv_crm_2023.csv';
        $file_path = base_path('_import_data/' . $this->csvFile);
        $file = fopen($file_path, 'r');
        $row = 0;
        $trung = [];
        while (($line = fgetcsv($file)) !== FALSE) {
            if ($row <= 0) {
                $row++;
                continue;
            }
            $row++;
            $this->info($row);
            $rs = $this->import($line);
            if (isset($rs['trung']) && !empty($rs['trung'])) {
                $trung[] = $rs['trung'];
            }
        }
        if (count($trung)) {
            dd(implode("\n", $trung));
        }
    }

    public function import($line)
    {
        $leadContent = implode("\n", $line);
        $data = [
            'user_id'      => $this->default_user_ids,
            'company_name' => $line[8],
            'contact_name' => $line[2],
            'email'        => $line[6],
            'phone'        => $line[4],
            'source'       => $this->source,
            'status'       => $this->default_status,
            'lead_content' => trim($leadContent),
        ];
        if (trim($data['email']) == '' && trim($data['phone']) == '') {
            $this->error('Email or Phone is required: ' . $leadContent);
            return;
        }
        // dd($data);
        // $existingLead = Lead::where('lead_content', $leadContent)->first();

        // if ($existingLead) {
        //     $existingLead = Lead::update([
        //         'user_id' => $data['user_id'],
        //         'company_name' => $data['company_name'],
        //         'contact_name' => $data['contact_name'],
        //         'email' => $data['email'],
        //         'phone' => $data['phone'],
        //         'source' => $data['source'],
        //         'lead_content' => $data['lead_content'],
        //     ]);
        //     $this->info('Lead already exists: ' . $existingLead->id . ' - ' . $leadContent);
        // } else {
        $lead = Lead::create([
            'user_id'      => $data['user_id'],
            'company_name' => $data['company_name'],
            'contact_name' => $data['contact_name'],
            'email'        => $data['email'],
            'phone'        => $data['phone'],
            'source'       => $data['source'],
            'status'       => $data['status'],
            'lead_content' => $data['lead_content'],
        ]);
        $this->info('Created Lead: ' . $lead->id . ' - ' . $leadContent);
        // }
    }
}
