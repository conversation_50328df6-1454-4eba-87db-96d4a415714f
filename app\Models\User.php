<?php

namespace App\Models;

use App\Helpers\Utils;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, CrudTrait, HasRoles, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'user_name',
        'password',
        'manager',
        'department_id',
        'outside_access',
        'status',
        'internal_company_id',
    ];

    protected static function boot()
    {
        parent::boot();
        static::saving(function ($model) {
            if ($model->isDirty('email')) {
                $model->user_name = \App\Helpers\Utils::getUsernameFromEmail($model->email);
            }
        });
    }
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>J
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password'          => 'hashed',
    ];


    public function internalCompany()
    {
        return $this->belongsTo(InternalCompany::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function userManager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    // public function getUserNameAttribute()
    // {
    //     return Utils::getUsernameFromEmail($this->email);
    // }

    # get user ids by department
    public function getUserIdsSameDepartment()
    {
        return $this->where('department_id', $this->department_id)->pluck('id')->toArray();
    }
    public function getUserIdsSameCompany()
    {
        return $this->where('internal_company_id', $this->internal_company_id)->pluck('id')->toArray();
    }

    public function currentAccessToken()
    {
        return $this->tokens()->where('expires_at', '>=', time())->orWhereNull('expires_at')->first();
    }

    public function routeNotificationForMail($notification)
    {
        return [$this->email => $this->name];
    }

    public function canOutsideAccess()
    {
        if ($this->hasRole('admin') || $this->hasRole('super-admin')) {
            return true;
        }
        if ($this->outside_access) {
            return true;
        }
        $clientIp = request()->ip();
        $allowedIps = AllowIp::pluck('ip_address')->toArray();
        $accept_ips = array_merge($allowedIps, config('app.accept_ip'));
        // dd($accept_ips);
        // $accept_ips = [];
        // dd($clientIp, $accept_ips);
        if (in_array($clientIp, $accept_ips)) {
            return true;
        }
        return false;
    }

    public function canShowDetailJob($job_id)
    {
        if ($this->hasRole('admin') || $this->hasRole('super-admin')) {
            return true;
        }
        if ($job_id instanceof Job) {
            $job = $job_id;
        } else {
            $job = Job::find($job_id);
        }
        if ($job->created_by == $this->id) {
            return true;
        }
        if ($this->can('job.all-data')) {
            return true;
        }
        if ($this->can('job.only-team')) {
            $department_ids = $this->department ? [$this->department_id] : [];
            if (in_array($job->createdBy->department_id, $department_ids)) {
                return true;
            }
        }
        if ($this->can('job.only-user')) {
            if ($job->created_by == $this->id) {
                return true;
            }
        }
        $job_user = $job->recerJob->pluck('id')->toArray();
        if (in_array($this->id, $job_user)) {
            return true;
        }
        $rec_team = $job->recTeam->pluck('id')->toArray();
        if (in_array($this->department_id, $rec_team)) {
            return true;
        }
        return false;
    }

    public function canShowDetailApplyJob($apply_job_id)
    {
        $apply_job = ApplyJob::find($apply_job_id);
        if ($this->canShowDetailJob($apply_job->job_id)) {
            return true;
        }
        if ($apply_job->user_id == $this->id) {
            return true;
        }
        return false;
    }
}
