<?php

namespace App\Console\Commands;

use App\Models\Cv;
use App\Jobs\PushCvToRecland;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;
use Illuminate\Contracts\Bus;

class PushToRecland extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:PushToRecland';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sell CV to Recland';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $file = 'cv_ids.txt';
        $ids = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        Cv::whereNull('pushed_to_recland')
            ->where('cv_public', '!=', '')
            ->whereNotNull('yoe')
            ->orderBy('id', 'desc')
            ->chunk(1, function ($cvs) use ($ids) {
                foreach ($cvs as $cv) {
                    if ($ids && in_array($cv->id, $ids)) {
                        $this->info($cv->email . ' Da xu ly');
                        continue;
                    }
                    // $job = new PushCvToRecland($cv);
                    // Bus::dispatch($job);
                    // dd($job->response);
                    $result = PushCvToRecland::dispatchSync($cv);
                    // dd($result);

                    // $result = Queue::push(new PushCvToRecland($cv));
                    // dd($result);
                    // if ($result['success']) {
                    //     $this->info($cv->email . ' Đã gửi CV thành công');
                    // } else {
                    //     $this->error($cv->email . ' ' . $result['message']);
                    // }
                }
            });

        return Command::SUCCESS;
    }
}
