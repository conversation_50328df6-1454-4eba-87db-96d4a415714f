<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class MatchScoreCv extends Model
{
    use HasFactory;

    protected $table = 'match_score_cvs';

    protected $fillable = [
        'parent_type',
        'parent_id',
        'job_id',
        'cv_id',
        'experience_score',
        'skills_score',
        'overview_score',
        'raw_data',
    ];

    protected $casts = [
        'raw_data' => 'array',
        'experience_score' => 'float',
        'skills_score' => 'float',
        'overview_score' => 'float',
    ];

    /**
     * Quan hệ polymorphic với parent (có thể là Cv hoặc ApplyJob)
     */
    public function parent(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Quan hệ với Job
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class);
    }

    /**
     * <PERSON>uan hệ với Cv
     */
    public function cv(): BelongsTo
    {
        return $this->belongsTo(Cv::class);
    }

    /**
     * T<PERSON>h điểm trung bình
     */
    public function getAverageScoreAttribute(): float
    {
        $scores = array_filter([
            $this->experience_score,
            $this->skills_score,
            $this->overview_score
        ]);

        if (empty($scores)) {
            return 0.0;
        }

        return round(array_sum($scores) / count($scores), 2);
    }
}
