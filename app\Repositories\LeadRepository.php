<?php

namespace App\Repositories;

use App\Models\Cv;
use App\Models\Lead;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LeadRepository extends BaseRepository
{
    const MODEL = Lead::class;

    public function getDataBySource($start_date, $end_date)
    {
        if (!$start_date) {
            $start_date = Carbon::now()->subDays(30)->toDateString();
        }
        if (!$end_date) {
            $end_date = Carbon::now()->toDateString();
        }
//         SELECT COUNT(id),source, FLOOR((DayOfMonth(created_at)-1)/7)+1 AS week, 
// DATE_FORMAT(created_at , '%Y-%m') as month , id FROM leads
// GROUP BY source, FLOOR((DayOfMonth(created_at)-1)/7)+1, DATE_FORMAT(created_at , '%Y-%m');
        $data = $this->query()
        ->select(DB::raw('COUNT(id) as count'),'source', 'status', DB::raw('FLOOR((DayOfMonth(created_at)-1)/7)+1 AS week'), DB::raw('DATE_FORMAT(created_at , \'%Y-%m\') as month'))
        ->where('created_at', '>=', $start_date)
        ->where('created_at', '<=', $end_date . ' 23:59:59')
        ->groupBy(DB::raw('source, status, FLOOR((DayOfMonth(created_at)-1)/7)+1, DATE_FORMAT(created_at , \'%Y-%m\')'))
        ->get();
        
        return $data->toArray();
    }
    public function getDataByUser($start_date, $end_date)
    {
        if (!$start_date) {
            $start_date = Carbon::now()->subDays(30)->toDateString();
        }
        if (!$end_date) {
            $end_date = Carbon::now()->toDateString();
        }

        $data = DB::table('log_leads')
            ->select(
                DB::raw('COUNT(id) as count'),
                'user_id',
                'status',
                DB::raw('FLOOR((DayOfMonth(created_at)-1)/7)+1 AS week'),
                DB::raw('DATE_FORMAT(created_at , \'%Y-%m\') as month')
            )
            ->where('created_at', '>=', $start_date)
            ->where('created_at', '<=', $end_date . ' 23:59:59')
            ->groupBy(DB::raw('user_id, status, FLOOR((DayOfMonth(created_at)-1)/7)+1, DATE_FORMAT(created_at , \'%Y-%m\')'))
            ->get();
        
        return $data->toArray();
    }

    public function getCreatedLeadByUser($start_date, $end_date)
    {
        $data = $this->query()
        ->select(DB::raw('COUNT(id) as count'),'user_id',  DB::raw('FLOOR((DayOfMonth(created_at)-1)/7)+1 AS week'), DB::raw('DATE_FORMAT(created_at , \'%Y-%m\') as month'))
        ->where('created_at', '>=', $start_date)
        ->where('created_at', '<=', $end_date . ' 23:59:59')
        ->groupBy(DB::raw('user_id, FLOOR((DayOfMonth(created_at)-1)/7)+1, DATE_FORMAT(created_at , \'%Y-%m\')'))
        ->get();

        return $data->toArray();
    }

}
