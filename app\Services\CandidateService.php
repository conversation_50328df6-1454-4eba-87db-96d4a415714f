<?php

namespace App\Services;


use App\Models\Candidate;
use App\Models\Note;
use App\Models\User;
use Illuminate\Http\Request;

class CandidateService
{

    public function queryList(array $filters = [])
    {
        $limit = !empty($filters['limit']) ? $filters['limit'] : 10;
        return Candidate::query()
            ->when(!empty($filters['keyword']), function ($query) use ($filters) {
                $query->where('name', 'like', '%' . $filters['keyword'] . '%')->orWhere('email', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['job_id_apply']), function ($query) use ($filters) {
                $query->whereHas('applies', function ($q) use ($filters) {
                    $q->where('job_id', '<>', $filters['job_id_apply']);
                });
            })->limit($limit)
            ->latest('id');
    }

    public function findOrFail($id)
    {
        return Candidate::findOrFail($id);
    }

}
