/**
    This file declares css color variables for branding purposes.
    Attention, "./color-adjustments.css" depends on this file to properly function.
*/

@charset "UTF-8";
:root, [data-bs-theme=light], [data-menu-theme=light] {
    --tblr-gray: #4c496c;
    --tblr-gray-dark: #211d3b;
    --tblr-gray-100: #f1f5f9;
    --tblr-gray-200: #e2e8f0;
    --tblr-gray-300: #c8d3e1;
    --tblr-gray-400: #c9c1d6;
    --tblr-gray-500: #736c91;
    --tblr-gray-600: #4c496c;
    --tblr-gray-700: #333152;
    --tblr-gray-800: #211d3b;
    --tblr-gray-900: #1a0f2a;
    --tblr-primary: #7c69ef;
    --tblr-secondary: #66626c;
    --tblr-success: #42ba96;
    --tblr-info: #467fd0;
    --tblr-warning: #ffc107;
    --tblr-danger: #df4759;
    --tblr-light: #f8fafc;
    --tblr-dark: #241f2d;
    --tblr-muted: #66626c;
    --tblr-tabler: #7c69ef;
    --tblr-primary-rgb: 124, 105, 239;
    --tblr-secondary-rgb: 102, 98, 108;
    --tblr-success-rgb: 66, 186, 150;
    --tblr-info-rgb: 70, 127, 208;
    --tblr-warning-rgb: 255, 193, 7;
    --tblr-danger-rgb: 223, 71, 89;
    --tblr-light-rgb: 248, 250, 252;
    --tblr-dark-rgb: 36, 31, 45;
    --tblr-muted-rgb: 102, 98, 108;
    --tblr-tabler-rgb: 124, 105, 239;
    --tblr-primary-text-emphasis: #322a60;
    --tblr-secondary-text-emphasis: #42444b;
    --tblr-success-text-emphasis: #1a4a3c;
    --tblr-info-text-emphasis: #1c3353;
    --tblr-warning-text-emphasis: #664d03;
    --tblr-danger-text-emphasis: #591c24;
    --tblr-light-text-emphasis: #333152;
    --tblr-dark-text-emphasis: #333152;
    --tblr-primary-bg-subtle: #e5e1fc;
    --tblr-secondary-bg-subtle: #edeef1;
    --tblr-success-bg-subtle: #d9f1ea;
    --tblr-info-bg-subtle: #dae5f6;
    --tblr-warning-bg-subtle: #fff3cd;
    --tblr-danger-bg-subtle: #f9dade;
    --tblr-light-bg-subtle: #f8fafc;
    --tblr-dark-bg-subtle: #a09bbe;
    --tblr-primary-border-subtle: #cbc3f9;
    --tblr-secondary-border-subtle: #dbdde4;
    --tblr-success-border-subtle: #b3e3d5;
    --tblr-info-border-subtle: #b5ccec;
    --tblr-warning-border-subtle: #ffe69c;
    --tblr-danger-border-subtle: #f2b5bd;
    --tblr-light-border-subtle: #e2e8f0;
    --tblr-dark-border-subtle: #736c91;
    --tblr-body-font-size: 0.875rem;
    --tblr-body-font-weight: 400;
    --tblr-body-line-height: 1.4285714286;
    --tblr-body-color: #241f2d;
    --tblr-body-color-rgb: 36, 31, 45;
    --tblr-body-bg: #f1f5f9;
    --tblr-body-bg-rgb: 241, 245, 249;
    --tblr-emphasis-color: #000;
    --tblr-emphasis-color-rgb: 0, 0, 0;
    --tblr-secondary-color: rgba(36, 31, 45, .75);
    --tblr-secondary-color-rgb: 36, 31, 45;
    --tblr-secondary-bg: #e2e8f0;
    --tblr-secondary-bg-rgb: 226, 232, 240;
    --tblr-tertiary-color: rgba(36, 31, 45, .5);
    --tblr-tertiary-color-rgb: 36, 31, 45;
    --tblr-tertiary-bg: #f1f5f9;
    --tblr-tertiary-bg-rgb: 241, 245, 249;
    --tblr-link-color: #7c69ef;
    --tblr-link-color-rgb: 124, 105, 239;
    --tblr-link-hover-color: #6354bf;
    --tblr-link-hover-color-rgb: 99, 84, 191;
    --tblr-border-color: #e7e6e7;
    --tblr-border-color-translucent: rgba(102, 98, 108, .16);
    --tblr-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, .075);
    --tblr-focus-ring-width: 0.25rem;
    --tblr-focus-ring-opacity: 0.25;
    --tblr-focus-ring-color: rgba(124, 105, 239, .25);
    --tblr-form-valid-color: #42ba96;
    --tblr-form-valid-border-color: #42ba96;
    --tblr-form-invalid-color: #df4759;
    --tblr-form-invalid-border-color: #df4759;
    --tblr-danger-text: var(--tblr-danger);
    --tblr-navbar-border-color: transparent;
}

[data-bs-theme=dark], [data-menu-theme=dark] {
    --tblr-body-color: var(--tblr-gray-400);
    --tblr-body-color-rgb: 160, 155, 190;
    --tblr-body-bg: #181818;
    --tblr-body-bg-rgb: 24, 24, 24;
    --tblr-emphasis-color: #fff;
    --tblr-emphasis-color-rgb: 255, 255, 255;
    --tblr-secondary-color: rgba(160, 155, 190, .75);
    --tblr-secondary-color-rgb: 160, 155, 190;
    --tblr-secondary-bg: #211d3b;
    --tblr-secondary-bg-rgb: 33, 29, 59;
    --tblr-tertiary-color: rgba(160, 155, 190, .5);
    --tblr-tertiary-color-rgb: 160, 155, 190;
    --tblr-tertiary-bg: #1e1633;
    --tblr-tertiary-bg-rgb: 30, 22, 51;
    --tblr-primary-text-emphasis: #b0a5f5;
    --tblr-secondary-text-emphasis: #cacdd6;
    --tblr-success-text-emphasis: #8ed6c0;
    --tblr-info-text-emphasis: #90b2e3;
    --tblr-warning-text-emphasis: #ffda6a;
    --tblr-danger-text-emphasis: #ec919b;
    --tblr-light-text-emphasis: #f1f5f9;
    --tblr-dark-text-emphasis: #c8d3e1;
    --tblr-primary-bg-subtle: #191530;
    --tblr-secondary-bg-subtle: #212225;
    --tblr-success-bg-subtle: #0d251e;
    --tblr-info-bg-subtle: #0e192a;
    --tblr-warning-bg-subtle: #332701;
    --tblr-danger-bg-subtle: #2d0e12;
    --tblr-light-bg-subtle: #211d3b;
    --tblr-dark-bg-subtle: #110f1e;
    --tblr-primary-border-subtle: #383243;
    --tblr-secondary-border-subtle: #646770;
    --tblr-success-border-subtle: #28705a;
    --tblr-info-border-subtle: #2a4c7d;
    --tblr-warning-border-subtle: #997404;
    --tblr-danger-border-subtle: #862b35;
    --tblr-light-border-subtle: #29272f;
    --tblr-dark-border-subtle: #211d3b;
    --tblr-link-color: #b0a5f5;
    --tblr-link-hover-color: #c0b7f7;
    --tblr-link-color-rgb: 176, 165, 245;
    --tblr-link-hover-color-rgb: 192, 183, 247;
    --tblr-code-color: #b0a5f5;
    --tblr-border-color: #20202b;
    --tblr-border-color-translucent: var(--tblr-border-color);
    --tblr-form-valid-color: #93c29a;
    --tblr-form-valid-border-color: #93c29a;
    --tblr-form-invalid-color: #d69090;
    --tblr-form-invalid-border-color: #d69090;
    --tblr-bg-surface: #221e26;
    --tblr-navbar-bg: var(--tblr-bg-surface);
    --tblr-dark-mode-border-color: #302b33;
    --tblr-bg-main-color: #181818;
    --tblr-bg-forms: var(--tblr-dark-mode-border-color);
    --tblr-danger-text: var(--tblr-danger);
    --tblr-danger: #E25A6A;
    --tblr-danger-rgb: 226, 90, 106;
    --tblr-navbar-color: rgba(255, 255, 255, 0.7);
    --tblr-bg-surface-tertiary: #ffffff02;
    --tblr-navbar-border-color: transparent;
}
