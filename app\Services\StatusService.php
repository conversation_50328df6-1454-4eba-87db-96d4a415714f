<?php

namespace App\Services;


use App\Models\Note;
use App\Models\Status;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class StatusService
{

    public function getStatusWorkSize()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('work-site')->orderBy('order')->orderBy('id')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusCurrency()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('currency')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusGender()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('gender')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusPriorityJob()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('job-priority')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusTypeJob()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('job-type')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusJob()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('job-status')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusJobContract()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('job-contract')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusApplyJob()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('apply-job')->whereNotNull('parent_id')->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getAllStatusApplyJob()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('apply-job')->whereNull('parent_id')->orderBy('order')->with(['parent', 'children'])->get();
            });
    }

    public function getAllStatusCompany()
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__, CACHE_TTL, function () {
                return Status::whereGroup('company')->whereNull('parent_id')->orderBy('order')->orderBy('id')->with(['parent', 'children'])->get();
            });
    }

    public function getAllStatus($key, $isNullParent = false)
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__ . md5($key), CACHE_TTL, function () use ($key, $isNullParent) {
                return Status::whereGroup($key)->when($isNullParent, function ($query) {
                    $query->whereNull('parent_id');
                })->when(!$isNullParent, function ($query) {
                    $query->whereNotNull('parent_id');
                })->orderBy('order')->orderBy('id')->with(['parent', 'children'])->get();
            });
    }

    public function getStatus($key, $isNullParent = true)
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__ . md5($key . '-' . $isNullParent), CACHE_TTL, function () use ($key, $isNullParent) {
                return Status::whereGroup($key)->when($isNullParent, function ($query) {
                    $query->whereNull('parent_id');
                })->when(!$isNullParent, function ($query) {
                    $query->whereNotNull('parent_id');
                })->orderBy('order')->orderBy('id')->pluck('name', 'id')->toArray();
            });
    }

    public function getStatusWithSlug($key, $isNullParent = true)
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__ . md5($key . '-' . $isNullParent), CACHE_TTL, function () use ($key, $isNullParent) {
                return Status::whereGroup($key)->when($isNullParent, function ($query) {
                    $query->whereNull('parent_id');
                })->when(!$isNullParent, function ($query) {
                    $query->whereNotNull('parent_id');
                })->orderBy('order')->orderBy('id')->pluck('name', 'slug_value')->toArray();
            });
    }

    public function getDefaultStatus($key, $isNullParent = true)
    {
        return Cache::tags([Status::class])
            ->remember(Status::class . __FUNCTION__ . md5($key . '-' . $isNullParent), CACHE_TTL, function () use ($key, $isNullParent) {
                return Status::whereGroup($key)->when($isNullParent, function ($query) {
                    $query->whereNull('parent_id');
                })->when(!$isNullParent, function ($query) {
                    $query->whereNotNull('parent_id');
                })->orderBy('order')->orderBy('id')->first();
            });
    }

}
