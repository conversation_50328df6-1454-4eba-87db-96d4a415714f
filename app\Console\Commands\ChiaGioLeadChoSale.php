<?php

namespace App\Console\Commands;

use App\Models\Admin;
use App\Models\Company;
use App\Models\Employer;
use App\Models\Lead;
use App\Models\User;
use Illuminate\Console\Command;

class ChiaGioLeadChoSale extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:chia-gio-lead-cho-sale';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Chia gio lead cho sale';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Query ra 3 admin cần dùng để xoay vòng
        $admin_user = [
            70 => 'Nguyễn Ninh',
            67 => 'Lê Thị Hồng Lĩnh',
            53 => 'Nguyễn <PERSON>',
            14 => '<PERSON>uyễ<PERSON>',
        ];

        $adminIds = array_keys($admin_user);
        $admins = User::whereIn('id', $adminIds)->get();

        if ($admins->count() != count($adminIds)) {
            $this->error('Không tìm thấy đủ ' . count($adminIds) . ' admin với ID: ' . implode(', ', $adminIds));
            return 1;
        }

        // Update cho Công ty
        $this->info('Cập nhật cho Lead');
        // Lấy tất cả company có is_real = 1, theo thứ tự id tăng dần
        $leads = Lead::whereNull('user_id')
            ->where('email', '!=', '')
            ->whereNotNull('email')
            ->orderBy('id', 'asc')
            ->get();

        // dd($leads->count());
        $this->info('Tìm thấy ' . $leads->count() . ' leads có user_id = null');

        $bar = $this->output->createProgressBar($leads->count());
        $bar->start();

        // Cập nhật admin_id cho các company, xoay vòng
        $adminIndex = 0;
        $updatedCount = 0;

        foreach ($leads as $lead) {
            // Lấy admin_id hiện tại từ mảng adminIds
            $adminId = $adminIds[$adminIndex];

            // Cập nhật company
            $lead->user_id = $adminId;
            $lead->save();

            // Tăng index và reset nếu cần
            $adminIndex++;
            if ($adminIndex >= count($adminIds)) {
                $adminIndex = 0;
            }

            $updatedCount++;
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Đã cập nhật ' . $updatedCount . ' companies');

        return 0;
    }
}
