<?php

namespace App\Notifications;

use App\Channels\RingChannel;
use App\Helpers\Utils;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\TaskService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use League\HTMLToMarkdown\HtmlConverter;

class ApplyJobStatusChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        // return ['mail', RingChannel::class];
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $apply_job_log = clone $notifiable;
        $apply_job = $apply_job_log->apply_job;
        $converter = new HtmlConverter();
        $job = $apply_job->job;
        $company = $job->company;
        $candidate = $apply_job->candidate;
        $cv = $apply_job->cv;
        $note = $apply_job_log->note;
        $status = $apply_job->statusApply;
        if ($status) {
            $status_str = '**' . $status->name . '**';
            $status_parent = $status->parent;
            if ($status_parent) {
                $status_str .= ' (' . $status_parent->name . ')';
            }
        }
        // Utils::getUsernameFromEmail(trim(optional($task->created_by_user)->email))
        // $followers = User::whereIn('id', $task->followers->pluck('id'))->pluck('name')->toArray();
        // $follow_name = ($followers && count($followers)) ?  implode(', ', $followers) : '';
        $mail = (new MailMessage)
            ->subject('[Apply: #' . $apply_job->id . '] - Ứng tuyển vào job:: ' . $job->title . ' thay đổi trạng thái')
            ->line('**Thông tin ứng tuyển**')
            ->line('Ứng viên: **' . $candidate->name . '**')
            ->line('Vị trí: **' . $job->title . '**')
            ->line('Trạng thái: ' . $status_str)
            ->line('Ghi chú: ' . $note)
            ->line('CV: **' . $cv->name . '**');
        if (!$job->hide_to_recer) {
            $mail->line('Công ty: **' . $company->name . '**');
        }
        $mail->line('Điểm mạnh của ứng viên: **' . $converter->convert($apply_job->candidate_strengths) . '**');
        $mail->line('Người cập nhật: **' . Utils::getUsernameFromEmail(trim(optional($apply_job_log->user)->email)) . '**');
        $mail->action('Xem thông tin ứng tuyển', backpack_url('apply-job/' . $apply_job->id . '/show'));
        return $mail;
        // ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function toRing($notifiable)
    {
        if ($notifiable->task_id) {
            $logTaskService = new TaskService();
            $task = $logTaskService->find($notifiable->task_id);
            $datas = [];
            if ($task) {
                $users = User::where('id', $task->user_id)->orWhereIn('id', $task->followers->pluck('id'))->select('name', 'email', 'id')->get()->toArray();
                if (count($users) > 0) {
                    foreach ($users as $user) {
                        $datas[] = [
                            'user_id'    => $user['id'],
                            'is_watched' => 0,
                            'link'       => route('task.show', $task->id),
                            'content'    => 'Thông báo cập nhật Task **' . $task->name . '**',
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ];
                    }
                }
            }
            (new NotificationService())->insert($datas);
        }
    }
}
