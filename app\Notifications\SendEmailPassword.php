<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SendEmailPassword extends Notification
{
    // use Queueable;
    public $password;
    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('Tài khoản của bạn đã được reset mật khẩu')
                    ->line('Email: **' . $notifiable->email . '**')
                    ->line('Mật khẩu mới: **' . $this->password . '**')
                    ->action('Bấm vào đây để login', backpack_url('login'))
                    // ->line('Hướng dẫn 1: Chức năng đầu tiên có thể sử dụng trên CRM là chức năng **quản lý công việc (Task)**')
                    // ->line('- Người dùng có thể tự tạo task cho cá nhân, hoặc cấp trên có thể tạo task và assign cho cấp dưới.')
                    // ->line('- Khi tạo task nếu nhập thêm người theo dõi, thì mỗi khi task có thay đổi nội dung (như cập nhật trạng thái, comment thêm vào task) thì những người liên quan sẽ nhận được cập nhật qua email')
                    // ->line('Link truy cập danh sách task: ' . backpack_url('task'))
                    ->line('Nếu có lỗi vui lòng liên hệ team Dev để được xử lý, xin cám ơn');

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function setPassword(string $password)
    {
        $this->password = $password;
    }
}
