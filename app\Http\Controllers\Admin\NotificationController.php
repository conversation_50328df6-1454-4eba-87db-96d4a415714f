<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function changeNotify(Request $request)
    {
        try {
            if ($request->id) {
                $notify = Notification::findOrFail($request->id);
                $notify->is_watched = 1;
                $notify->save();
                return response()->json('success', 200);
            }
            if ($request->check_all) {
                Notification::query()->where('is_watched', 0)->update(['is_watched' => 1]);
                return response()->json('success', 200);
            }
            return  response()->json('error',400);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), $exception->getCode());
        }

    }

}
