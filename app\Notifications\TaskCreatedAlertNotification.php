<?php

namespace App\Notifications;

use App\Channels\RingChannel;
use App\Helpers\Utils;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\TaskService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use League\HTMLToMarkdown\HtmlConverter;

class TaskCreatedAlertNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', RingChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $task = clone $notifiable;
        $converter = new HtmlConverter();

        $followers = User::whereIn('id', $task->followers->pluck('id'))->pluck('name')->toArray();
        $follow_name = ($followers && count($followers)) ?  implode(', ', $followers) : '';
        $mail = (new MailMessage)
            ->subject('Task: #' . $task->id . ' - '. $task->name . '" mới được tạo' )
            ->line('Task **' . $task->name . '** mới được tạo bởi **' . Utils::getUsernameFromEmail(trim(optional($task->created_by_user)->email)) . '**')
            ->line('Người làm task: **' . Utils::getUsernameFromEmail(trim(optional($task->user)->email)) . '**');
        if ($follow_name) {
            $mail->line('Người theo dõi: **' . $follow_name . '**');
        }
        $mail->action('Xem thông tin task', backpack_url('task/' . $task->id . '/show'));
        return $mail;
                    // ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function toRing($notifiable)
    {
        if ($notifiable->task_id) {
            $logTaskService = new TaskService();
            $task = $logTaskService->find($notifiable->task_id);
            $datas = [];
            if ($task) {
                $users = User::where('id', $task->user_id)->orWhereIn('id', $task->followers->pluck('id'))->select('name', 'email', 'id')->get()->toArray();
                if (count($users) > 0) {
                    foreach ($users as $user) {
                        $datas[] = [
                            'user_id'    => $user['id'],
                            'is_watched' => 0,
                            'link'       => route('task.show', $task->id),
                            'content'    => 'Thông báo cập nhật Task **' . $task->name . '**',
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ];
                    }

                }
            }
            (new NotificationService())->insert($datas);
        }
    }
}
