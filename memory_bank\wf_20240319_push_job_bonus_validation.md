# Workflow: C<PERSON><PERSON> nhật validation cho bonus trong PushJobToReclandModal

## C<PERSON><PERSON> thay đổi cần thực hiện:
1. Disable mặc định input bonus
2. Enable input bonus khi đủ 4 điều kiện:
   - career_id có giá trị
   - skill_id có giá trị  
   - level_id có giá trị
   - recruitment_type_id có giá trị
3. Gọi API để lấy giá thấp nhất cho phép
4. Hi<PERSON><PERSON> thị thông báo giá thấp nhất
5. Validate bonus phải lớn hơn giá thấp nhất

## Files cần sửa:
- resources/js/components/Admin/Job/PushJobToReclandModal.vue 