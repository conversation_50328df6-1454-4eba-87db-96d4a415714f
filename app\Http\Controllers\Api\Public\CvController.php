<?php

namespace App\Http\Controllers\Api\Public;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Admin\ChangeStatusLeadRequest;
use App\Http\Requests\Api\Public\CvRequest;
use App\Http\Resources\Api\Admin\LeadResource;
use App\Jobs\DownloadAttactment;
use App\Jobs\DownloadCvAttactment;
use App\Models\Candidate;
use App\Models\Cv;
use App\Models\Status;
use App\Services\LeadService;
use App\Services\LogLeadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;


class CvController extends Controller
{
    protected $leadService;
    protected $logLeadService;

    public function __construct(LogLeadService $logLeadService, LeadService $leadService)
    {
        $this->logLeadService = $logLeadService;
        $this->leadService = $leadService;
    }

    public function createCv(CvRequest $request)
    {
        $data_key = [
            'name', # require
            'email', # require
            'dob',
            'gender',
            'mobile', # require
            'university',
            'address', # require
            'facebook',
            'linkedin',
            'job_title', # require
            'yoe',
            'academic_level',
            'currency',
            'salary_expect',
            'salary_current',
            'level',
            'language',
            'skill',
            'old_company',
            'is_open',
            'can_contact',
            // 'candidate_id',
            'work_site',
            'cv_public',
            'cv_private',
            'source_name',
            'source_id',
            'force_update_candidate',
            'attachment',
            'comment',
            'created_at',
            'updated_at',
        ];
        $data = $request->only($data_key);

        foreach ($data_key as $key) {
            if (!isset($data[$key])) {
                $data[$key] = '';
            }
        }
        $data['created_at'] = empty($data['created_at']) ? date('Y-m-d H:i:s') : date('Y-m-d H:i:s', strtotime($data['created_at']));
        $data['updated_at'] = empty($data['updated_at']) ? date('Y-m-d H:i:s') : date('Y-m-d H:i:s', strtotime($data['updated_at']));


        $data['mobile'] = Utils::cleanPhone($data['mobile']);
        if (Str::length($data['mobile']) > 10) {
            $data['mobile'] = Utils::getTenNumberPhone($data['mobile']);
        }
        $data['name'] = Str::title(strtolower($data['name']));
        // dd($data);
        $candidate = Candidate::where('mobile', $data['mobile'])->first();
        $data['dob'] = empty($data['dob']) ? null : date('Y-m-d', strtotime($data['dob']));
        $data_candidate = [
            'name'        => $data['name'],
            'email'       => $data['email'],
            'mobile'       => $data['mobile'],
            'dob'         => $data['dob'],
            'gender'      => $data['gender'],
            'university'  => $data['university'],
            'address'     => $data['address'],
            'facebook'    => $data['facebook'],
            'linkedin'    => $data['linkedin'],
            'old_company' => $data['old_company'],
        ];
        DB::beginTransaction();

        if (!$candidate) {
            $candidate = Candidate::create($data_candidate);
            $candidate->raw_data()->create(['raw_data' => $data]);
        } elseif ($candidate && isset($data['force_update_candidate']) && $data['force_update_candidate'] == 1) {
            $candidate->update($data_candidate);
        }

        $data['candidate_id'] = $candidate->id;

        # create CV
        $cvs = null;
        $data['source_id'] = isset($data['source_id']) ? $data['source_id'] : null;
        if (isset($data['source_name']) && $data['source_name'] && $data['source_id']) {
            $cvs = Cv::where('source_name', $data['source_name'])->where('source_id', $data['source_id'])->first();
        }
        $data['skill_describe'] = Str::limit($data['skill'], 250);
        $work_site              = $data['work_site'];
        $skills                 = $data['skill'];
        $academic_level         = $data['academic_level'];
        $level                  = $data['level'];
        $gender                 = $data['gender'];
        $language               = $data['language'];

        # xu ly du lieu truoc khi update vao database
        unset($data['work_site']);
        unset($data['skill']);
        unset($data['academic_level']);
        unset($data['level']);
        unset($data['language']);

        $work_site = explode(',', $work_site);
        // get first work site
        $work_site = count($work_site) ? $work_site[0] : $work_site;
        $work_site = Status::where('group', 'work-site')->where('slug_value', $work_site)->first();
        if ($work_site) {
            $data['work_site'] = $work_site->id;
        }

        if (!in_array($data['gender'], [1, 2])) {
            unset($data['gender']);
        }
        if (!in_array($data['is_open'], [0, 1])) {
            unset($data['is_open']);
        }
        if (!in_array($data['can_contact'], [0, 1])) {
            unset($data['can_contact']);
        }
        $data['yoe'] = empty($data['yoe']) ? null : intval($data['yoe']);

        # check if level is string then get id from table level, then set to data['level']
        if (isset($level) && is_string($level) && intval($level) == 0) {
            $level = explode(',', $level);
            $level = count($level) ? $level[0] : $level;
            $level = \App\Models\CareerLevel::where('slug', $level)->first();
            if ($level) {
                $data['level'] = $level->id;
            }
        }
        if ($academic_level) {
            $academic_level = \App\Models\AcademicLevel::where('slug', $academic_level)->first();
            if ($academic_level) {
                $data['academic_level'] = $academic_level->id;
            }
        }
        $is_created_cv = false;
        if (intval($data['source_id']) == 0) {
            $data['source_id'] = null;
        }
        if (!$cvs) {
            $is_created_cv = true;
            $cvs = Cv::create($data);
            // $cvs = Cv::where('cv_public', $data['cv_public'])
            //     ->orWhere('cv_private',$data['cv_private'])->first();
            DownloadCvAttactment::dispatch($cvs);
        } elseif ($cvs && isset($data['force_update_candidate']) && $data['force_update_candidate'] == 1) {
            $cvs->update($data);
        }
        // print_r($cvs->toArray());
        // die;

        # check skill is array and exists in table skill, if not exists, create new skill. Then link to cv
        if ($skills) {
            if (!is_array($skills)) {
                $skills = explode(',', $skills);
            }
            foreach ($skills as $skill_name) {
                $skill = \App\Models\Skill::where('name', $skill_name)->first();
                if (!$skill) {
                    continue;
                    // $skill = \App\Models\Skill::create(['name' => $skill_name]);
                }
                $cvs->skills()->syncWithoutDetaching([$skill->id]);
            }
        }
        # check skill is array and exists in table skill, if not exists, create new skill. Then link to cv
        if (isset($language)  && $language) {
            // $languages = $language;
            if (!is_array($language)) {
                $language = explode(',', $language);
            }
            foreach ($language as $lang_name) {
                $lang = \App\Models\CareerLanguage::where('slug', $lang_name)->first();
                if ($lang) {
                    // $lang = \App\Models\CareerLanguage::create(['name_en' => $lang_name]);
                    $cvs->careerLanguages()->syncWithoutDetaching([$lang->id]);
                }
            }
        }

        # check if cv is created, the save metadata
        if ($is_created_cv) {
            $cvs->raw_data()->create(['raw_data' => $data]);
            # create attachment
            if (isset($data['attachment']) && is_array($data['attachment']) && count($data['attachment']) > 0) {
                foreach ($data['attachment'] as $attact_path) {
                    $attachment = $candidate->attachment()->create(['path' => $attact_path]);
                    $attachment->raw_data()->create(['raw_data' => [$attact_path]]);
                    DownloadAttactment::dispatch($attachment);
                }
            }

            # create comment
            if (isset($data['comment']) && is_array($data['comment']) && count($data['comment']) > 0) {
                foreach ($data['comment'] as $comment) {
                    $com = $candidate->comment()->create(['content' => $comment['content'], 'created_at' => $comment['created_at']]);
                    $com->raw_data()->create(['raw_data' => $comment]);
                }
            }
        } elseif (isset($data['force_update_candidate']) && $data['force_update_candidate'] == 1) {
            $cvs->raw_data()->update(['raw_data' => $data]);
            # insert not exists attachment & comment
            if (isset($data['attachment']) && is_array($data['attachment']) && count($data['attachment']) > 0) {
                // dd($data['attachment']);
                # get old attachment of cvs
                $old_attachment = $cvs->attachment;
                $old_attachment_candidate = $candidate->attachment;
                $old_attachment_path = [];
                if (count($old_attachment) > 0) {
                    foreach ($old_attachment as $att) {
                        $atta_raw = $att->raw_data;
                        if ($atta_raw) {
                            $atta_raw_path = $atta_raw->raw_data[0];
                            $old_attachment_path[] = $atta_raw_path;
                        }
                    }
                }
                if (count($old_attachment_candidate) > 0) {
                    foreach ($old_attachment_candidate as $att) {
                        $atta_raw = $att->raw_data;
                        if ($atta_raw) {
                            $atta_raw_path = $atta_raw->raw_data[0];
                            $old_attachment_path[] = $atta_raw_path;
                        }
                    }
                }
                // dd($old_attachment_path);

                foreach ($data['attachment'] as $attact_path) {
                    if (!in_array($attact_path, $old_attachment_path)) {
                        // $attachment = $cvs->attachment()->create(['path' => $attact_path]);
                        $attachment = $candidate->attachment()->create(['path' => $attact_path]);
                        $attachment->raw_data()->create(['raw_data' => [$attact_path]]);
                        DownloadAttactment::dispatch($attachment);
                    }
                }
            }

            if (isset($data['comment']) && is_array($data['comment']) && count($data['comment']) > 0) {
                # get old comment of cvs
                $old_comment = $cvs->comment;
                $old_comment_content = $old_comment->pluck('content')->toArray();
                $old_comment = $candidate->comment;
                $old_comment_content = array_merge($old_comment_content, $old_comment->pluck('content')->toArray());
                foreach ($data['comment'] as $comment) {
                    if (!in_array($comment['content'], $old_comment_content)) {
                        $com = $candidate->comment()->create(['content' => $comment['content'], 'created_at' => $comment['created_at']]);
                        $com->raw_data()->create(['raw_data' => $comment]);
                    }
                }
            }
        }

        try {
            DB::commit();
            return response()->json(['success' => 1, 'data' => ['id' => $cvs->id, 'candidate_id' => $candidate->id]]);
        } catch (\Exception $e) {
            # write log
            Log::error($e->getMessage(), ['exception' => $e]);
            DB::rollBack();
            return response()->json(['success' => 0, 'message' => $e->getMessage()], $e->getCode());
        }
    }
}
