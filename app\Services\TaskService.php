<?php

namespace App\Services;


use App\Models\Lead;
use App\Models\Task;


class TaskService
{
    public function update($id, array $data)
    {
        $lead = Task::findOrFail($id);
        $lead->update($data);
        return $lead;
    }

    public function findOrFail($id)
    {
        return Task::findOrFail($id);
    }

    public function find($id){
        return Task::find($id);
    }
}
