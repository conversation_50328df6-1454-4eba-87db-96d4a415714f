<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\MeetingRoom;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MeetingRoomController extends Controller
{
    /**
     * Display a listing of active meeting rooms.
     */
    public function index(): JsonResponse
    {
        $meetingRooms = MeetingRoom::where('is_active', true)
            ->select('id', 'name', 'location', 'capacity', 'description')
            ->orderBy('name')
            ->get();

        return response()->json($meetingRooms);
    }

    /**
     * Display the specified meeting room.
     */
    public function show(MeetingRoom $meetingRoom): JsonResponse
    {
        return response()->json($meetingRoom);
    }

    /**
     * Check availability of a meeting room for a specific time range.
     */
    public function checkAvailability(Request $request, MeetingRoom $meetingRoom): JsonResponse
    {
        $request->validate([
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'exclude_booking_id' => 'nullable|integer'
        ]);

        $isAvailable = $meetingRoom->isAvailable(
            $request->start_time,
            $request->end_time,
            $request->exclude_booking_id
        );

        return response()->json([
            'available' => $isAvailable,
            'room' => $meetingRoom->only(['id', 'name', 'location', 'capacity'])
        ]);
    }

    /**
     * Get all meeting rooms with their availability status for a specific time range.
     */
    public function getAvailableRooms(Request $request): JsonResponse
    {
        $request->validate([
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'exclude_booking_id' => 'nullable|integer'
        ]);

        $meetingRooms = MeetingRoom::where('is_active', true)
            ->select('id', 'name', 'location', 'capacity', 'description')
            ->orderBy('name')
            ->get();

        $roomsWithAvailability = $meetingRooms->map(function ($room) use ($request) {
            $isAvailable = $room->isAvailable(
                $request->start_time,
                $request->end_time,
                $request->exclude_booking_id
            );

            return [
                'id' => $room->id,
                'name' => $room->name,
                'location' => $room->location,
                'capacity' => $room->capacity,
                'description' => $room->description,
                'available' => $isAvailable
            ];
        });

        return response()->json($roomsWithAvailability);
    }
}
