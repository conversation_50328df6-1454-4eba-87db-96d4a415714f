# Workflow: Thêm cột vào chức năng export cho Apply Job

## <PERSON><PERSON><PERSON> thực hiện: 29/04/2024

## <PERSON><PERSON> tả

Thêm 3 cột sau vào chức năng export của Apply Job trong Laravel Backpack:

-   Trạng thái
-   Ghi chú của trạng thái cuối (chỉ hiển thị trong export)
-   <PERSON><PERSON><PERSON>uyể<PERSON> (ngày tạo) (chỉ hiển thị trong export)

## Các tác vụ đã thực hiện

### 1. Thêm cột trạng thái

Đã thêm cột trạng thái (status_export) vào chức năng export, hiển thị thông tin trạng thái cha và trạng thái con (nếu có).

```php
CRUD::addColumn([
    'name' => 'status_export',
    'label' => 'Trạng thái',
    'type' => 'closure',
    'function' => function ($entry) {
        if ($entry->statusApply) {
            $statusParent = $entry->statusApply->where('id', $entry->statusApply->parent_id)->first();
            if ($statusParent) {
                return $statusParent->name . ' - ' . $entry->statusApply->name;
            }
            return $entry->statusApply->name;
        }
        return '';
    },
    'exportOnlyColumn' => true,
    'visibleInTable' => false,
]);
```

### 2. Thêm cột ghi chú trạng thái cuối

Đã thêm cột ghi chú trạng thái cuối (last_status_note) vào chức năng export, lấy thông tin ghi chú từ bản ghi LogChangeStatusApply mới nhất.

```php
CRUD::addColumn([
    'name' => 'last_status_note',
    'label' => 'Ghi chú của trạng thái cuối',
    'type' => 'closure',
    'function' => function ($entry) {
        $latestLog = \App\Models\LogChangeStatusApply::where('apply_job_id', $entry->id)
            ->latest('id')
            ->first();
        return $latestLog ? $latestLog->note : '';
    },
    'exportOnlyColumn' => true,
    'visibleInTable' => false,
]);
```

### 3. Thêm cột ngày ứng tuyển (ngày tạo)

Đã thêm cột ngày ứng tuyển (created_at) vào chức năng export.

```php
CRUD::addColumn([
    'name' => 'created_at',
    'label' => 'Ngày ứng tuyển',
    'type' => 'date',
    'format' => 'Y-m-d H:i:s',
    'exportOnlyColumn' => true,
    'visibleInTable' => false,
]);
```

## File đã sửa

-   app/Http/Controllers/Admin/ApplyJobCrudController.php
