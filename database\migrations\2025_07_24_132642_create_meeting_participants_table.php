<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('meeting_booking_id')->constrained('meeting_bookings')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('status', ['invited', 'accepted', 'declined', 'tentative'])->default('invited');
            $table->boolean('is_organizer')->default(false);
            $table->boolean('email_sent')->default(false);
            $table->timestamps();
            
            // Unique constraint to prevent duplicate participants
            $table->unique(['meeting_booking_id', 'user_id']);
            
            // Index for faster queries
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_participants');
    }
};
