<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('log_change_status_applies', function (Blueprint $table) {
            $table->id();
            $table->integer('status_id');
            $table->integer('apply_job_id');
            $table->datetime('date_status')->nullable();
            $table->integer('user_id');
            $table->text('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('log_change_status_applies');
    }
};
