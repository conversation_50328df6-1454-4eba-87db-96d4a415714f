<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Admin\TaskResource;
use App\Http\Resources\Api\Admin\LogTaskResource;
use App\Services\LogTaskService;
use App\Services\TaskService;
use Illuminate\Http\Request;

class TaskController extends Controller
{

    protected $logTaskService;
    protected $taskService;

    public function __construct(LogTaskService $logTaskService, TaskService $taskService)
    {
        $this->logTaskService = $logTaskService;
        $this->taskService = $taskService;
    }

    public function createLog(Request $request)
    {
        $this->logTaskService->create([
            'task_id'    => $request->task_id,
            'created_by' => backpack_auth()->id(),
            'content'    => request('content'),
        ]);
        return response()->json(['success' => 'Cập nhật dữ liệu thành công']);

    }

    public function changeStatus($id, Request $request)
    {
        try {
            $this->taskService->update($id, ['status_id' => $request->status]);
            return response()->json(['success' => 'Cập nhật dữ liệu thành công']);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], $e->getCode());
        }


    }

    public function getDetail($id)
    {
        return response()->json(['data' => new TaskResource($this->taskService->findOrFail($id))]);
    }

    public function getLogTask($id)
    {
        return response()->json(['data' => LogTaskResource::collection($this->logTaskService->queryList(['task_id' => $id])->get())]);
    }

}
