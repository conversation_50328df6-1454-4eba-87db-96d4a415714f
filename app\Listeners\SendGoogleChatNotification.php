<?php

namespace App\Listeners;

use App\Events\ApplyJobCreatedEvent;
use App\Notifications\NewFilePrivateApplyJobGoogleChatNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendGoogleChatNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ApplyJobCreatedEvent $event): void
    {
        $applyJob = $event->apply_job;

        // G<PERSON><PERSON> thông báo đến Google Chat
        $applyJob->notify(new NewFilePrivateApplyJobGoogleChatNotification($applyJob));
    }
}
