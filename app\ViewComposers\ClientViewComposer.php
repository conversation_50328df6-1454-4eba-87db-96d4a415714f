<?php

namespace App\ViewComposers;

use App\Models\Notification;
use Illuminate\View\View;

class ClientViewComposer
{
    /** Binding data to view
     *
     * @param  View  $view
     *
     * @return View
     * @throws \Exception
     * */
    public function compose(View $view)
    {
       $notifications = Notification::where('user_id',backpack_auth()->id())->limit(10)->orderByDesc('id')->get();
        return $view->with('notifications',$notifications);
    }
}
