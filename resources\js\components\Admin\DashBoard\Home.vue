<template>
    <LineChart id="app"  style="max-height: 400px; padding-bottom: 50px" />
    <b-card>
        <div class=" 2">
            <h2 style="font-weight: 600">Top 20 Apply Job <span class="badge bg-danger">New</span></h2>
            <table class="table">
                <thead>
                <tr>
                    <td>#</td>
                    <td>Candidate</td>
                    <td>Job title</td>
                    <td>Company</td>
                    <td>Status</td>
                    <td>Updated at</td>
                    <td>Users updated by</td>
                    <td>Note</td>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(log, index) in listApplyJob" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ log.name }}</td>
                    <td>{{ log.job_title }}</td>
                    <td>{{ log.company_name }}</td>
                    <td>{{ log.status_name }}</td>
                    <td>{{ moment(log.updated_at).format('DD/MM/YYYY hh:mm A') }}</td>
                    <td>{{ log.users_updated_by }}</td>
                    <td>{{ log.note }}</td>
                </tr>

                </tbody>
            </table>
        </div>
    </b-card>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';
import LineChart from './LineChart.vue';
import {Line} from "vue-chartjs";


export default {
    components: {Line, Loading , LineChart },
    props: {
        applyJobs: {
            required: true,
        },
        // user_id: {
        //     required: true,
        // },
        // company_id:{
        //     required:true
        // },
        // contact_id:{
        //     required:true
        // },
        // status:{
        //     required:true
        // },
        // content:{
        //     required:true
        // }
    },
    data() {
        return {
            moment: moment,
            showModal: false,
            listApplyJob: [],
            isLoading: true,
            fullPage: true,
            keyStatus: null,
            // dataSubmit: {
            //     time_contact: null,
            //     content: null,
            //     lead_id: this.lead_id,
            //     user_id: this.user_id,
            //     company_id: this.company_id,
            //     contact_id: this.contact_id,
            //     status: this.status,
            // },
            config: {
                enableTime: true,
                static: true,
                allowInput: true,
                disableMobile: true,
                wrap: true,
                time_24hr: true,
                altFormat: 'd/m/Y H:i',
                altInput: true,
                dateFormat: 'Y-m-d H:i',
            },
            // error: {
            //     status: null,
            //     time_contact: null,
            //     note: null,
            // },

        }

    },
    methods: {
        getApplyJob() {
            this.isLoading = true;
            axios.get('/api/top-applyjob-dashboard').then(res => {
                this.listApplyJob = res.data.data;
                this.isLoading = false;
            }).catch(err => {
                console.error(err);
                alert('Có lỗi xảy ra khi tải dữ liệu');
                this.isLoading = false;
            });
        },
    },

    mounted() {
        this.getApplyJob();
    }
}


</script>
<style  scoped>
#app {
    font-family: "Avenir", Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* text-align: center; */
    color: #2c3e50;
    /* margin-top: 60px; */
}
</style>
