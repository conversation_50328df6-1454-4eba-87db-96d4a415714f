<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('skills', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->index();
            $table->string('slug')->index();
            $table->text('concept_en')->nullable();
            $table->text('concept_vi')->nullable();
            $table->string('img_logo')->nullable();
            $table->boolean('is_popular')->default(false);
            $table->unsignedInteger('order')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['name', 'slug']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('skills');
    }
};
