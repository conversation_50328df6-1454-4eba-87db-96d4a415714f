<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Admin\ChangeStatusLeadRequest;
use App\Http\Resources\Api\Admin\LeadResource;
use App\Http\Resources\Api\Admin\LogLeadResource;
use App\Http\Resources\Api\Admin\TaskResource;
use App\Services\LeadService;
use App\Services\LogLeadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LeadController extends Controller
{
    protected $leadService;
    protected $logLeadService;

    public function __construct(LogLeadService $logLeadService, LeadService $leadService)
    {
        $this->logLeadService = $logLeadService;
        $this->leadService = $leadService;
    }


    public function changeStatus($id, Request $request)
    {
        DB::beginTransaction();
        try {
            $this->leadService->update($id, ['status' => $request->status]);
            DB::commit();
            return response()->json(['success' => 'Cập nhật dữ liệu thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()],500);
        }

    }
    public function changeUser(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $lead = $this->leadService->find($id);
            if (!$lead->canChangeOwner()) {
                return response()->json(['message' => 'Không thể chuyển người quản lý'], 500);
            }
            $this->leadService->update($id, ['user_id' => $request->user_id]);
            DB::commit();
            $lead->notify(new \App\Notifications\LeadChangeUser());
            return response()->json(['success' => 'Cập nhật dữ liệu thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()],500);
        }

    }

    public function getLogLead($id)
    {
        // dd($this->logLeadService->queryList(['lead_id' => $id])->get());
        // return response()->json(['data' => LeadResource::collection($this->logLeadService->queryList(['lead_id' => $id])->get())]);
        $logs = $this->logLeadService->queryList(['lead_id' => $id])->get();
        return response()->json(['data' => LogLeadResource::collection($logs)]);
    }

    public function getData($id)
    {
        $lead = $this->leadService->find($id);
        return response()->json(['data' => new LeadResource($lead)]);
    }

    public function createLogLead(Request $request)
    {
        $this->logLeadService->create([
            'lead_id'      => $request->lead_id,
            'user_id'      => backpack_auth()->id(),
            'company_id'   => $request->company_id,
            'content'      => request('content'),
            'contact_id'   => $request->contact_id,
            'status'       => $request->status,
            'time_contact' => $request->time_contact,
        ]);
        return response()->json(['success' => 'Cập nhật dữ liệu thành công']);
    }
}
