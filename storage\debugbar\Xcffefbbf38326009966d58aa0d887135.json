{"__meta": {"id": "Xcffefbbf38326009966d58aa0d887135", "datetime": "2025-08-05 17:32:31", "utime": **********.186773, "method": "GET", "uri": "/api/created-apply-job-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.563247, "end": **********.186791, "duration": 0.6235439777374268, "duration_str": "624ms", "measures": [{"label": "Booting", "start": **********.563247, "relative_start": 0, "end": **********.873062, "relative_end": **********.873062, "duration": 0.3098149299621582, "duration_str": "310ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.873074, "relative_start": 0.3098270893096924, "end": **********.186793, "relative_end": 2.1457672119140625e-06, "duration": 0.3137190341949463, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30787568, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/created-apply-job-chart", "middleware": "api, web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\Api\\Admin\\ApplyJobController@getApplyJobChart", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FApi%2FAdmin%2FApplyJobController.php&line=55\" onclick=\"\">app/Http/Controllers/Api/Admin/ApplyJobController.php:55-58</a>"}, "queries": {"nb_statements": 85, "nb_visible_statements": 86, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12594999999999995, "accumulated_duration_str": "126ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.932986, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.947612, "duration": 0.02032, "duration_str": "20.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 16.133}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-01' and '2024-01-07'", "type": "query", "params": [], "bindings": ["2024-01-01", "2024-01-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9801831, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 16.133, "width_percent": 2.04}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-08' and '2024-01-14'", "type": "query", "params": [], "bindings": ["2024-01-08", "2024-01-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.984565, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 18.174, "width_percent": 1.826}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-15' and '2024-01-21'", "type": "query", "params": [], "bindings": ["2024-01-15", "2024-01-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9879591, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 20, "width_percent": 1.763}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-22' and '2024-01-28'", "type": "query", "params": [], "bindings": ["2024-01-22", "2024-01-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9912708, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 21.763, "width_percent": 1.739}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-29' and '2024-02-04'", "type": "query", "params": [], "bindings": ["2024-01-29", "2024-02-04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.994549, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 23.501, "width_percent": 1.104}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-05' and '2024-02-11'", "type": "query", "params": [], "bindings": ["2024-02-05", "2024-02-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.997041, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 24.605, "width_percent": 0.818}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-12' and '2024-02-18'", "type": "query", "params": [], "bindings": ["2024-02-12", "2024-02-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.999172, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 25.423, "width_percent": 0.818}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-19' and '2024-02-25'", "type": "query", "params": [], "bindings": ["2024-02-19", "2024-02-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.001239, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 26.241, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-26' and '2024-03-03'", "type": "query", "params": [], "bindings": ["2024-02-26", "2024-03-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0033572, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 27.05, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-04' and '2024-03-10'", "type": "query", "params": [], "bindings": ["2024-03-04", "2024-03-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.005454, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 27.836, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-11' and '2024-03-17'", "type": "query", "params": [], "bindings": ["2024-03-11", "2024-03-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.007527, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 28.622, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-18' and '2024-03-24'", "type": "query", "params": [], "bindings": ["2024-03-18", "2024-03-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.009604, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 29.408, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-25' and '2024-03-31'", "type": "query", "params": [], "bindings": ["2024-03-25", "2024-03-31"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.011707, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 30.218, "width_percent": 0.802}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-01' and '2024-04-07'", "type": "query", "params": [], "bindings": ["2024-04-01", "2024-04-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0138102, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 31.02, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-08' and '2024-04-14'", "type": "query", "params": [], "bindings": ["2024-04-08", "2024-04-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.015862, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 31.83, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-15' and '2024-04-21'", "type": "query", "params": [], "bindings": ["2024-04-15", "2024-04-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.017977, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 32.624, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-22' and '2024-04-28'", "type": "query", "params": [], "bindings": ["2024-04-22", "2024-04-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.020112, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 33.434, "width_percent": 0.881}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-29' and '2024-05-05'", "type": "query", "params": [], "bindings": ["2024-04-29", "2024-05-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.022319, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 34.315, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-06' and '2024-05-12'", "type": "query", "params": [], "bindings": ["2024-05-06", "2024-05-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.024446, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 35.125, "width_percent": 1.85}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-13' and '2024-05-19'", "type": "query", "params": [], "bindings": ["2024-05-13", "2024-05-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.027883, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 36.975, "width_percent": 0.826}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-20' and '2024-05-26'", "type": "query", "params": [], "bindings": ["2024-05-20", "2024-05-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.030221, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 37.801, "width_percent": 0.85}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-27' and '2024-06-02'", "type": "query", "params": [], "bindings": ["2024-05-27", "2024-06-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.032577, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 38.65, "width_percent": 0.873}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-03' and '2024-06-09'", "type": "query", "params": [], "bindings": ["2024-06-03", "2024-06-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.034873, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 39.524, "width_percent": 0.826}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-10' and '2024-06-16'", "type": "query", "params": [], "bindings": ["2024-06-10", "2024-06-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.03702, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 40.349, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-17' and '2024-06-23'", "type": "query", "params": [], "bindings": ["2024-06-17", "2024-06-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.039077, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 41.135, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-24' and '2024-06-30'", "type": "query", "params": [], "bindings": ["2024-06-24", "2024-06-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0411282, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 41.913, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-01' and '2024-07-07'", "type": "query", "params": [], "bindings": ["2024-07-01", "2024-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.043236, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 42.692, "width_percent": 0.857}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-08' and '2024-07-14'", "type": "query", "params": [], "bindings": ["2024-07-08", "2024-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.046518, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 43.549, "width_percent": 0.802}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-15' and '2024-07-21'", "type": "query", "params": [], "bindings": ["2024-07-15", "2024-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.04861, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 44.351, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-22' and '2024-07-28'", "type": "query", "params": [], "bindings": ["2024-07-22", "2024-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.050724, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 45.161, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-29' and '2024-08-04'", "type": "query", "params": [], "bindings": ["2024-07-29", "2024-08-04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0528939, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 45.971, "width_percent": 0.85}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-05' and '2024-08-11'", "type": "query", "params": [], "bindings": ["2024-08-05", "2024-08-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0557501, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 46.82, "width_percent": 1.755}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-12' and '2024-08-18'", "type": "query", "params": [], "bindings": ["2024-08-12", "2024-08-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0589762, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 48.575, "width_percent": 1.747}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-19' and '2024-08-25'", "type": "query", "params": [], "bindings": ["2024-08-19", "2024-08-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.062269, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 50.322, "width_percent": 1.707}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-26' and '2024-09-01'", "type": "query", "params": [], "bindings": ["2024-08-26", "2024-09-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.065507, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 52.029, "width_percent": 1.723}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-02' and '2024-09-08'", "type": "query", "params": [], "bindings": ["2024-09-02", "2024-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0687559, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 53.751, "width_percent": 1.707}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-09' and '2024-09-15'", "type": "query", "params": [], "bindings": ["2024-09-09", "2024-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.071981, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 55.459, "width_percent": 1.715}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-16' and '2024-09-22'", "type": "query", "params": [], "bindings": ["2024-09-16", "2024-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.075212, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 57.173, "width_percent": 1.731}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-23' and '2024-09-29'", "type": "query", "params": [], "bindings": ["2024-09-23", "2024-09-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.078481, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 58.904, "width_percent": 1.699}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-30' and '2024-10-06'", "type": "query", "params": [], "bindings": ["2024-09-30", "2024-10-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0816948, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 60.603, "width_percent": 1.731}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-07' and '2024-10-13'", "type": "query", "params": [], "bindings": ["2024-10-07", "2024-10-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.084977, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 62.334, "width_percent": 1.016}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-14' and '2024-10-20'", "type": "query", "params": [], "bindings": ["2024-10-14", "2024-10-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0873382, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 63.351, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-21' and '2024-10-27'", "type": "query", "params": [], "bindings": ["2024-10-21", "2024-10-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0893831, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 64.129, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-28' and '2024-11-03'", "type": "query", "params": [], "bindings": ["2024-10-28", "2024-11-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.091465, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 64.915, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-04' and '2024-11-10'", "type": "query", "params": [], "bindings": ["2024-11-04", "2024-11-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.093528, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 65.701, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-11' and '2024-11-17'", "type": "query", "params": [], "bindings": ["2024-11-11", "2024-11-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0955842, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 66.479, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-18' and '2024-11-24'", "type": "query", "params": [], "bindings": ["2024-11-18", "2024-11-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.097649, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 67.265, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-25' and '2024-12-01'", "type": "query", "params": [], "bindings": ["2024-11-25", "2024-12-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0997012, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 68.043, "width_percent": 0.802}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-02' and '2024-12-08'", "type": "query", "params": [], "bindings": ["2024-12-02", "2024-12-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.101802, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 68.845, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-09' and '2024-12-15'", "type": "query", "params": [], "bindings": ["2024-12-09", "2024-12-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.103868, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 69.639, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-16' and '2024-12-22'", "type": "query", "params": [], "bindings": ["2024-12-16", "2024-12-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.105925, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 70.417, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-23' and '2024-12-29'", "type": "query", "params": [], "bindings": ["2024-12-23", "2024-12-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.107975, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 71.195, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-30' and '2025-01-05'", "type": "query", "params": [], "bindings": ["2024-12-30", "2025-01-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.110059, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 71.973, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-06' and '2025-01-12'", "type": "query", "params": [], "bindings": ["2025-01-06", "2025-01-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.112111, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 72.751, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-13' and '2025-01-19'", "type": "query", "params": [], "bindings": ["2025-01-13", "2025-01-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.114152, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 73.529, "width_percent": 0.857}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-20' and '2025-01-26'", "type": "query", "params": [], "bindings": ["2025-01-20", "2025-01-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.116307, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 74.387, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-27' and '2025-02-02'", "type": "query", "params": [], "bindings": ["2025-01-27", "2025-02-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.118405, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 75.181, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-03' and '2025-02-09'", "type": "query", "params": [], "bindings": ["2025-02-03", "2025-02-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.12047, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 75.967, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-10' and '2025-02-16'", "type": "query", "params": [], "bindings": ["2025-02-10", "2025-02-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.122587, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 76.761, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-17' and '2025-02-23'", "type": "query", "params": [], "bindings": ["2025-02-17", "2025-02-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.124694, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 77.57, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-24' and '2025-03-02'", "type": "query", "params": [], "bindings": ["2025-02-24", "2025-03-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1267412, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 78.349, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-03' and '2025-03-09'", "type": "query", "params": [], "bindings": ["2025-03-03", "2025-03-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.128819, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 79.127, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-10' and '2025-03-16'", "type": "query", "params": [], "bindings": ["2025-03-10", "2025-03-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.130872, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 79.905, "width_percent": 0.77}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-17' and '2025-03-23'", "type": "query", "params": [], "bindings": ["2025-03-17", "2025-03-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1329072, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 80.675, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-24' and '2025-03-30'", "type": "query", "params": [], "bindings": ["2025-03-24", "2025-03-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1350172, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 81.461, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-31' and '2025-04-06'", "type": "query", "params": [], "bindings": ["2025-03-31", "2025-04-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1371, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 82.247, "width_percent": 1.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-07' and '2025-04-13'", "type": "query", "params": [], "bindings": ["2025-04-07", "2025-04-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1404388, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 84.041, "width_percent": 1.731}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-14' and '2025-04-20'", "type": "query", "params": [], "bindings": ["2025-04-14", "2025-04-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.143698, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 85.772, "width_percent": 1.524}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-21' and '2025-04-27'", "type": "query", "params": [], "bindings": ["2025-04-21", "2025-04-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.146681, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 87.297, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-28' and '2025-05-04'", "type": "query", "params": [], "bindings": ["2025-04-28", "2025-05-04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1487432, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 88.083, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-05' and '2025-05-11'", "type": "query", "params": [], "bindings": ["2025-05-05", "2025-05-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.150853, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 88.861, "width_percent": 0.826}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-12' and '2025-05-18'", "type": "query", "params": [], "bindings": ["2025-05-12", "2025-05-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.152995, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 89.686, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-19' and '2025-05-25'", "type": "query", "params": [], "bindings": ["2025-05-19", "2025-05-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.155116, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 90.472, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-26' and '2025-06-01'", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.157186, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 91.258, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-02' and '2025-06-08'", "type": "query", "params": [], "bindings": ["2025-06-02", "2025-06-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.159256, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 92.044, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-09' and '2025-06-15'", "type": "query", "params": [], "bindings": ["2025-06-09", "2025-06-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.161318, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 92.83, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-16' and '2025-06-22'", "type": "query", "params": [], "bindings": ["2025-06-16", "2025-06-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1633742, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 93.609, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-23' and '2025-06-29'", "type": "query", "params": [], "bindings": ["2025-06-23", "2025-06-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.165426, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 94.387, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-30' and '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-06-30", "2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.167503, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 95.173, "width_percent": 0.826}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-07' and '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-07", "2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.169634, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 95.998, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-14' and '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-14", "2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1717172, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 96.792, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-21' and '2025-07-27'", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.173786, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 97.586, "width_percent": 0.826}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-28' and '2025-08-03'", "type": "query", "params": [], "bindings": ["2025-07-28", "2025-08-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.175884, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 98.412, "width_percent": 0.802}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-08-04' and '2025-08-10'", "type": "query", "params": [], "bindings": ["2025-08-04", "2025-08-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.177987, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 99.214, "width_percent": 0.786}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/api/created-apply-job-chart\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/api/created-apply-job-chart", "status_code": "<pre class=sf-dump id=sf-dump-1218854731 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1218854731\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1460170014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1460170014\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-45414561 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-45414561\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-308501965 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRzOFEwSjFtWitTb3VwRVZPV1dsMWc9PSIsInZhbHVlIjoiZzY3Rzc4UzBvOHJPdGpCeDJ2YU5JMmpENE1tSHM3dHk0VFd3c3JER3JtWmVEZGxZc0pDbmJYK1kzU1ltaDZkQkhadGFwdXhoWWk4WUZRU1RPc3BwdzFUekJBbXhGeXo4VVBNaWZGTzdpVlFFSUxJT1RldGhaNzgxaGdDNEg3ZE4iLCJtYWMiOiJiYWY5ZDRlYjY0MDVmOTc4OGU2YTA4NjMwMTZkNjQ4NjJkNGZjZDFlMDZkMjQ3YWJjNzkxNmYzYTQ5MzE1M2NkIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6ImJ6cFVFM01GMlhMRXg2bnkxMFJuVlE9PSIsInZhbHVlIjoiQnJrbVhNbkk2TFFQMnZjNWxwWm9JRlZjZGpWWVpvT1ZBU29uQThFUEtBKzBxSWE5MzV6MXlpQmliSHNQL3Y4aitIVnY2OTNsc0dQTVJzNSs3VWFvdUdJdWJPd040d0x6Nkh1RlVOZlo1N3ptcFR0Q09hZzBwdEJiU2JqZEhmd1ciLCJtYWMiOiIyNjU3Y2IxZTE0YmFlZWNmNDc0MzUwNzc5ZDVjOTc1MjMwMzA1NjhlZjAwYTQ3YzA0MzE5YTA2NTlhYTU0MTZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-308501965\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1703470957 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMTD4fIgcGCh8kCgEEAr8zN2Jp5KvwJtujpSk7Vb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703470957\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1533642314 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 10:32:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImE2ZXJBTjFoenJ4OXBybUUrTzlYRWc9PSIsInZhbHVlIjoiTTF1Uy93b21BejBTQ2xCNEQxQ0NUVjBvSlQySUlOaFVVRHNnK3A2S05uTE45L2tySnVRWmpVRGo0RktweWRUOEJ0WS9CQWlBNG5HeFVmdG1PSFNYaXg2U0lQMGxmYkQwdnhia1lDaFJnT1JWa2FRRXNLRUpwbXdtNk5XdEZ5bnoiLCJtYWMiOiJhM2JlMDI5NTdkYzc4YTViNzIzYmFjNTliYWFjNzJmNGZiZDRiN2FhMmIzNzA4ZDY4MzY3YWY3ZjhmMmYyZjMwIiwidGFnIjoiIn0%3D; expires=Tue, 05 Aug 2025 12:32:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IkZLNysvTjVhMFR1OUttVFlMQjhtVUE9PSIsInZhbHVlIjoiV3V6ZGdlRmJZM1NhRE9vSWJWWDNubmxpY1lnNklVRUNoNDdGSEl2T0tzVm1WanJYWXZUYjNxbElzN0J3YTJ5alJ6OUE4d0w5alVMY0NZL05qd0UrdEl4QU81dUU3SUxneHRkSXlsdEw0NkJTeFJuRmhMTWVhTW9BTkZtUllJUS8iLCJtYWMiOiJkNmE5M2MxNWVmZjZkMTZlMDZjODA2NTk4ZGFlN2Y0N2Y2MzhkMzE5NjI1OWRlMGZjOTY1MmM2OTJjNmJlMDQ4IiwidGFnIjoiIn0%3D; expires=Tue, 05 Aug 2025 12:32:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImE2ZXJBTjFoenJ4OXBybUUrTzlYRWc9PSIsInZhbHVlIjoiTTF1Uy93b21BejBTQ2xCNEQxQ0NUVjBvSlQySUlOaFVVRHNnK3A2S05uTE45L2tySnVRWmpVRGo0RktweWRUOEJ0WS9CQWlBNG5HeFVmdG1PSFNYaXg2U0lQMGxmYkQwdnhia1lDaFJnT1JWa2FRRXNLRUpwbXdtNk5XdEZ5bnoiLCJtYWMiOiJhM2JlMDI5NTdkYzc4YTViNzIzYmFjNTliYWFjNzJmNGZiZDRiN2FhMmIzNzA4ZDY4MzY3YWY3ZjhmMmYyZjMwIiwidGFnIjoiIn0%3D; expires=Tue, 05-Aug-2025 12:32:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IkZLNysvTjVhMFR1OUttVFlMQjhtVUE9PSIsInZhbHVlIjoiV3V6ZGdlRmJZM1NhRE9vSWJWWDNubmxpY1lnNklVRUNoNDdGSEl2T0tzVm1WanJYWXZUYjNxbElzN0J3YTJ5alJ6OUE4d0w5alVMY0NZL05qd0UrdEl4QU81dUU3SUxneHRkSXlsdEw0NkJTeFJuRmhMTWVhTW9BTkZtUllJUS8iLCJtYWMiOiJkNmE5M2MxNWVmZjZkMTZlMDZjODA2NTk4ZGFlN2Y0N2Y2MzhkMzE5NjI1OWRlMGZjOTY1MmM2OTJjNmJlMDQ4IiwidGFnIjoiIn0%3D; expires=Tue, 05-Aug-2025 12:32:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533642314\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1476794269 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://chri.local/api/created-apply-job-chart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476794269\", {\"maxDepth\":0})</script>\n"}}