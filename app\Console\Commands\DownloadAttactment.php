<?php

namespace App\Console\Commands;

use App\Jobs\DownloadAttactment as JobsDownloadAttactment;
use App\Models\Attachment;
use Illuminate\Console\Command;

class DownloadAttactment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:download-attactment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $attact = Attachment::find(2);
        JobsDownloadAttactment::dispatch($attact);
    }
}
