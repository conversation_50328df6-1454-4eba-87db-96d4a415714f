<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Company extends Model  implements AuditableContract
{
    use CrudTrait,HasFactory, Auditable;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'companies';

    protected $guarded = ['id'];


    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->created_by)) {
                $model->created_by = backpack_auth()->id();
            }
        });
    }
    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function contact()
    {
        return $this->hasMany(Contact::class, 'company_id', 'id');
    }

    public function job()
    {
        return $this->hasMany(Job::class, 'company_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeRoleData($query)
    {
        $userIds = [backpack_user()->id];

        return $query->where(function ($q) use ($userIds) {
            if (backpack_user()->can('company.all-data')) {
                return $q;
            } else {
                if (backpack_user()->can('company.only-company')) {
                    $userIds = User::where('internal_company_id', backpack_user()->internal_company_id)->pluck('id')->toArray();
                } elseif (backpack_user()->can('company.only-team')) {
                    $userIds = User::where('department_id', backpack_user()->department_id)->pluck('id')->toArray();
                };
                return $q->whereIn('created_by', $userIds);
            }
        });
    }
    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
