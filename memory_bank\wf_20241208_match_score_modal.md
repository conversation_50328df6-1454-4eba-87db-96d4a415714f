# Workflow: <PERSON><PERSON><PERSON>dal Đ<PERSON>h Giá <PERSON>ổng <PERSON>uan (Match Score)

**<PERSON><PERSON><PERSON> thực hiện**: 08/12/2024  
**File được sửa đổi**: `resources/views/admins/jobs/show.blade.php`

## Mục tiêu

<PERSON>o function `showMatchScore(id)` để hiển thị modal đánh giá tổng quan ứng viên với dữ liệu từ `match_score_cvs.raw_data`.

## Các thay đổi đã thực hiện

### 1. Thêm Modal HTML

-   **Vị trí**: Sau `@endsection` và trước `@section('before_breadcrumbs_widgets')`
-   **Modal ID**: `match-score-modal`
-   **Tính năng**:
    -   Header với tiêu đề "Đánh giá tổng quan"
    -   Phần điểm tổng quan với circle score
    -   Hai cột: Kỹ năng và Kinh nghiệm
    -   Mỗi phần có điểm mạnh (✓) và điểm cần cải thiện (✗)

### 2. Cập nhật Function JavaScript

#### Function `showMatchScore(id)`

-   Gọi API: `{{ route("api.cv.getMatchScore", ":id") }}`
-   Xử lý response và parse JSON từ `raw_data`
-   Hiển thị modal sau khi load dữ liệu thành công
-   Xử lý lỗi với thông báo Noty

#### Function `populateMatchScoreModal(data)`

-   Cập nhật điểm tổng quan (`data.overview`)
-   Cập nhật kỹ năng (`data.skills`)
-   Cập nhật kinh nghiệm (`data.experience`)
-   Xử lý danh sách advantages và disadvantages

#### Function `updateScoreCircleColor(selector, score)`

-   Phân loại điểm số:
    -   > = 80: score-high (xanh lá)
    -   > = 60: score-medium (vàng)
    -   < 60: score-low (đỏ)

#### Function `updateScoreBadgeColor(selector, score)`

-   Cập nhật màu badge theo điểm số
-   Tương tự như score circle

### 3. Thêm CSS Styling

-   **Score Circle**: Hiệu ứng gradient và màu sắc theo điểm số
-   **Evaluation Section**: Box styling với border và background
-   **Modal Body**: Max height và scroll
-   **Icons**: Màu sắc cho success và danger icons

## Cấu trúc dữ liệu JSON được xử lý

```json
{
    "skills": {
        "advantage": ["..."],
        "disadvantages": ["..."],
        "score": 75
    },
    "experience": {
        "advantage": ["..."],
        "disadvantages": ["..."],
        "score": 70
    },
    "overview": {
        "score": 73,
        "description": "..."
    }
}
```

## Cách sử dụng

Gọi function `showMatchScore(cvId)` với ID của CV để hiển thị modal đánh giá.

## Route API cần thiết

Cần tạo route `api.cv.getMatchScore` trả về dữ liệu với field `raw_data` chứa JSON như cấu trúc trên.
