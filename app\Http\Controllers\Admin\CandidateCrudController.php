<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\CandidateRequest;
use App\Models\Candidate;
use App\Models\Skill;
use App\Models\Status;
use App\Services\CandidateService;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Can;

/**
 * Class CandidateCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class CandidateCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;

    protected $statusService;


    public function __construct(StatusService $statusService)
    {
        $this->statusService = $statusService;
        parent::__construct();
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Candidate::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/candidate');
        CRUD::setEntityNameStrings('candidate', 'candidates');
        CRUD::denyAccess('delete');
        CRUD::addClause('outSide');
        if (!backpack_user()->can('candidate.index')) {
            CRUD::denyAccess('list');
        }
        if (!backpack_user()->can('candidate.edit')) {
            CRUD::denyAccess('update');
        }
        if (!backpack_user()->can('candidate.create')) {
            CRUD::denyAccess('create');
        }
        if (!backpack_user()->can('candidate.show')) {
            CRUD::denyAccess('show');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {

        //        CRUD::setFromDb(); // set columns from db columns.
        $this->crud->addClause('roleData');
        $this->filterData();
        $this->crud->column('id')->label('ID')->type('text');
        $this->crud->column('name')->label('Name')->type('text');
        $this->crud->column('email')->label('Email')->type('text');
        $this->crud->column([
            'name' => 'dob', // The db column name
            'label' => 'Date of birth', // Table column heading
            'type' => 'datetime',
            'format' => 'l ', // use something else than the base.default_datetime_format config value
        ]);
        $this->crud->removeColumn('gender');
        CRUD::column('gender')->label('Gender')->value(function ($item) {
            return $this->statusService->getStatusGender()[$item->gender] ?? '';
        });
        $this->crud->column('created_at');
        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(CandidateRequest::class);
        // CRUD::setFromDb(); // set fields from db columns.

        //        CRUD::field([   // select2_from_array
        //            'name' => 'gender',
        //            'label' => "Gender",
        //            'type' => 'select2_from_array',
        //            'options' => $this->statusService->getStatusGender(),
        //            'allows_null' => true,
        //        ]);
        CRUD::addFields($this->fieldData());

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function findModelById($id)
    {
        $model = \App\Models\Candidate::findOrFail($id);
        return response()->json($model);
    }

    protected function setupShowOperation()
    {
        $this->setupListOperation();
        $this->crud->setShowView('admins.candidates.show');
        Widget::add()->type('script')->content('assets/js/admin/custom.js');
    }

    public function ajaxSearch(Request $request)
    {
        $data = $request->all();
        $data['limit'] = 20;
        return response()->json((new CandidateService())->queryList($data)->selectRaw('id,CONCAT(name, " - ",email) as name')->get());
    }

    protected function fieldData()
    {
        $validateIgnoreEmail =  !empty(request('id')) ? ',email,' . request('id') : '';
        $validateIgnoreMobile =  !empty(request('id')) ? ',mobile,' . request('id') : '';
        return [
            fieldColumnData('name', 'Name', 'text', 'form-group col-md-6', 'required'),
            fieldColumnData('email', 'Email', 'text', 'form-group col-md-3', 'required|unique:candidates' . $validateIgnoreEmail),
            //            fieldColumnData('mobile', 'Mobile', 'number', 'form-group col-md-3', ['required', 'regex:/^(\+?84|0)(\d{9,10})$/', 'unique:candidates' . $validateIgnoreMobile]),
            fieldColumnData('mobile', 'Mobile', 'number', 'form-group col-md-3', ['required', 'unique:candidates' . $validateIgnoreMobile]),
            selectFormArrayData('gender', 'Gender', 'form-group col-md-2',  $this->statusService->getStatusGender(), 'required'),
            fieldColumnData('dob', 'Dob', 'date', 'form-group col-md-2', 'required'),
            fieldColumnData('address', 'Address', 'text', 'form-group col-md-4'),
            fieldColumnData('university', 'University', 'text', 'form-group col-md-4'),
            fieldColumnData('facebook', 'Facebook', 'text', 'form-group col-md-4'),
            fieldColumnData('linkedin', 'Linkedin', 'text', 'form-group col-md-4'),
            fieldColumnData('old_company', 'Old company', 'text', 'form-group col-md-4'),
        ];
    }

    public function filterData()
    {

        $this->crud->addFilter(
            [
                'name' => 'email',
                'type' => 'text',
                'label' => 'Email'
            ],
            false,
            function ($value) {
                $this->crud->query->where('email', 'like', '%' . $value . '%');
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'mobile',
                'type' => 'text',
                'label' => 'Mobile'
            ],
            false,
            function ($value) {
                $this->crud->query->where('mobile', 'like', '%' . $value . '%');
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'skills',
                'type' => 'select2_multiple',
                'label' => 'Skill'
            ],
            function () {
                return Skill::all()->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('cvs', function ($query) use ($value) {
                    $query->whereHas('skills', function ($query) use ($value) {
                        $query->whereIn('skill_id', json_decode($value));
                    });
                });
            }
        );


        $this->crud->addFilter(
            [
                'name' => 'yoe',
                'type' => 'select2_multiple',
                'label' => 'Yoe'
            ],
            function () {
                $yoe = [];
                for ($i = 1; $i <= 10; $i++) {
                    $yoe[$i] = $i;
                }
                return $yoe;
            },
            function ($value) {
                $this->crud->query->whereHas('cvs', function ($query) use ($value) {
                    if (in_array(10, json_decode($value))) {
                        $query->where('yoe', '>=', 10);
                    } else {
                        $query->whereIn('yoe', json_decode($value));
                    }
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'work_site',
                'type' => 'select2_multiple',
                'label' => 'Work site'
            ],
            function () {
                return  Status::where('group', 'work-site')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('cvs', function ($query) use ($value) {
                    $query->whereHas('statusWorkSite', function ($query) use ($value) {
                        $query->whereIn('id', json_decode($value));
                    });
                });
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'created_by',
                'type' => 'select2',
                'label' => 'Người đăng'
            ],
            function () {
                return \App\Models\User::pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->addClause('where', 'created_by', $value);
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'date_range',
                'type' => 'date_range',
                'label' => 'Khoảng thời gian'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                $this->crud->addClause('where', 'created_at', '>=', $dates->from);
                $this->crud->addClause('where', 'created_at', '<=', $dates->to . ' 23:59:59');
            }
        );
    }
}
