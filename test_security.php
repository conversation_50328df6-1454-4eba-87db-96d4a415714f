<?php

require_once 'vendor/autoload.php';

use App\Services\FileSecurityService;

// Test basic functionality
echo "Testing FileSecurityService...\n";

// Test rate limit check
$result = FileSecurityService::checkRateLimit('127.0.0.1');
echo "Rate limit check for 127.0.0.1: " . ($result ? "OK" : "Exceeded") . "\n";

// Test hash generation
$hash = FileSecurityService::generateHash(1, 'Cv', 'cv_public');
echo "Generated hash: " . $hash . "\n";

// Test hash verification
$isValid = FileSecurityService::verifyHash(1, 'Cv', 'cv_public', $hash);
echo "Hash verification: " . ($isValid ? "Valid" : "Invalid") . "\n";

echo "All tests completed!\n";