<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SetPermisionToRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-permision-to-role {permision} {role?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set permision to role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $permision = $this->argument('permision');
        $role = $this->argument('role');

        if ($permision) {
            $role = str_replace(', ', ',', $role);
            $permision = explode(',', $permision);
            $permisions = \Spatie\Permission\Models\Permission::whereIn('name', $permision)->get();
        } else {
            $this->error('Please enter permision you want to set');
            return;
        }

        if ($role) {
            $role = str_replace(', ', ',', $role);
            $role = explode(',', $role);
            $roles = \Spatie\Permission\Models\Role::whereIn('id', $role)->get();
        } else {
            $roles = \Spatie\Permission\Models\Role::all();
        }


        foreach ($roles as $role) {
            foreach ($permisions as $per) {
                $role->givePermissionTo($per);
                $this->info('Set permision ' . $per->name . ' to role ' . $role->name . ' successfully');
            }
        }
    }
}
