@extends(backpack_view('blank'))



@section('content')
    <div class="col-12" id="app">
        <h2>Report Lead</h2>
        <form>
            <div class="card">
                <div class="card-body row">
                    <div class="col-md-3">
                        <div class="form-group row">
                            <label for="start_date" class="col-sm-4 col-form-label">Ng<PERSON>y bắt đầu: </label>
                            <div class="col-sm-8">
                                <input type="date" id="start_date" name="start_date" class="form-control"
                                    value="{{ request('start_date') }}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group row">
                            <label for="end_date" class="col-sm-4 col-form-label"><PERSON><PERSON><PERSON> kết thúc</label>
                            <div class="col-sm-8">
                                <input type="date" id="end_date" name="end_date" class="form-control"
                                    value="{{ request('end_date') }}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="card mt-2">
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th rowspan="2">NGUỒN</th>
                        <th rowspan="2">Trạng thái</th>
                        @if (count($months) > 0)
                            @foreach ($months as $month)
                                <th colspan="{{ $max_week_of_month[$month] }}">{{ $month }}</th>
                            @endforeach
                        @endif
                    </tr>
                    <tr>
                        @foreach ($months as $month)
                            @for ($i = 1; $i <= $max_week_of_month[$month]; $i++)
                                <th>Tuần {{ $i }}</th>
                            @endfor
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach ($all_source as $source_key => $source_value)
                        @if (!isset($status_of_source[$source_key]) || count($status_of_source[$source_key]) == 0)
                            @continue
                        @endif
                        @php
                            $index_row = 0;
                        @endphp
                        @foreach ($status_of_source[$source_key] as $status_key => $status_value)
                            <tr>
                                @if ($index_row == 0)
                                    <td rowspan="{{ count($status_of_source[$source_key]) }}">{{ $source_value }}</td>
                                    @php
                                        $index_row = 1;
                                    @endphp
                                @endif
                                <td>{{ $status_value }}</td>
                                @if (count($months) > 0)
                                    @foreach ($months as $month)
                                        @for ($i = 1; $i <= $max_week_of_month[$month]; $i++)
                                            <td>
                                                @if (isset($data_by_source[$source_key][$status_key][$month][$i]))
                                                    {{ $data_by_source[$source_key][$status_key][$month][$i] }}
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        @endfor
                                    @endforeach
                                @endif
                            </tr>
                        @endforeach
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@endsection
@section('after_scripts')
    {{-- @vite('resources/js/app.js') --}}
@endsection
