<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Requests\TaskRequest;
use App\Models\Status;
use App\Models\SubTasks;
use App\Models\Task;
use App\Models\User;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Mockery\Exception;
use Illuminate\Support\Str;

/**
 * Class TaskCrudController
 *
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class TaskCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation {
        store as traitStore;
    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;


    protected $statusService;

    public function __construct(StatusService $statusService)
    {
        $this->statusService = $statusService;
        parent::__construct();
    }


    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Task::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/task');
        CRUD::setEntityNameStrings('task', 'tasks');
        CRUD::denyAccess('delete');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        try {
            // CRUD::setFromDb();
            $this->crud->addClause('roleData');
            $this->crud->addColumns($this->columnData());
            $this->filterData();
            /**
             * Columns can be defined using the fluent syntax:
             * - CRUD::column('price')->type('number');
             */
        } catch (\Exception $exception) {
            abort($exception->getCode() ? $exception->getCode() : 500, $exception->getMessage());
        }
    }

    // create function to generate columns include id, name, description, status, priority, start_date, end_date, user_id
    protected function columnData()
    {
        $status_completed = Status::where('group', 'status-task')->where('slug_value', 'completed')->first();
        return [
            [
                'name' => 'id',
                'label' => 'ID',
                'type' => 'text',
            ],
            [
                'name' => 'name',
                'label' => 'Tên task',
                'type' => 'custom_html',
                'value' => function ($entry) {
                    return '<strong>' . $entry->name . '</strong><br><sub>' . Str::limit(strip_tags($entry->description), 100) . '</sub>';
                }],
            // [
            //     'name' => 'description',
            //     'label' => 'Mô tả',
            //     'type' => 'closure',
            //     'function' => function ($entry) {
            //         return Str::limit(strip_tags($entry->description), 5);
            //     }
            // ],
            // [
            //     'name' => 'status',
            //     'label' => 'Trạng thái',
            //     'type' => 'relationship',
            //     'entity' => 'status', // the method that defines the relationship in your Model
            //     'attribute' => 'name', // foreign key attribute that is shown to user
            // ],
            // [
            //     'name' => 'priority',
            //     'label' => 'Độ ưu tiên',
            //     'type' => 'relationship',
            //     'entity' => 'priorityTask', // the method that defines the relationship in your Model
            //     'attribute' => 'name', // foreign key attribute that is shown to user
            // ],
            [
                'name' => 'priority',
                'label' => 'Trạng thái/Độ ưu tiên',
                'type' => 'custom_html',
                'value' => function ($entry) {
                    return '<strong>' . $entry->status->name . '</strong><br><sub >Ưu tiên: ' . $entry->priorityTask->name . '</sub>';
                }
            ],
            // [
            //     'name' => 'start_date',
            //     'label' => 'Ngày bắt đầu',
            //     'type' => 'date',
            //     'format' => 'DD/MM/YYYY',
            // ],
            [
                'name' => 'end_date',
                'label' => 'Hạn hoàn thành',
                'type' => 'custom_html',
                'value' => function ($entry) use ($status_completed) {
                    $str = Carbon::parse($entry->end_date)->format('d/m/Y');
                    if ($entry->end_date < Carbon::yesterday()->endOfDay() && $entry->status_id != $status_completed->id) {
                        $str = '<span class="text-danger fw-bold ">' . $str . '</span> <span class="badge bg-danger">Quá hạn</span>';
                    }
                    return $str;
                    // return '<input type="date" name="end_date" class="form-control col-md-12" value="' . $entry->end_date . '">';
                }
            ],
            // [
            //     'name' => 'user_id', // name of relationship method in the model
            //     'type' => 'relationship',
            //     'entity' => 'user', // the method that defines the relationship in your Model
            //     'attribute' => 'email', // foreign key attribute that is shown to user
            //     'label' => 'Người thực hiện', // Table column heading
            // ],
            [
                'name' => 'user_id',
                'label' => 'Người thực hiện',
                'type' => 'custom_html',
                'value' => function ($entry) {
                    return Utils::getUsernameFromEmail($entry->user->email);
                }
            ],
            [
                'name' => 'created_by',
                'label' => 'Người tạo task',
                'type' => 'custom_html',
                'value' => function ($entry) {
                    return '<strong>' . Utils::getUsernameFromEmail($entry->created_by_user->email) . '</strong><br>' . Carbon::parse($entry->start_date)->format('d/m/Y');
                }
            ],
        ];
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(TaskRequest::class);
        Widget::add([
            'type' => 'style',
            'content' => 'assets/css/admin/custom.css',
        ]);
        CRUD::addFields($this->fieldData());
        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    protected function fieldData()
    {

        return [
            fieldColumnData('name', 'Tên task', 'text', 'form-group col-md-12', 'required'),
            [   // repeatable
                'name' => 'subtasks',
                'label' => 'Sub Task',
                'type' => 'repeatable',
                'subfields' => [ // also works as: "fields"
                    fieldColumnData('title', 'Tên task', 'text', 'form-group col-md-6', 'required'),
                    selectFormArrayData('status_id', 'Trạng thái', 'form-group col-md-3', $this->statusService->getStatus('status-task', true), 'required'),
                ]
            ],
            selectFormArrayData('service_id', 'Dịch vụ/sản phẩm', 'form-group col-md-3', $this->statusService->getStatus('service-name', true), null),
            selectFormArrayData('priority', 'Độ ưu tiên', 'form-group col-md-3', $this->statusService->getStatus('priority-task', true), 'required'),
            fieldColumnData('start_date', 'Ngày bắt đầu', 'date_picker', 'form-group col-md-3', 'required'),
            fieldColumnData('end_date', 'Ngày hoàn thành', 'date_picker', 'form-group col-md-3'),
            selectFormArrayData('status_id', 'Trạng thái', 'form-group col-md-3', $this->statusService->getStatus('status-task', true), 'required'),
            [
                'type' => "relationship",
                'name' => 'user',
                'label' => 'Người thực hiện',
                'ajax' => true,
                'placeholder' => 'Choose option',
                'minimum_input_length' => 0,
                'default' => backpack_user()->id,
                'attribute' => 'email',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'validationRules' => [
                    'required',
                ],
            ], [
                'type' => "relationship",
                'name' => 'followers',
                'label' => 'Người theo dõi',
                'ajax' => true,
                'placeholder' => 'Choose option',
                'minimum_input_length' => 0,
                'attribute' => 'email',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'validationRules' => [
                    // 'required',
                ],
            ],
            [
                'name' => 'description',
                'label' => 'Description',
                // 'type' => 'ckeditor',
                'type' => 'tinymce',
                'options' => [
                    // 'config' => [
                    //     'height' => 200,
                    // ],
                    'height' => 300,
                    // 'autoGrow_minHeight' => 200,
                    // 'autoGrow_bottomSpace' => 50,
                ],

            ],
        ];
    }

    public function fetchFollowers()
    {
        $query = request()->get('q');
        return $this->fetch([
            'model' => User::class, // required
            'searchable_attributes' => [],
            'paginate' => 10, // items to show per page
            // 'searchOperator' => 'ILIKE',
            'query' => function ($model) use ($query) {
                // return $model->where('is_active', 1);
                // return $model;
                return $model->where('email', 'like', '%' . $query . '%')->select('id', 'email');
            } // to filter the results that are returned
        ]);
        // return $this->fetch(User::class);
    }


    public function fetchUser()
    {
        $query = User::query()->when(request('q'), function ($query) {
            $query->where('name', 'like', '%' . request('q') . '%');
        });
        $query->where('status', 1);
        return $query->get();


        if (backpack_user()->can('task.view.all-data')) {
            return $query->get();
        }

        if (backpack_user()->can('task.view.only-company')) {
            return $query->where('internal_company_id',
                backpack_user()->internal_company_id)->get();
        }

        if (backpack_user()->can('task.view.only-team')) {
            return $query->where('department_id',
                backpack_user()->department_id)->get();
        }

        return $query->where('id', backpack_user()->id)->get();
    }

    protected function setupShowOperation()
    {
        $this->crud->setShowView('admins.task.show');
        Widget::add([
            'type' => 'style',
            'content' => 'assets/css/admin/custom.css',
        ]);
    }

    public function store(Request $request)
    {
        // do something before validation, before save, before everything; for example:
        // $this->crud->addField(['type' => 'hidden', 'name' => 'author_id']);
        // $this->crud->removeField('password_confirmation');

        // Note: By default Backpack ONLY saves the inputs that were added on page using Backpack fields.
        // This is done by stripping the request of all inputs that do NOT match Backpack fields for this
        // particular operation. This is an added security layer, to protect your database from malicious
        // users who could theoretically add inputs using DeveloperTools or JavaScript. If you're not properly
        // using $guarded or $fillable on your model, malicious inputs could get you into trouble.

        // However, if you know you have proper $guarded or $fillable on your model, and you want to manipulate
        // the request directly to add or remove request parameters, you can also do that.
        // We have a config value you can set, either inside your operation in `config/backpack/crud.php` if
        // you want it to apply to all CRUDs, or inside a particular CrudController:
        // $this->crud->setOperationSetting('saveAllInputsExcept', ['_token', '_method', 'http_referrer', 'current_tab', 'save_action']);
        // The above will make Backpack store all inputs EXCEPT for the ones it uses for various features.
        // So you can manipulate the request and add any request variable you'd like.
        // $this->crud->getRequest()->request->add(['author_id'=> backpack_user()->id]);
        // $this->crud->getRequest()->request->remove('password_confirmation');

        $response = $this->traitStore();
        $task = $this->crud->getCurrentEntry();
        event(new \App\Events\TaskCreated($task));


        // do something after save
        return $response;
    }

    public function filterData()
    {
        $this->crud->addFilter(
            [
                'name' => 'created_by',
                'type' => 'select2',
                'label' => 'Người tạo'
            ],
            function () {
                return User::whereIn('id',
                    Task::distinct()->pluck('created_by', 'created_by')
                        ->toArray())->pluck('email', 'id')->toArray();
            },
            function ($value) {
                $this->crud->addClause('where', 'created_by', $value);
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'user_id',
                'type' => 'select2',
                'label' => 'Người thực hiện'
            ],
            function () {
                return User::whereIn('id',
                    Task::distinct()->pluck('user_id', 'user_id')
                        ->toArray())->pluck('email', 'id')->toArray();
            },
            function ($value) {
                $this->crud->addClause('where', 'user_id', $value);
            }
        );
        # get distinct `source` from `candidates` table
        $this->crud->addFilter(
            [
                'name' => 'status_id',
                'type' => 'select2',
                'label' => 'Trạng thái'
            ],
            function () {
                $status = $this->statusService->getStatus('status-task', true);
                return $status;
            },
            function ($value) {
                // dd($value);
                $this->crud->addClause('where', 'status_id', $value);
            }
        );
        # get distinct `source` from `candidates` table
        $this->crud->addFilter(
            [
                'name' => 'service_id',
                'type' => 'select2',
                'label' => 'Loại dịch vụ'
            ],
            function () {
                $status = $this->statusService->getStatus('service-name', true);
                return $status;
            },
            function ($value) {
                // dd($value);
                $this->crud->addClause('where', 'service_id', $value);
            }
        );
        CRUD::filter('end_date')
            ->type('date_range')
            ->label('Ngày hết hạn')
            ->whenActive(function ($value) {
                $dates = json_decode($value);
                CRUD::addClause('where', 'end_date', '>=', $dates->from);
                CRUD::addClause('where', 'end_date', '<=', $dates->to . ' 23:59:59');
            });

    }

}
