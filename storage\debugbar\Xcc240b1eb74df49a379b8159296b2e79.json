{"__meta": {"id": "Xcc240b1eb74df49a379b8159296b2e79", "datetime": "2025-08-05 17:32:24", "utime": **********.099324, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754389941.536648, "end": **********.099358, "duration": 2.5627100467681885, "duration_str": "2.56s", "measures": [{"label": "Booting", "start": 1754389941.536648, "relative_start": 0, "end": **********.572484, "relative_end": **********.572484, "duration": 1.0358359813690186, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.572508, "relative_start": 1.****************, "end": **********.099361, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#785\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#784 …}\n  file: \"D:\\Projects\\HRI\\crm.hri.com.vn\\routes\\web.php\"\n  line: \"16 to 18\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Froutes%2Fweb.php&line=16\" onclick=\"\">routes/web.php:16-18</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj", "_previous": "array:1 [\n  \"url\" => \"http://chri.local\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1170497456 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1170497456\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-348438576 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-348438576\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-154449191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-154449191\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1358747002 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"545 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358747002\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2042820504 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042820504\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1570047648 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 10:32:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InVmeUtnYlh0N1YzaTNSVHYrbklOS3c9PSIsInZhbHVlIjoiS0lKc1hqT2hyNGE5ODA3QUltZ2lJMHBDM3F5MlB6ZnVNTWRHUXdad1BNMzA4K2FWdDd5TUZFWXhFL0JRSHFuRjJyZlk4dzY5YU9ReU40Lzl1TWFxY1F1NzU4bzhUNWZtYzR5UDlBTi9pOUZTeWtZQ3poUWJFMXZPaEc2M2lwWEUiLCJtYWMiOiIzNjc0MjU4MGNmMTFlNTI0MDRlN2FmMzc4NzhlMWM5ZDUwNmIwZDJmYjQzOTQ3MzhiNGQ4MGY3MjU0OTk2NjZkIiwidGFnIjoiIn0%3D; expires=Tue, 05 Aug 2025 12:32:23 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IlRhRUVYK3hBM3VKNzBLUDZVVVdaalE9PSIsInZhbHVlIjoienZtdzEyNU1rUVc0R0dadDNCOGI0aWhGZGplcEdjeXhObUxzZ0VwcURocmRMci9kaSs2YzV0STdsQkZwQk15MnJuWVFtMnZONCtWb242L0NweVlvSzJnZVd6U05wTkZGcldmbmhXOTZMSXZpR0tBSzFUR1V4UlhyZGJqQkF6VngiLCJtYWMiOiIyNTk2NDJmYjljOTI1MmQ0Y2Q5NWY0ODFkODA5N2E2MTc3OWU3ZDVkZjBiN2Y4OThiNGU4NDcyNDExNzZlYWZhIiwidGFnIjoiIn0%3D; expires=Tue, 05 Aug 2025 12:32:23 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InVmeUtnYlh0N1YzaTNSVHYrbklOS3c9PSIsInZhbHVlIjoiS0lKc1hqT2hyNGE5ODA3QUltZ2lJMHBDM3F5MlB6ZnVNTWRHUXdad1BNMzA4K2FWdDd5TUZFWXhFL0JRSHFuRjJyZlk4dzY5YU9ReU40Lzl1TWFxY1F1NzU4bzhUNWZtYzR5UDlBTi9pOUZTeWtZQ3poUWJFMXZPaEc2M2lwWEUiLCJtYWMiOiIzNjc0MjU4MGNmMTFlNTI0MDRlN2FmMzc4NzhlMWM5ZDUwNmIwZDJmYjQzOTQ3MzhiNGQ4MGY3MjU0OTk2NjZkIiwidGFnIjoiIn0%3D; expires=Tue, 05-Aug-2025 12:32:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IlRhRUVYK3hBM3VKNzBLUDZVVVdaalE9PSIsInZhbHVlIjoienZtdzEyNU1rUVc0R0dadDNCOGI0aWhGZGplcEdjeXhObUxzZ0VwcURocmRMci9kaSs2YzV0STdsQkZwQk15MnJuWVFtMnZONCtWb242L0NweVlvSzJnZVd6U05wTkZGcldmbmhXOTZMSXZpR0tBSzFUR1V4UlhyZGJqQkF6VngiLCJtYWMiOiIyNTk2NDJmYjljOTI1MmQ0Y2Q5NWY0ODFkODA5N2E2MTc3OWU3ZDVkZjBiN2Y4OThiNGU4NDcyNDExNzZlYWZhIiwidGFnIjoiIn0%3D; expires=Tue, 05-Aug-2025 12:32:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570047648\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-912319818 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iUMWA1e1WXAfgSQ1PWAviSilNUTILWSImfxaJaAj</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"17 characters\">http://chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-912319818\", {\"maxDepth\":0})</script>\n"}}