<div><label>&nbsp;</label></div>
<button class="btn btn-primary" type="button" id="btn-gen-cv-private"
    rel="{{ isset($field['data']['id']) ? $field['data']['id'] : '' }}">Tạo CV che tự động</button>


@section('after_scripts')
    @parent
    <script>
        $(document).ready(function() {
            $('#btn-gen-cv-private').click(function() {
                var label = 'Tạo CV che tự động';
                $(this).attr('disabled', true);
                $(this).html('Đang xử lý...');
                var url = '{{ route('api.cv.gen-private-cv', ':id') }}';
                url = url.replace(':id', $(this).attr('rel'));
                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function(response) {
                        if (response.status == 'success') {
                            alert('Tạo CV che thành công');
                            window.location.reload();
                        } else {
                            alert('Tạo CV che thất bại');
                            $(this).attr('disabled', false);
                            $(this).html(label);
                        }
                    },
                    error: function() {
                        alert('Tạo CV che thất bại');
                        $(this).attr('disabled', false);
                        $(this).html(label);
                    }
                });
            });
        });
    </script>
@endsection
