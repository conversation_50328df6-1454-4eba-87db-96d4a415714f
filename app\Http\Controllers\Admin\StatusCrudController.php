<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\StatusRequest;
use App\Http\Resources\StatusResource;
use App\Models\Status;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Http\Request;

/**
 * Class StatusCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class StatusCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Status::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/status');
        CRUD::setEntityNameStrings('status', 'statuses');
        CRUD::denyAccess('delete');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::setFromDb(); // set columns from db columns.
        $this->crud->removeColumn('parent_id');
        $this->crud->removeColumn('order');
        $this->crud->addColumn([
            'label' => 'Order',
            'name'  => 'order',
            'type'  => 'number',
        ]);
        $this->crud->addColumn([
            'name'  => 'is_active',
            'type'  => 'switch',
            'label' => 'Active',
        ]);
        $this->crud->addColumn([
            'label'     => 'Parent',
            'type'      => 'relationship',
            'name'      => 'parent_id',
            'entity'    => 'parent',
            'attribute' => 'name', // combined name & date column
            'model'     => 'App\Models\Status',
            //            'searchLogic' => function ($query, $column, $searchTerm) {
            //                $query->orWhereHas('company', function ($q) use ($column, $searchTerm) {
            //                    $q->where('company_name', 'like', '%' . $searchTerm . '%');
            //                });
            //            }
        ]);
        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(StatusRequest::class);
        CRUD::addField(fieldColumnData('name', 'Name', 'text', 'form-group', 'required'));
        CRUD::addField(fieldColumnData('group', 'Group', 'text', 'form-group', 'required'));
        CRUD::addField([
            'label'                => "Parent Status",
            'type'                 => 'select2_from_ajax',
            'name'                 => 'parent_id',
            'data_source'          => route('status.ajax.get-status', ['parent_id' => null]),
            'placeholder'          => 'Select an option',
            'allow_clear'          => true,
            'minimum_input_length' => 0,
            'method'               => 'POST',
            'attribute'            => 'name',
        ]);
        CRUD::addField([
            'name'  => 'is_active',
            'type'  => 'switch',
            'label'    => 'Active',
        ]);
        CRUD::addField(fieldColumnData('order', 'Order', 'number', 'form-group', ''));


        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function getStatus(Request $request)
    {

        $categories = Status::query()
            ->when(!empty($request->q), function ($q) use ($request) {
                return $q->where('name', 'like', '%' . $request->q . '%');
            })
            ->when(!empty($request->parent_id), function ($q) use ($request) {
                return $q->where('parent_id', $request->parent_id);
            })->with(['parent', 'children'])->get();
        return $categories;
    }
}
