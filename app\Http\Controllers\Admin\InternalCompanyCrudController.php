<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\InternalCompanyRequest;
use App\Models\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class InternalCompanyCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class InternalCompanyCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;



    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\InternalCompany::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/internal-company');
        CRUD::setEntityNameStrings('internal company', 'internal companies');
        CRUD::denyAccess('delete');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::column('row_number')->type('row_number')->label('#')->orderable(false);

        CRUD::column('name')->label('Tên');
        // CRUD::column('user')->label('Quản lý');

        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(InternalCompanyRequest::class);
        CRUD::addFields([
            fieldColumnData('name', 'Công ty ', 'text', 'form-group col-md-6', 'required'),
            // [
            //     'type'                 => "relationship",
            //     'name'                 => 'user',
            //     'label'                => 'Người quản lý',
            //     'ajax'                 => true,
            //     'placeholder'          => 'Choose option',
            //     'minimum_input_length' => 0,
            //     'wrapper'              => [
            //         'class' => 'form-group col-md-6',
            //     ],

            // ],
        ]);

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function fetchUser()
    {
        return $this->fetch(User::class);
    }

    protected function setupInlineCreateOperation()
    {

        $this->setupCreateOperation();
    }

}
