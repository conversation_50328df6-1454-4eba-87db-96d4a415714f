<?php

namespace App\Jobs;

use App\Models\Attachment;
use App\Services\FileServiceS3;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DownloadAttactment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public $attachment;
    public function __construct(Attachment $attachment)
    {
        $this->attachment = $attachment;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        
        $path = $this->attachment->path;
        $options = [
            'basic_auth' => env('JIRA_USER') . ':' . env('JIRA_PASS'),
        ];
        # if path contain "https://rec.hri.com.vn/secure/attachment"
        if (strpos($path, 'https://rec.hri.com.vn/secure/attachment') !== false) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $name = pathinfo($path, PATHINFO_FILENAME);
            $file = file_get_contents($path);
            // dd($file);
            // $path = 'attachments/' . $name . '.' . $extension;
            $target_path = PATH_FOLDER_SAVE_CV . '/' . date('Y') . '/' . date('m') . '/' . date('d'); // . '/' . md5($path) . '.' . $extension;
            $target_path = FileServiceS3::getInstance()->uploadToS3FromLink($path, $target_path, $options);
            if ($target_path) {
                $this->attachment->path = $target_path;
                $this->attachment->save();
            }
        }

        if ($this->attachment->type == 'file') {
            $cv = $this->attachment->object;
            if (empty($cv->cv_public)) {
                $cv->cv_public = $this->attachment->path;
                $cv->save();
            }
        }

    }
}


