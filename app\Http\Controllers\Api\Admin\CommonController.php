<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Admin\TaskResource;
use App\Http\Resources\Api\Admin\LogTaskResource;
use App\Models\Department;
use App\Services\LogTaskService;
use App\Services\TaskService;
use Illuminate\Http\Request;
use App\Helpers\Utils;

class CommonController extends Controller
{


    public function __construct() {}

    public function getMasterData(Request $request)
    {
        $key = $request->get('key');
        $keys = explode(',', $key);
        $data = [];
        if (in_array('teams', $keys)) {
            $data['teams'] = Department::get();
        }
        if (in_array('recTeams', $keys)) {
            $data['recTeams'] = Department::whereIn('id', [9, 10, 11, 12, 13, 14, 15, 16])->get();
        }
        return response()->json(['data' => $data]);
    }

    public function getCity(Request $request)
    {
        $data = Utils::getCities();
        $data = array_map(function ($key, $value) {
            return [
                'id' => $key,
                'name' => $value,
            ];
        }, array_keys($data), array_values($data));
        return response()->json($data);
    }
}
