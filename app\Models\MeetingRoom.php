<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class MeetingRoom extends Model
{
    use HasFactory, SoftDeletes, CrudTrait;

    protected $fillable = [
        'name',
        'location',
        'capacity',
        'description',
        'facilities',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'facilities' => 'array'
    ];

    /**
     * Get the bookings for the meeting room.
     */
    public function bookings()
    {
        return $this->hasMany(MeetingBooking::class);
    }

    /**
     * Check if room is available for a given time range
     */
    public function isAvailable($startTime, $endTime, $excludeBookingId = null)
    {
        $query = $this->bookings()
            ->where('status', '!=', 'cancelled')
            ->where('status', '!=', 'rejected')
            ->where(function ($q) use ($startTime, $endTime) {
                $q->whereBetween('start_time', [$startTime, $endTime])
                    ->orWhereBetween('end_time', [$startTime, $endTime])
                    ->orWhere(function ($q2) use ($startTime, $endTime) {
                        $q2->where('start_time', '<=', $startTime)
                            ->where('end_time', '>=', $endTime);
                    });
            });

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->count() === 0;
    }
}
