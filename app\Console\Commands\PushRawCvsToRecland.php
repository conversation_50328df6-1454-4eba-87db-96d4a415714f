<?php

namespace App\Console\Commands;

use App\Models\RawCv;
use Illuminate\Console\Command;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

class PushRawCvsToRecland extends Command
{
    protected $signature = 'command:PushRawCvsToRecland';
    protected $description = 'Push Raw CVs to Recland';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // $file = 'raw_cv_ids.txt';
        // $handle = fopen($file, 'a');
        // $ids = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        RawCv::where('push_to_recland', false)
            ->orderBy('id', 'desc')
            ->chunk(100, function ($rawCvs) {
                foreach ($rawCvs as $rawCv) {

                    $client = new Client();
                    // $url = 'https://recland.co/api/selling-cv/create';
                    $url = 'http://recland.local/api/selling-cv/create';

                    $cvFile = strpos($rawCv->cv_public, 'https://') !== false
                        ? $rawCv->cv_public
                        : gen_url_file_s3($rawCv->cv_public, '', false);

                    $cvFile_private = strpos($rawCv->cv_private, 'https://') !== false
                        ? $rawCv->cv_private
                        : gen_url_file_s3($rawCv->cv_private, '', false);
                    $raw_data = $rawCv->raw_data;
                    $raw_province = $raw_data['province'] ?? '';
                    $raw_province = Str::slug($raw_province);
                    if (strpos($raw_province, 'ho-chi-minh') !== false || strpos($raw_province, 'hcm') !== false) {
                        $province = 'ho-chi-minh';
                    } elseif (strpos($raw_province, 'ha-noi') !== false || strpos($raw_province, 'hn') !== false) {
                        $province = 'ha-noi';
                    } else {
                        $province = '';
                    }
                    $skills = $raw_data['skills'] ?? [];
                    $cvFile = str_replace('https://itnavi-development', 'https://itnavi', $cvFile);
                    $cvFile_private = str_replace('https://itnavi-development', 'https://itnavi', $cvFile_private);
                    $raw_data = $rawCv->raw_data;
                    $data = [
                        'private_cv_upload'          => '',
                        'cv_public'                  => $cvFile,
                        'cv_private'                 => $cvFile_private,
                        'id'                         => 0,
                        'candidate_name'             => $raw_data['full_name'] ?? '',
                        'candidate_mobile'           => $rawCv->phone ?? '',
                        'candidate_email'            => strtolower($raw_data['email']) ?? '',
                        'year_experience'            => $raw_data['years_experience'] ?? 0,
                        'candidate_job_title'        => $raw_data['job_title'] ?? '',
                        'rank'                       => '',
                        'candidate_portfolio'        => '',
                        'candidate_salary_expect'    => 0,
                        'candidate_salary_expect_to' => 0,
                        'candidate_currency'         => 'VND',
                        'career'                     => [30, 31],
                        'assessment'                 => '',
                        'candidate_location'         => $province,
                        'skills'                     => $skills,
                        'candidate_formwork'         => 1,
                        'candidate_est_timetowork'   => 2,
                        'selling_skill'              => '',
                        'is_authority'               => 1,
                        'type_of_sale'               => 'cv',
                        'source'                     => 'raw_cv.hri.com.vn',
                        'created_at'                 => $rawCv->created_at->format('Y-m-d H:i:s'),
                        'updated_at'                 => $rawCv->updated_at->format('Y-m-d H:i:s'),
                    ];

                    // dd($data);
                    try {
                        $response = $client->post($url, [
                            'json' => $data,
                            'http_errors' => false
                        ]);
                        dd($response->getBody()->getContents());
                        $result = json_decode($response->getBody()->getContents(), true);

                        if (!$result) {
                            $this->error("Không thể parse JSON response cho Raw CV ID: " . $rawCv->id);
                            $rawCv->update(['push_to_recland' => -1]);
                            continue;
                        }

                        $id = data_get($result, 'data.id');
                        $this->info($rawCv->id . ' ' . $rawCv->email . ' => ' . ($id ? $id : 'Loi'));
                        // fwrite($handle, $rawCv->id . "\n");

                        if ($id) {
                            $rawCv->update(['push_to_recland' => true]);
                        }
                    } catch (\Exception $e) {
                        $this->error("Lỗi khi gửi Raw CV ID {$rawCv->id}: " . $e->getMessage());
                        continue;
                    }
                    echo __FILE__ . ': ' . __LINE__; die;
                }
            });

        return Command::SUCCESS;
    }
}
