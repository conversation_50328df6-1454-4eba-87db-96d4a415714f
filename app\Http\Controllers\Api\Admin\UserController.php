<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Admin\StatusResource;
use App\Services\ReportService;
use App\Services\StatusService;
use Illuminate\Http\Request;

class UserController extends Controller
{

    public function __construct()
    {
    }

    public function getAccessToken(Request $request)
    {
        $token = backpack_auth()->user()->createToken('api-token')->plainTextToken;
        return response()->json(['access_token' => $token]);
    }
    public function getListUser(Request $request)
    {
        $users = backpack_user()->where('status', 1)->get();
        $data = [];
        foreach ($users as $user) {
            // $data[$user->id] = Utils::getUsernameFromEmail($user->email);
            $data[] = [
                'id' => $user->id,
                'name' => Utils::getUsernameFromEmail($user->email)
            ];
        }
        return response()->json(['users' => $data]);
    }

    /**
     * Get users for meeting participants
     */
    public function index(Request $request)
    {
        $query = backpack_user()->where('status', 1);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->select('id', 'name', 'email')
                      ->orderBy('name')
                      ->limit(50)
                      ->get();

        $data = $users->map(function($user) {
            return [
                'id' => $user->id,
                'name' => $user->name ?: Utils::getUsernameFromEmail($user->email),
                'email' => $user->email,
                'avatar' => null // Add avatar logic if available
            ];
        });

        return response()->json($data);
    }
}
