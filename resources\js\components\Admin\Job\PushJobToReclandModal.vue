<template>
  <v-dialog v-model="dialog" max-width="600px" persistent>
    <v-card>
      <v-card-title class="text-h5"><PERSON><PERSON><PERSON> sang Recland</v-card-title>

      <v-card-text v-if="successMessage">
        <v-container>
          <v-row>
            <v-col cols="12" md="12">
              <v-alert type="success"> {{ successMessage }} </v-alert>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-text v-else-if="max_salary > 0">
        <v-container>
          <v-row>
            <v-col cols="12" md="12">
              <v-autocomplete ref="searchBar" v-model="search_company_id" :items="companies" outlined dense hide-no-data
                item-title="name" item-value="id" label="Gõ và bấm Enter để tìm kiếm công ty"
                @update:search="companySearchText = $event" @keydown.enter="searchCompany" :messages="msg"
                append-icon="" clearable @update:modelValue="loadEmployerByCompanyId"
                :rules="[(v) => !!v || 'Vui lòng chọn công ty']"></v-autocomplete>
            </v-col>

            <!-- <v-col cols="12" md="6">
                            <v-select v-model="formData.company_id" :items="companies" item-title="name" item-value="id"
                                label="Công ty" required :rules="[(v) => !!v || 'Vui lòng chọn công ty']"></v-select>
                        </v-col> -->

            <v-col cols="12" md="12">
              <v-select v-model="formData.employer_id" :items="employers" item-title="name" item-value="id"
                label="Người dùng" required :rules="[(v) => !!v || 'Vui lòng chọn người dùng']"></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-autocomplete v-model="formData.career_id" :items="careers" item-title="name" item-value="id"
                label="Ngành nghề" required :rules="[(v) => !!v || 'Vui lòng chọn ngành nghề']"></v-autocomplete>
            </v-col>

            <v-col cols="12" md="6">
              <v-autocomplete clearable v-model="formData.skill_id" :items="skills" item-title="name" item-value="id"
                label="Kỹ năng" required :rules="[(v) => !!v || 'Vui lòng chọn kỹ năng']"></v-autocomplete>
            </v-col>

            <v-col cols="12" md="6">
              <v-autocomplete v-model="formData.level_id" :items="levels" item-title="name" item-value="id"
                label="Cấp độ" required :rules="[(v) => !!v || 'Vui lòng chọn cấp độ']"></v-autocomplete>
            </v-col>

            <v-col cols="12" md="6">
              <v-select v-model="formData.recruitment_type_id" :items="recruitmentTypes" item-title="name"
                item-value="id" label="Loại tuyển dụng" required
                :rules="[(v) => !!v || 'Vui lòng chọn loại tuyển dụng']"></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select v-model="formData.job_type_id" :items="jobTypes" item-title="name" item-value="id"
                label="Loại tuyển dụng" required :rules="[(v) => !!v || 'Vui lòng chọn loại tuyển dụng']"></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-autocomplete v-model="formData.city_id" :items="cities" item-title="name" item-value="id"
                label="Thành phố" required :rules="[(v) => !!v || 'Vui lòng chọn thành phố']"></v-autocomplete>
            </v-col>

            <v-col cols="12">
              <v-text-field v-model="formData.bonus" label="Tiền thưởng" type="number" min="0" step="1000" suffix="VND"
                :disabled="!isBonusEnabled"
                :rules="[(v) => v >= 0 || 'Tiền thưởng phải lớn hơn hoặc bằng 0', (v) => !minBonusPrice || v >= minBonusPrice || `Tiền thưởng phải lớn hơn hoặc bằng ${minBonusPriceStr}`]"></v-text-field>
              <div v-if="minBonusPriceStr" class="text-caption text-red">Yêu cầu giá thấp nhất là {{ minBonusPriceStr }}
              </div>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-text v-else>
        <v-container>
          <v-row>
            <v-col cols="12" md="12">
              <v-alert type="warning"> Vui lòng nhập mức lương tối đa để tính toán tiền thưởng </v-alert>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-actions v-if="successMessage">
        <v-spacer></v-spacer>
        <v-btn color="grey" text @click="closeModal"> Đóng </v-btn>
      </v-card-actions>
      <v-card-actions v-else>
        <v-spacer></v-spacer>
        <v-btn color="grey" text @click="closeModal"> Hủy </v-btn>
        <v-btn color="primary" @click="submitForm" :loading="isLoading" :disabled="!isFormValid"> Push to Recland
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import axios from "axios";

export default {
  name: "PushJobToReclandModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    jobId: {
      type: [String, Number],
      required: true,
    },
    maxSalary: {
      type: [String, Number],
      required: true,
    },
    salaryCurrency: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      companySearchText: "",
      dialog: false,
      isLoading: false,
      group_id: null,
      is_it: false,
      minBonusPrice: null,
      minBonusPriceStr: "",
      successMessage: "",
      max_salary: 0,
      cities: [],
      formData: {
        company_id: null,
        employer_id: null,
        career_id: null,
        skill_id: null,
        level_id: null,
        recruitment_type_id: null,
        job_type_id: null,
        city_id: null,
        bonus: 0,
      },
      companies: [],
      employers: [],
      careers: [],
      skills: [],
      levels: [],
      jobTypes: [],
      recruitmentTypes: ["cv", "interview", "onboard"],
      search_company_id: null,
      formRef: null,
    };
  },
  computed: {
    isBonusEnabled() {
      return this.formData.career_id && this.formData.skill_id && this.formData.level_id && this.formData.recruitment_type_id;
    },
    isFormValid() {
      return this.search_company_id && this.formData.employer_id && this.formData.career_id && this.formData.skill_id && this.formData.level_id && this.formData.recruitment_type_id && this.max_salary > 0 && this.formData.bonus >= this.minBonusPrice;
    },
  },
  watch: {
    show(newVal) {
      this.successMessage = "";
      this.dialog = newVal;
      if (newVal) {
        // this.loadSelectData();
        this.loadCareer();
        this.loadJobType(); 
        this.loadCity();
        this.processMaxSalary();
      }
    },
    maxSalary: {
      handler(newVal) {
        this.processMaxSalary();
      },
      immediate: true,
    },
    salaryCurrency: {
      handler(newVal) {
        this.processMaxSalary();
      },
      immediate: true,
    },
    dialog(newVal) {
      if (!newVal) {
        this.$emit("close");
      }
    },
    "formData.career_id"(newVal) {
      if (newVal) {
        this.formData.skill_id = null;
        this.formData.level_id = null;
        this.loadSkill();
      }
    },

    "formData.skill_id"(newVal) {
      if (newVal) {
        this.group_id = this.skills.find((skill) => skill.id === newVal).group;
        if (this.formData.career_id == 30 || this.formData.career_id == 31) {
          this.is_it = true;
        } else {
          this.is_it = false;
        }
        this.formData.level_id = null;
        this.loadLevel();
      }
    },
    "formData.level_id"(newVal) {
      if (newVal && this.isBonusEnabled) {
        this.fetchMinBonusPrice();
      }
    },
    "formData.recruitment_type_id"(newVal) {
      if (newVal && this.isBonusEnabled) {
        this.fetchMinBonusPrice();
      }
    },
  },
  mounted() {
    // this.loadCareer();
  },
  methods: {
    processMaxSalary() {
      if (!this.maxSalary) {
        this.max_salary = 0;
        return;
      }

      if (this.salaryCurrency === "9") {
        // USD
        this.max_salary = parseFloat(this.maxSalary) * 25500;
      } else {
        this.max_salary = parseFloat(this.maxSalary);
      }
    },
    searchCompany() {
      axios.get(`/api/recland/company?q=${this.companySearchText}`).then(({ data }) => {
        this.companies = data;
      });
    },

    loadEmployerByCompanyId() {
      if (this.search_company_id) {
        this.employers = [];
        this.formData.employer_id = null;
        axios
          .get("/api/recland/employer-by-company-id?company_id=" + this.search_company_id)
          .then((response) => {
            this.employers = response.data;
            console.log(this.employers);
          })
          .catch((error) => {
            console.log(error);
          });
      }
    },

    async loadCareer() {
      this.careers = [];
      axios.get("/api/recland/career").then((response) => {
        this.careers = response.data;
      });
    },
    async loadJobType() {
      this.jobTypes = [];
      axios.get("/api/recland/job-type").then((response) => {
        this.jobTypes = response.data;
      });
    },
    async loadCity() {
      this.cities = [];
      axios.get("/api/city").then((response) => {
        this.cities = response.data;
      });
    },

    async loadSkill() {
      this.skills = [];
      axios.get("/api/recland/skill-by-career?career_id=" + this.formData.career_id).then((response) => {
        this.skills = response.data;
      });
    },

    async loadLevel() {
      this.levels = [];
      axios.get("/api/recland/level?group_id=" + this.group_id + "&is_it=" + this.is_it).then((response) => {
        this.levels = response.data;
      });
    },

    async loadSelectData() {
      try {
        this.isLoading = true;

        // Tải dữ liệu cho các select box
        const [companiesRes, usersRes, careersRes, skillsRes, levelsRes, recruitmentTypesRes] = await Promise.all([axios.get("/api/companies"), axios.get("/api/users"), axios.get("/api/careers"), axios.get("/api/skills"), axios.get("/api/levels"), axios.get("/api/recruitment-types")]);

        this.companies = companiesRes.data;
        this.employers = usersRes.data;
        this.careers = careersRes.data;
        this.skills = skillsRes.data;
        this.levels = levelsRes.data;
        this.recruitmentTypes = recruitmentTypesRes.data;
      } catch (error) {
        console.error("Lỗi khi tải dữ liệu:", error);
        this.$emit("error", "Không thể tải dữ liệu");
      } finally {
        this.isLoading = false;
      }
    },

    async submitForm() {
      if (!this.isFormValid) {
        this.$emit("error", "Vui lòng điền đầy đủ thông tin");
        return;
      }

      try {
        this.isLoading = true;

        const response = await axios.post("/api/recland/push-job", {
          job_id: this.jobId,
          company_id: this.search_company_id,
          employer_id: this.formData.employer_id,
          career_id: this.formData.career_id,
          skill_id: this.formData.skill_id,
          level_id: this.formData.level_id,
          recruitment_type_id: this.formData.recruitment_type_id,
          job_type_id: this.formData.job_type_id,
          city_id: this.formData.city_id,
          bonus: this.formData.bonus,
          max_salary: this.max_salary,
        });

        this.$emit("success", response.data.message || "Push job thành công!");
        this.successMessage = "Đã đẩy job sang Recland thành công!";
        // this.closeModal();
      } catch (error) {
        console.error("Lỗi khi push job:", error);
        this.$emit("error", error.response?.data?.message || "Có lỗi xảy ra khi push job");
      } finally {
        this.isLoading = false;
      }
    },

    validateForm() {
      const requiredFields = ["company_id", "employer_id", "career_id", "skill_id", "level_id", "recruitment_type_id"];

      for (const field of requiredFields) {
        if (!this.formData[field]) {
          this.$emit("error", "Vui lòng điền đầy đủ thông tin");
          return false;
        }
      }

      if (this.formData.bonus < 0) {
        this.$emit("error", "Tiền thưởng phải lớn hơn hoặc bằng 0");
        return false;
      }

      return true;
    },

    closeModal() {
      this.dialog = false;
      this.resetForm();
    },

    resetForm() {
      this.formData = {
        company_id: null,
        employer_id: null,
        career_id: null,
        skill_id: null,
        level_id: null,
        recruitment_type_id: null,
        bonus: 0,
      };
    },

    async fetchMinBonusPrice() {
      try {
        const response = await axios.get("/api/recland/get-min-submit-price", {
          params: {
            group: this.group_id,
            is_it: this.is_it ? 1 : 0,
            bonus_type: this.formData.recruitment_type_id,
            level: this.formData.level_id,
            career: this.formData.career_id,
            skill_id: this.formData.skill_id,
            salary_min: 0,
            salary_max: this.max_salary,
            salary_currency: "VND",
          },
        });

        if (response.data.success) {
          this.minBonusPrice = response.data.data.min_price;
          this.minBonusPriceStr = response.data.data.min_price_str;
        }
      } catch (error) {
        console.error("Lỗi khi lấy giá thấp nhất:", error);
      }
    },
  },
};
</script>

<style scoped>
.v-card-title {
  background-color: #1976d2;
  color: white;
}
</style>
