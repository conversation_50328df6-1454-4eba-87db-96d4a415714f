<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Models\ApplyJob;
use App\Models\Job;
use App\Models\Notification;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ReclandController extends Controller
{
    public function getCompany(Request $request)
    {
        $q = $request->get('q');
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'api/company/search', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'query' => [
                'q' => $q,
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        $data = $data['data'];
        $data = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }, $data);
        return response()->json($data);
    }

    public function getEmployerByCompanyId(Request $request)
    {
        $companyId = $request->get('company_id');
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'api/employer/search', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'query' => [
                'company_id' => $companyId,
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        $data = $data['data'];
        $data = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }, $data);
        return response()->json($data);
    }

    public function getCareer(Request $request)
    {
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'api/career', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        $data = $data['data'];
        $data = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }, $data);
        return response()->json($data);
    }
    public function getJobType(Request $request)
    {
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'api/job-type', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        $data = $data['data'];
        $data = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }, $data);
        return response()->json($data);
    }

    public function getSkillByCareer(Request $request)
    {
        $careerId = $request->get('career_id');
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'ajax/get-skill-main-it', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'query' => [
                'career' => $careerId,
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        $data = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name_vi'],
                'group' => $item['group'],
            ];
        }, $data);
        return response()->json($data);
    }

    public function getLevel(Request $request)
    {
        $group_id = $request->get('group_id');
        $is_it = $request->get('is_it');
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'ajax/get-level', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'query' => [
                'group' => $group_id,
                'is_it' => $is_it,
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        $data = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'name' => $item['name_vi'],
            ];
        }, $data);
        return response()->json($data);
    }

    public function getMinSubmitPrice(Request $request)
    {
        $group = $request->get('group');
        $is_it = $request->get('is_it');
        $bonus_type = $request->get('bonus_type');
        $level = $request->get('level');
        $career = $request->get('career');
        $skill_id = $request->get('skill_id');
        $salary_min = $request->get('salary_min');
        $salary_max = $request->get('salary_max');
        $salary_currency = $request->get('salary_currency');
        $client = new Client();
        $url = config('services.recland.url');
        $response = $client->get($url . 'ajax/get-min-submit-price', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'query' => [
                'group' => $group,
                'is_it' => $is_it,
                'bonus_type' => $bonus_type,
                'level' => $level,
                'career' => $career,
                'skill_id' => $skill_id,
                'salary_min' => $salary_min,
                'salary_max' => $salary_max,
                'salary_currency' => $salary_currency,
            ],
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        return response()->json($data);
    }

    public function pushJobToRecland(Request $request)
    {
        $job_id              = $request->get('job_id');
        $company_id          = $request->get('company_id');
        $employer_id         = $request->get('employer_id');
        $career_id           = $request->get('career_id');
        $skill_id            = $request->get('skill_id');
        $level_id            = $request->get('level_id');
        $recruitment_type_id = $request->get('recruitment_type_id');
        $job_type_id         = $request->get('job_type_id');
        $city_id             = $request->get('city_id');
        $bonus               = $request->get('bonus');
        $max_salary          = $request->get('max_salary');

        $job = Job::find($job_id);
        if (!$job) {
            return response()->json(['error' => 'Job not found'], 404);
        }
        // $job->status = 'push_to_recland';
        // $job->save();
        $data = [
            'name'                => $job->title,
            'company_id'          => $company_id,
            'employer_id'         => $employer_id,
            'expire_at'           => $job->expire_at,
            'vacancies'           => $job->vacancy,
            'type'                => $job_type_id,
            'career'              => $career_id,
            'rank'                => $level_id,
            'skill'               => $skill_id,
            'bonus'               => $bonus,
            'bonus_type'          => $recruitment_type_id,
            'city_slug'           => $city_id,
            'address'             => $job->address,
            'jd_description'      => $job->description,
            'jd_request'          => $job->requirement,
            'jd_welfare'          => $job->benefit,
            'salary_min'          => $job->min_salary,
            'salary_max'          => $job->max_salary,
            'salary_currency'     => $job->currency == 9 ? 'USD' : 'VND',
        ];

        $client              = new Client();
        $url                 = config('services.recland.url');
        $response            = $client->post($url . 'api/job/create', [
            'headers' => [
                'X-API-Key' => config('services.recland.api_key'),
            ],
            'json' => $data,
            'http_errors' => false,
        ]);
        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);
        if ($response->getStatusCode() == 200) {
            $recland_job_id = $data['data']['id'];
            $job->setMeta('recland_job_id', $recland_job_id);
        }

        return response()->json($data);
    }
}
