<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class LogChangeStatusApply extends Model
{
    use CrudTrait, HasFactory, Notifiable;

    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->date_status)) {
                $model->date_status = date('Y-m-d H:i:s');
            }
        });
        static::created(function ($model) {
            $default_status = Status::where('group', 'apply-job')->where('name', 'Đã ứng tuyển')->first();
            if ($model->status_id != $default_status->id) {
                event(new \App\Events\ApplyJobStatusChangedEvent($model));
            }
        });
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }
    public function apply_job()
    {
        return $this->belongsTo(ApplyJob::class, 'apply_job_id');
    }

    public function routeNotificationForMail($notification)
    {
        $apply_job = $this->apply_job;
        $user_ids = [$apply_job->created_by];
        $department = optional($apply_job->createdBy)->department;
        if ($department) {
            $user_ids = array_merge($user_ids, $department->user()->pluck('id')->toArray());
        }
        $user_ids[] = $apply_job->job->user->id;


        $user = User::whereIn('id', $user_ids)->pluck('name', 'email')->toArray();
        return $user;
    }
}
