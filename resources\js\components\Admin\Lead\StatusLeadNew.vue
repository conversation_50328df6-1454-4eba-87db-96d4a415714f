<template>
    <!-- <div class="card"> -->
    <!-- <div class="q-pa-md"> -->
    <q-stepper :loading="isLoading" v-model="step" ref="stepper" color="primary" header-nav animated alternative-labels>
        <template v-for="(status, index) in listStatusLead">
            <q-step :name="status.id" :prefix="index + 1" :title="status.name" @click="changeStatus(status.id)"
                :done="index <= this.status_index"></q-step>
        </template>
    </q-stepper>
    <!-- </div> -->
    <!-- </div> -->

    <div class="card mb-3 mt-2">
        <div class="card-body">
            <div class="row b-line">
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Tên liên hệ :</strong></div>
                </div>
                <div class="col-sm-4 ">{{ this.lead.contact_name }}</div>
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Full Name</strong></div>
                </div>
                <div class="col-sm-4 ">
                    Kenneth Valdez
                </div>
            </div>
            <div class="row b-line">
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Số điện thoại :</strong></div>
                </div>
                <div class="col-sm-4 ">{{ this.lead.phone }}</div>
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Email :</strong></div>
                </div>
                <div class="col-sm-4 ">{{ this.lead.email }}</div>
            </div>
            <div class="row b-line">
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Tên công ty :</strong></div>
                </div>
                <div class="col-sm-4 ">{{ this.lead.company.company_name }}</div>
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Người liên hệ :</strong></div>
                </div>
                <div class="col-sm-4 ">{{ this.lead.company.company_contact_name }}</div>
            </div>
            <div class="row b-line">
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Người chăm sóc: </strong></div>
                </div>
                <div class="col-sm-4 ">{{ this.lead.user.username }}</div>
            </div>
            <div class="row b-line">
                <div class="col-sm-2">
                    <div class="mb-0"><strong>Nội dung Lead</strong></div>
                </div>
                <div class="col-sm-10 " v-html="this.lead.content"></div>
            </div>
            <div class="row mt-2">
                <div class="col-sm-12">
                    <a :href="`/admin/lead/` + this.lead.id + `/edit`"><q-btn color="primary" class=" float-right"
                            icon="add">Edit </q-btn></a>
                    <a :href="`/admin/lead/` + this.lead.id + `/create-company`"><q-btn color="secondary"
                            class=" float-right mr-6" icon="add"> Tạo thông
                            tin khách hàng</q-btn></a>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-3 mt-2">
        <div class="card-body">
            <q-form @reset="resetForm()" @submit="submitData">
                <div class="row">
                    <div class="col-sm-11">

                        <div class="q-pa-md">
                            <q-input v-model="dataSubmit.content" filled type="textarea" :rows="3" />
                        </div>
                    </div>
                    <div class="col-sm-1 q-pa-md">
                        <q-btn type="submit" color="primary" icon-right="send" label="Submit" style="height: 100%;" />
                    </div>
                </div>
            </q-form>
        </div>
    </div>

    <div class="card mb-3 mt-2">
        <div class="card-body">
            <div class=" 2">
                <h5 class="pb-3"> Lịch sử thay đổi</h5>
                <table class="table">
                    <thead>
                        <tr>
                            <td>#</td>
                            <td>Tạo bởi</td>
                            <td>Nội dung</td>
                            <td>Thời gian tạo</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(log, index) in listLogLeads" :key="index">
                            <td>{{ index + 1 }}</td>
                            <td>{{ log.user_name }}</td>
                            <td v-html="log.content"></td>
                            <td>{{ log.created_at }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <loading v-model:active="isLoading" :is-full-page="fullPage" />
</template>

<script>
import Loading from 'vue-loading-overlay';
export default {
    name: "DetailTask",
    components: { Loading },
    // Your component's JavaScript logic goes here
    props: {
        lead_id: {
            required: true,
        },
    },
    watch: {
        step: function (newVal, oldVal) {
            this.changeStatus(newVal);
        }
    },
    data() {
        return {
            step: 0,
            text: '',
            lead: {
                company: {},
                user: {},
            },
            isLoading: false,
            status_index: 0,
            listStatusLead: [],
            listLogLeads: [],
            dataSubmit: {
                time_contact: null,
                content: null,
                lead_id: this.lead_id,
                user_id: this.user_id,
                company_id: this.company_id,
                contact_id: this.contact_id,
                status: this.status,
            },
            error: {
                status: null,
                time_contact: null,
                note: null,
            },
        }
    },
    mounted() {
        this.getListStatusLead();
        this.getLogLead();
    },
    methods: {
        getLogLead() {
            this.isLoading = true;
            axios.get('/api/get-log-lead/' + this.lead_id).then(res => {
                this.listLogLeads = res.data.data;
                this.isLoading = false;
            }).catch(err => {
                // this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        changeStatus(status) {
            // alert(status)
            this.isLoading = true;
            axios.post('/api/change-status-lead/' + this.lead_id, { status: status }).then(res => {
                this.isLoading = false;
                new Noty({ type: "success", text: res.data.success, }).show();
                this.getLeadDetail();
            }).catch(err => {
                console.log(err);
                alert('Có lỗi xảy ra ');
            });

        },
        getLeadDetail() {
            this.isLoading = true;
            axios.get('/api/lead/' + this.lead_id).then(res => {
                this.isLoading = false;
                this.lead = res.data.data;
                this.reCalStatusIndex();
                // console.log(this.lead)
                // alert(this.lead.user_email)
                // this.setKeyActive(this.lead.status_name);
            }).catch(err => {
                this.isLoading = false;
                console.log(err);
                alert('(getLeadDetail) Có lỗi xảy ra')
            });
        },
        getListStatusLead() {
            this.isLoading = true;
            axios.post('/api/status-parent/status-lead').then(res => {
                if (res && res.data && res.data.data && res.data.data.length > 0) {
                    this.listStatusLead = res.data.data;
                    this.getLeadDetail();
                    this.isLoading = false;
                }
            }).catch(err => {
                this.isLoading = false;
                console.log(err);
                // alert('Có lỗi xảy ra ')
            });
        },
        reCalStatusIndex() {
            for (let i = 0; i < this.listStatusLead.length; i++) {
                console.log(this.listStatusLead[i].id, this.lead.status);
                if (this.listStatusLead[i].id == parseInt(this.lead.status)) {
                    this.status_index = i;
                    break;
                }
            }
            // this.status_index = this.listStatusLead.findIndex(item => item.id == this.lead.status);
            // console.log(this.status_index);
        },
        resetForm() {
            this.showModal = false;
            this.dataSubmit = {
                time_contact: null,
                content: null,
                lead_id: this.lead_id,
                user_id: this.user_id,
                company_id: this.company_id,
                contact_id: this.contact_id,
                status: this.status,
            };

            this.error = { time_contact: null, content: null, };
        },
        async submitData(e) {
            e.preventDefault();
            this.isLoading = true;
            await axios.post('/api/create-log-lead', this.dataSubmit).then(res => {
                this.isLoading = false;
                this.dataSubmit.content = '';
                this.getLogLead();
                this.resetForm();
                new Noty({ type: "success", text: res.data.success, }).show();
            }).catch(err => {
                this.isLoading = false;
                if (err.response.data.errors.content) {
                    this.error.content = err.response.data.errors.content[0];
                } else {
                    alert('Có lỗi xảy ra ');
                }

            });
        },
    }
}
</script>

<style scoped>
/* Your component's CSS styles go here */
.b-line {
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 8px;
    padding-top: 8px;
}
</style>