<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cvs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->date('dob')->nullable();
            $table->string('gender', 10)->nullable();
            $table->string('mobile', 11)->nullable();
            $table->string('university')->nullable();
            $table->string('address')->nullable();
            $table->string('facebook')->nullable();
            $table->string('linkedin')->nullable();
            $table->string('job_title')->nullable();
            $table->tinyInteger('yoe')->nullable();
            $table->integer('academic_level')->nullable();
            $table->string('currency')->nullable();
            $table->string('salary_expect')->nullable();
            $table->string('salary_current')->nullable();
            $table->integer('level')->nullable();
            $table->string('language')->nullable();
            $table->string('skill')->nullable();
            $table->string('old_company')->nullable();
            $table->boolean('is_open')->default(0);
            $table->boolean('can_contact')->default(0);
            $table->integer('candidate_id')->unsigned();
            $table->string('work_site')->nullable();
            $table->string('cv_public')->nullable();
            $table->string('cv_private')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cvs');
    }
};
