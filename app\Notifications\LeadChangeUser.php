<?php

namespace App\Notifications;

use App\Channels\RingChannel;
use App\Models\Task;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\TaskService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use League\HTMLToMarkdown\HtmlConverter;

class LeadChangeUser extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', RingChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $lead = clone $notifiable;
        // $task = $log_task->task;
        $converter = new HtmlConverter();

        return (new MailMessage)
            ->subject('Bạn vừa nhận được 1 lead mới  #' . $lead->id)
            ->line('Bạn vừa nhận được 1 lead mới, vui lòng bấm vào link để xem thông tin')
            // ->line($converter->convert($log_task->content))
            ->action('Xem thông tin Lead', backpack_url('lead/' . $lead->id . '/show'));
//                     ->line('Thank you for using our application!');
    }

    public function toRing($notifiable)
    {
        $lead = clone $notifiable;
        $user = User::find($lead->user_id);
        $datas = [];

        $datas[] = [
            'user_id'    => $user->id,
            'is_watched' => 0,
            'link'       => route('task.show', $lead->id),
            'content'    => 'Bạn vừa nhận được 1 lead mới',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        (new NotificationService())->insert($datas);


    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

}
