<?php

namespace App\Http\Requests;

use App\Models\MeetingRoom;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MeetingBookingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'title'           => 'required|string|max:255',
            'description'     => 'nullable|string',
            'meeting_room_id' => 'required|exists:meeting_rooms,id',
            'start_time'      => 'required|date|after:now',
            'end_time'        => 'required|date|after:start_time',
            'status'          => ['nullable', Rule::in(['pending', 'approved', 'rejected', 'cancelled'])],
            'meeting_link'    => 'nullable|url',
            'agenda'          => 'nullable|string',
            'participants'    => 'nullable|array',
            'participants.*'  => 'exists:users,id'
        ];

        // Custom validation for room availability
        $rules['meeting_room_id'] = [
            'required',
            'exists:meeting_rooms,id',
            function ($attribute, $value, $fail) {
                $startTime = $this->input('start_time');
                $endTime = $this->input('end_time');
                $excludeId = $this->route('id');

                if ($startTime && $endTime) {
                    $room = MeetingRoom::find($value);
                    if ($room && !$room->isAvailable($startTime, $endTime, $excludeId)) {
                        $fail('Phòng họp đã được đặt trong khoảng thời gian này.');
                    }
                }
            }
        ];

        return $rules;
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => 'Tiêu đề',
            'description' => 'Mô tả',
            'meeting_room_id' => 'Phòng họp',
            'start_time' => 'Thời gian bắt đầu',
            'end_time' => 'Thời gian kết thúc',
            'status' => 'Trạng thái',
            'meeting_link' => 'Link cuộc họp',
            'agenda' => 'Nội dung',
            'participants' => 'Người tham gia'
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'start_time.after' => 'Thời gian bắt đầu phải sau thời điểm hiện tại.',
            'end_time.after' => 'Thời gian kết thúc phải sau thời gian bắt đầu.',
        ];
    }
}
