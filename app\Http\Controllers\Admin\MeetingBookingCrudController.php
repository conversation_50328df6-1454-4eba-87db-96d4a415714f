<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\MeetingBookingRequest;
use App\Models\MeetingBooking;
use App\Models\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class MeetingBookingCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class MeetingBookingCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\MeetingBooking::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/meeting-booking');
        CRUD::setEntityNameStrings('đặt lịch họp', 'đặt lịch họp');
    }

    /**
     * Define what happens when the List operation is loaded.
     * 
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::column('title')
            ->type('text')
            ->label('Tiêu đề');

        CRUD::column('meeting_room_id')
            ->type('select')
            ->entity('meetingRoom')
            ->model('App\\Models\\MeetingRoom')
            ->attribute('name')
            ->label('Phòng họp');

        CRUD::column('start_time')
            ->type('datetime')
            ->label('Thời gian bắt đầu');

        CRUD::column('end_time')
            ->type('datetime')
            ->label('Thời gian kết thúc');

        CRUD::column('status')
            ->type('enum')
            ->label('Trạng thái');

        CRUD::column('created_at')
            ->type('datetime')
            ->label('Ngày tạo');
    }

    /**
     * Define what happens when the Create operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(MeetingBookingRequest::class);

        CRUD::field('title')
            ->type('text')
            ->label('Tiêu đề')
            ->wrapper(['class' => 'form-group col-md-12']);

        CRUD::field('meeting_room_id')
            ->type('select2')
            ->entity('meetingRoom')
            ->model('App\\Models\\MeetingRoom')
            ->attribute('name')
            ->label('Phòng họp')
            ->wrapper(['class' => 'form-group col-md-6']);

        CRUD::field('start_time')
            ->type('datetime')
            ->label('Thời gian bắt đầu')
            ->wrapper(['class' => 'form-group col-md-3']);

        CRUD::field('end_time')
            ->type('datetime')
            ->label('Thời gian kết thúc')
            ->wrapper(['class' => 'form-group col-md-3']);

        CRUD::field('participants')
            ->type('select2_multiple')
            ->entity('participantUsers')
            ->model('App\\Models\\User')
            ->attribute('name')
            ->label('Người tham gia')
            ->pivot(true)
            ->wrapper(['class' => 'form-group col-md-12']);

        CRUD::field('description')
            ->type('textarea')
            ->label('Mô tả')
            ->wrapper(['class' => 'form-group col-md-12']);

        CRUD::field('agenda')
            ->type('textarea')
            ->label('Nội dung cuộc họp')
            ->wrapper(['class' => 'form-group col-md-12']);

        CRUD::field('meeting_link')
            ->type('url')
            ->label('Link cuộc họp (nếu họp online)')
            ->wrapper(['class' => 'form-group col-md-12']);

        CRUD::field('status')
            ->type('select2_from_array')
            ->options(config('constant.meeting_status'))
            ->label('Trạng thái')
            ->default('approved')
            ->wrapper(['class' => 'form-group col-md-6']);

        // Hidden field for created_by
        CRUD::field('created_by')
            ->type('hidden')
            ->value(backpack_user()->id);
    }

    /**
     * Define what happens when the Update operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    /**
     * Store a newly created resource in the database.
     */
    public function store()
    {
        $this->crud->hasAccessOrFail('create');

        // execute the FormRequest authorization and validation, if one is required
        $request = $this->crud->validateRequest();

        // register any Model Events defined on fields
        $this->crud->registerFieldEvents();

        // insert item in the db
        $item = $this->crud->create($this->crud->getStrippedSaveRequest($request));
        $this->data['entry'] = $this->crud->entry = $item;

        // Add participants
        if ($request->has('participants')) {
            foreach ($request->participants as $userId) {
                $item->addParticipant($userId, $userId == backpack_user()->id);
            }
        }

        // Add creator as organizer if not in participants list
        if (!$item->isParticipant(backpack_user()->id)) {
            $item->addParticipant(backpack_user()->id, true);
        }

        // show a success message
        \Alert::success(trans('backpack::crud.insert_success'))->flash();

        // save the redirect choice for next time
        $this->crud->setSaveAction();

        return $this->crud->performSaveAction($item->getKey());
    }

    /**
     * Update the specified resource in the database.
     */
    public function update()
    {
        $this->crud->hasAccessOrFail('update');

        // execute the FormRequest authorization and validation, if one is required
        $request = $this->crud->validateRequest();

        // register any Model Events defined on fields
        $this->crud->registerFieldEvents();

        // update the row in the db
        $item = $this->crud->update(
            $request->get($this->crud->model->getKeyName()),
            $this->crud->getStrippedSaveRequest($request)
        );
        $this->data['entry'] = $this->crud->entry = $item;

        // Update participants
        if ($request->has('participants')) {
            // Get current participants
            $currentParticipants = $item->participants()->pluck('user_id')->toArray();
            $newParticipants = $request->participants;

            // Remove participants not in new list
            $toRemove = array_diff($currentParticipants, $newParticipants);
            $item->participants()->whereIn('user_id', $toRemove)->delete();

            // Add new participants
            $toAdd = array_diff($newParticipants, $currentParticipants);
            foreach ($toAdd as $userId) {
                $item->addParticipant($userId, $userId == $item->created_by);
            }
        }

        // show a success message
        \Alert::success(trans('backpack::crud.update_success'))->flash();

        // save the redirect choice for next time
        $this->crud->setSaveAction();

        return $this->crud->performSaveAction($item->getKey());
    }

    /**
     * Show calendar view.
     */
    public function calendar()
    {
        $this->data['title'] = 'Lịch phòng họp';
        $this->data['breadcrumbs'] = [
            trans('backpack::crud.admin') => backpack_url('dashboard'),
            'Lịch phòng họp' => false,
        ];

        return view('admins.meeting.calendar', $this->data);
    }
}
