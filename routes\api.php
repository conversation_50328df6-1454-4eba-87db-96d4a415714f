<?php

use App\Http\Middleware\PublicApiAuth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::middleware(array_merge(
    (array)config('backpack.base.web_middleware', 'web'),
    (array)config('backpack.base.middleware_key', 'admin')
))->group(function () {
    Route::post('status-apply-job', [\App\Http\Controllers\Api\Admin\StatusController::class, 'getAllStatusApplyJob']);
    Route::post('status-parent/{key}', [\App\Http\Controllers\Api\Admin\StatusController::class, 'getAllStatusNullParent']);
    Route::post('change-status-apply-job', [\App\Http\Controllers\Api\Admin\ApplyJobController::class, 'create']);
    Route::get('/get-log-apply-job/{id}', [\App\Http\Controllers\Api\Admin\ApplyJobController::class, 'getLogApplyJob']);
    Route::post('/change-status-lead/{id}', [\App\Http\Controllers\Api\Admin\LeadController::class, 'changeStatus']);
    Route::post('/change-status-task/{id}', [\App\Http\Controllers\Api\Admin\TaskController::class, 'changeStatus']);
    Route::post('/create-log-task', [\App\Http\Controllers\Api\Admin\TaskController::class, 'createLog']);
    Route::post('/create-log-lead', [\App\Http\Controllers\Api\Admin\LeadController::class, 'createLogLead']);
    Route::get('/get-log-task/{id}', [\App\Http\Controllers\Api\Admin\TaskController::class, 'getLogTask']);
    Route::get('/get-log-lead/{id}', [\App\Http\Controllers\Api\Admin\LeadController::class, 'getLogLead']);
    Route::get('/task/{id}', [\App\Http\Controllers\Api\Admin\TaskController::class, 'getDetail']);
    Route::get('/lead/{id}', [\App\Http\Controllers\Api\Admin\LeadController::class, 'getData']);
    Route::post('/change-user-lead/{id}', [\App\Http\Controllers\Api\Admin\LeadController::class, 'changeUser']);
    Route::get('/cv/detail/{id}', [\App\Http\Controllers\Api\Admin\CvController::class, 'getDetail'])->name('api.cv.getCvDetail');
    Route::get('/cv/gen-private-cv/{id}', [\App\Http\Controllers\Api\Admin\CvController::class, 'genPrivateCv'])->name('api.cv.gen-private-cv');
    Route::post('/cv/upload-cv-file/{id}', [\App\Http\Controllers\Api\Admin\CvController::class, 'uploadCvFile'])->name('api.cvs.upload-cv-file');
    Route::get('/top-applyjob-dashboard', [\App\Http\Controllers\Admin\HomeController::class, 'index'])->name('home.index');
    Route::get('/created-cv-chart', [\App\Http\Controllers\Api\Admin\CvController::class, 'getChart']);
    Route::get('/created-apply-job-chart', [\App\Http\Controllers\Api\Admin\ApplyJobController::class, 'getApplyJobChart']);
    Route::get('/get-access-token', [\App\Http\Controllers\Api\Admin\UserController::class, 'getAccessToken']);
    Route::get('/get-list-user', [\App\Http\Controllers\Api\Admin\UserController::class, 'getListUser']);
    Route::get('/admin/users', [\App\Http\Controllers\Api\Admin\UserController::class, 'index']);
    Route::post('/change-subtask-active', [\App\Http\Controllers\Api\Admin\SubTasksController::class, 'changeSubtaskActive']);
    Route::get('/report/total-apply-status-by-month', [\App\Http\Controllers\Api\Admin\ReportController::class, 'totalApplyStatusByMonth']);
    Route::get('/report/total-recer-apply-status-by-month', [\App\Http\Controllers\Api\Admin\ReportController::class, 'totalRecerApplyStatusByMonth']);
    Route::get('/master-data', [\App\Http\Controllers\Api\Admin\CommonController::class, 'getMasterData']);
    Route::get('/city', [\App\Http\Controllers\Api\Admin\CommonController::class, 'getCity']);
    # API get data from recland
    Route::get('/recland/company', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getCompany']);
    Route::get('/recland/employer-by-company-id', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getEmployerByCompanyId']);
    Route::get('/recland/career', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getCareer']);
    Route::get('/recland/skill-by-career', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getSkillByCareer']);
    Route::get('/recland/level', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getLevel']);
    Route::get('/recland/job-type', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getJobType']);
    Route::get('/recland/get-min-submit-price', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'getMinSubmitPrice']);
    Route::post('/recland/push-job', [\App\Http\Controllers\Api\Admin\ReclandController::class, 'pushJobToRecland']);
    
    // Meeting Booking routes
    Route::get('/admin/meeting-bookings/calendar', [\App\Http\Controllers\Api\Admin\MeetingBookingController::class, 'calendar']);
    Route::get('/admin/meeting-bookings/{id}', [\App\Http\Controllers\Api\Admin\MeetingBookingController::class, 'show']);
    Route::post('/admin/meeting-bookings', [\App\Http\Controllers\Api\Admin\MeetingBookingController::class, 'store']);

    // Meeting Room routes
    Route::get('/admin/meeting-rooms', [\App\Http\Controllers\Api\Admin\MeetingRoomController::class, 'index']);
    Route::get('/admin/meeting-rooms/{meetingRoom}', [\App\Http\Controllers\Api\Admin\MeetingRoomController::class, 'show']);
    Route::post('/admin/meeting-rooms/{meetingRoom}/check-availability', [\App\Http\Controllers\Api\Admin\MeetingRoomController::class, 'checkAvailability']);
    Route::post('/admin/meeting-rooms/available', [\App\Http\Controllers\Api\Admin\MeetingRoomController::class, 'getAvailableRooms']);
});

// Route::middleware('auth:sanctum')->group(function () {
//     Route::get('/report/total-apply-status-by-month', [\App\Http\Controllers\Api\Admin\ReportController::class, 'totalApplyStatusByMonth']);
//     Route::get('/report/total-recer-apply-status-by-month', [\App\Http\Controllers\Api\Admin\ReportController::class, 'totalRecerApplyStatusByMonth']);
// });
Route::middleware(PublicApiAuth::class)->group(function () {
    Route::post('public/create-cv', [\App\Http\Controllers\Api\Public\CvController::class, 'createCv']);
    Route::post('public/create-raw-cv', [\App\Http\Controllers\Api\Public\RawCvController::class, 'create']);
});
