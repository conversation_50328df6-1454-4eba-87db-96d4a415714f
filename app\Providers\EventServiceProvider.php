<?php

namespace App\Providers;

use App\Events\ApplyJobCreated;
use App\Events\ApplyJobCreatedEvent;
use App\Events\ApplyJobStatusChangedEvent;
use App\Events\FTPBackupFileS3Event;
use App\Events\JobCreatedEvent;
use App\Events\LogTaskCreatedEvent;
use App\Events\MeetingBookingCreatedEvent;
use App\Events\TaskCreated;
use App\Listeners\ApplyJobCreatedListener;
use App\Listeners\ApplyJobStatusChangedListener;
use App\Listeners\FTPBackupFileS3Listener;
use App\Listeners\JobCreatedListener;
use App\Listeners\LogSendingEmail;
use App\Listeners\LogTaskCreatedListener;
use App\Listeners\SendMeetingInvitationListener;
use App\Listeners\TaskCreatedListener;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        // Registered::class => [
        //     SendEmailVerificationNotification::class,
        // ],
        // FTPBackupFileS3Event::class => [
        //     FTPBackupFileS3Listener::class
        // ],
        // LogTaskCreatedEvent::class => [
        //     LogTaskCreatedListener::class
        // ],
        // TaskCreated::class => [
        //     TaskCreatedListener::class
        // ],
        // // \Illuminate\Mail\Events\MessageSending::class => [
        // //     LogSendingEmail::class,
        // //     \App\Listeners\RedirectStagingEmail::class,
        // // ],
        // JobCreatedEvent::class => [
        //     JobCreatedListener::class,
        // ],
        // ApplyJobCreatedEvent::class => [
        //     ApplyJobCreatedListener::class,
        // ],
        ApplyJobStatusChangedEvent::class => [
            ApplyJobStatusChangedListener::class,
        ],
        MeetingBookingCreatedEvent::class => [
            SendMeetingInvitationListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
