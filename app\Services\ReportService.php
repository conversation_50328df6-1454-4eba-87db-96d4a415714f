<?php

namespace App\Services;

use App\Helpers\Utils;
use App\Models\ApplyJob;
use App\Models\Contact;
use App\Models\LogChangeStatusApply;
use App\Models\Status;
use App\Models\User;
use App\Repositories\LeadRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportService
{

    public function __construct() {}

    public function getSubUserIds($request)
    {
        // $user = $request->user();
        $user = backpack_auth()->user();
        // dd($user);
        $user_ids = [];
        if ($user->can('report.all-data')) {
            $user_ids = User::pluck('id')->toArray();
        } elseif ($user->can('report.only-company')) {
            $user_ids = $user->getUserIdsSameCompany();
        } elseif ($user->can('report.only-team')) {
            $user_ids = $user->getUserIdsSameDepartment();
        } elseif ($user->can('report.index')) {
            $user_ids = [$user->id];
        } else {
            $user_ids = [];
        }
        if (!is_array($user_ids) || count($user_ids) == 0) {
            $user_ids = [0];
        }
        return $user_ids;
    }

    public function totalApplyStatusByMonth(Request $request)
    {
        $start_date = $request->get('start_date', date('Y-01-01'));
        $end_date = $request->get('end_date', date('Y-m-d'));
        # check $start_date is valid
        if (!Utils::isValidDate($start_date)) {
            $start_date = date('Y-m-01');
        }
        # check $end_date is valid
        if (!Utils::isValidDate($end_date)) {
            $end_date = date('Y-m-d');
        }
        $start_date .= ' 00:00:00';
        $end_date .= ' 23:59:59';

        $user_ids = $this->getSubUserIds($request);
        // dd($user_ids);

        $data = \DB::table('log_change_status_applies')
            ->selectRaw('COUNT(id) AS total, user_id, status_id, DATE_FORMAT(date_status, "%Y-%m") AS `month`')
            ->whereIn('user_id', $user_ids)
            ->groupBy('user_id', 'status_id', \DB::raw('DATE_FORMAT(date_status, "%Y-%m")'))
            ->whereBetween('date_status', [$start_date, $end_date]);

        $data = $data->get()->toArray();
        $month = [];
        $user_ids = [];
        $status_ids = [];
        if (count($data) > 0) {
            foreach ($data as $item) {
                $month[$item->month][$item->status_id][$item->user_id] = $item->total;
                $user_ids[] = $item->user_id;
                $status_ids[] = $item->status_id;
                if (!isset($month[$item->month][$item->status_id]['total'])) {
                    $month[$item->month][$item->status_id]['total'] = $item->total;
                } else {
                    $month[$item->month][$item->status_id]['total'] += $item->total;
                }
            }
        }


        $data_jobs = \DB::table('jobs')
            ->selectRaw('COUNT(id) AS total, DATE_FORMAT(created_at, "%Y-%m") AS `month`')
            ->whereIn('created_by', $user_ids)
            ->groupBy(\DB::raw('DATE_FORMAT(created_at, "%Y-%m")'))
            ->whereBetween('created_at', [$start_date, $end_date])->get()->pluck('total', 'month')->toArray();


        $users = User::whereIn('id', array_unique($user_ids))->get()->pluck('email', 'id')->toArray();
        $status = Status::whereIn('id', array_unique($status_ids))->get()->pluck('name', 'id')->toArray();

        foreach ($users as $key => $value) {
            $users[$key] = Utils::getUsernameFromEmail($value);
        }

        return [
            'month' => $month,
            'users' => $users,
            'jobs_created' => $data_jobs,
            'status' => $status,

        ];
    }

    public function totalRecerApplyStatusByMonth(Request $request)
    {
        $start_date = $request->get('start_date', date('Y-01-01'));
        $end_date = $request->get('end_date', date('Y-m-d'));
        $team_id = $request->get('team_id', '');
        # check $start_date is valid
        if (!Utils::isValidDate($start_date)) {
            $start_date = date('Y-m-01');
        }
        # check $end_date is valid
        if (!Utils::isValidDate($end_date)) {
            $end_date = date('Y-m-d');
        }
        $start_date .= ' 00:00:00';
        $end_date .= ' 23:59:59';
        $user_ids = $this->getSubUserIds($request);

        $user_in_team = false;
        if ($team_id) {
            $user_in_team = User::where('department_id', $team_id)->pluck('id')->toArray();
        }

        $data = LogChangeStatusApply::selectRaw('COUNT(DISTINCT apply_jobs.id) AS total, apply_jobs.created_by as user_id, status_id')
            ->join('apply_jobs', 'apply_jobs.id', '=', 'log_change_status_applies.apply_job_id')
            ->groupBy('apply_jobs.created_by', 'status_id')
            ->whereIn('apply_jobs.created_by', $user_ids)
            ->whereBetween('log_change_status_applies.date_status', [$start_date, $end_date])
            ->when($user_in_team, function ($query, $user_in_team) {
                return $query->whereIn('apply_jobs.created_by', $user_in_team);
            })
            // dd($data->toSql(), $data->getBindings());
            ->get()->toArray();
        // dd($data);

        $cvUser = \DB::table('cvs')
            ->selectRaw('COUNT(cvs.id) AS total_cv, cvs.created_by, DATE_FORMAT(cvs.created_at, "%Y-%m") AS `month`')
            ->groupBy('cvs.created_by', \DB::raw('DATE_FORMAT(cvs.created_at, "%Y-%m")'))
            ->whereBetween('cvs.created_at', [$start_date, $end_date])
            ->whereIn('cvs.created_by', $user_ids)
            ->when($user_in_team, function ($query, $user_in_team) {
                return $query->whereIn('cvs.created_by', $user_in_team);
            })
            // dd($cvUser->toSql(), $cvUser->getBindings());
            ->get()->toArray();


        $user_ids = [];
        $status_ids = [];
        $total = [];
        $total_cv = [];
        if (count($data) > 0) {
            foreach ($data as $item) {
                $user_ids[] = $item['user_id'];
                $status_ids[] = $item['status_id'];
                $total[$item['user_id']][$item['status_id']] = $item['total'];
            }
        }

        // dd($cvUser);
        if (count($cvUser) > 0) {
            foreach ($cvUser as $item) {
                $total_cv[$item->created_by] = ($total_cv[$item->created_by] ?? 0) + $item->total_cv;
            }
        }
        // dd($total_cv);

        $users = User::whereIn('id', array_unique($user_ids))->get()->pluck('email', 'id')->toArray();
        $status = Status::whereIn('id', array_unique($status_ids))->get()->pluck('name', 'id')->toArray();

        foreach ($users as $key => $value) {
            $users[$key] = Utils::getUsernameFromEmail($value);
        }

        return [
            'total' => $total,
            'users' => $users,
            'status' => $status,
            'total_cv' => $total_cv
        ];
    }

    public function reportLeadBySource($start_date, $end_date)
    {
        $leadRepository = new LeadRepository();
        $data_raw = $leadRepository->getDataBySource($start_date, $end_date);
        $max_week_of_month = [];
        $data = [];
        $status_of_month = [];
        $status_of_source = [];
        $status = Status::where('group', 'status-lead')->orderBy('order')->get()->pluck('name', 'id')->toArray();
        $status['unknown'] = 'Không xác định';
        $month = [];
        // dd($status);
        foreach ($data_raw as $item) {
            $item['source'] = $item['source'] ?? 'unknown';
            $item['status'] = $item['status'] ?? 'unknown';
            $data[$item['source']][$item['status']][$item['month']][$item['week']] = $item['count'];
            $status_of_month[$item['month']][$item['status']] = $status[$item['status']];
            $status_of_source[$item['source']][$item['status']] = $status[$item['status']];
            $max_week_of_month[$item['month']] = max($max_week_of_month[$item['month']] ?? 0, $item['week']);
            $month[$item['month']] = $item['month'];
        }
        // dd($month);
        return [
            // 'status'            => $status,
            'data'              => $data,
            'status_of_month'   => $status_of_month,
            'max_week_of_month' => $max_week_of_month,
            'status_of_source'  => $status_of_source,
            'month'  => $month
        ];
    }

    public function reportLeadByUser($start_date, $end_date)
    {
        $leadRepository = new LeadRepository();
        $data_raw = $leadRepository->getDataByUser($start_date, $end_date);
        $data_raw_created = $leadRepository->getCreatedLeadByUser($start_date, $end_date);
        $max_week_of_month = [];
        $data = [];
        $status_of_month = [];
        $status_of_user = [];
        $status = Status::where('group', 'status-lead')->orderBy('order')->get()->pluck('name', 'id')->toArray();
        $status['unknown'] = 'Số phản hồi vào lead';
        $status['created'] = 'Số lead được tạo mới';
        $month = [];
        // dd($status);
        foreach ($data_raw as $item) {
            $item                                                           = (object) $item;
            $item->user_id                                                  = $item->user_id ?? 'unknown';
            $item->status                                                   = $item->status ?? 'unknown';
            $data[$item->user_id][$item->status][$item->month][$item->week] = $item->count;
            $status_of_month[$item->month][$item->status]                   = $status[$item->status];
            $status_of_month[$item->month]['created']                       = $status['created'];
            $status_of_user[$item->user_id][$item->status]                  = $status[$item->status];
            $status_of_user[$item->user_id]['created']                      = $status['created'];
            $max_week_of_month[$item->month]                                = max($max_week_of_month[$item->month] ?? 0, $item->week);
            $month[$item->month]                                            = $item->month;
        }
        foreach ($data_raw_created as $item) {
            $item = (object) $item;
            $item->user_id = $item->user_id ?? 'unknown';
            $data[$item->user_id]['created'][$item->month][$item->week] = $item->count;
            $month[$item->month] = $item->month;
        }
        // dd($month);
        return [
            // 'status'            => $status,
            'data'              => $data,
            'status_of_month'   => $status_of_month,
            'max_week_of_month' => $max_week_of_month,
            'status_of_user'    => $status_of_user,
            'month'             => $month
        ];
    }

    public function applyBodyshop(Request $request)
    {
        // Fetch all required data in a single query
        $rawData = DB::table('log_change_status_applies')
            ->join('apply_jobs', 'log_change_status_applies.apply_job_id', '=', 'apply_jobs.id')
            ->join('status as child_status', 'log_change_status_applies.status_id', '=', 'child_status.id')
            ->leftJoin('status as parent_status', 'child_status.parent_id', '=', 'parent_status.id')
            ->join('jobs', 'apply_jobs.job_id', '=', 'jobs.id')
            ->select(
                'parent_status.id as parent_id',
                'parent_status.name as parent_name',
                'parent_status.order as parent_order',
                'child_status.id as child_id',
                'child_status.name as child_name',
                'child_status.order as child_order',
                'jobs.id as job_id',
                'jobs.title as job_title',
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('parent_status.id', 'child_status.id', 'jobs.id')
            ->orderBy('parent_status.order')
            ->orderBy('child_status.order')
            ->get();

        // Process the raw data into the desired structure
        $reportData = [];
        foreach ($rawData as $row) {
            $parentId = $row->parent_id ?? 'null';
            $childId = $row->child_id;
            $jobId = $row->job_id;

            if (!isset($reportData[$parentId])) {
                $reportData[$parentId] = [
                    'name' => $row->parent_name ?? 'Ungrouped',
                    'statuses' => [],
                    'total' => 0
                ];
            }

            if (!isset($reportData[$parentId]['statuses'][$childId])) {
                $reportData[$parentId]['statuses'][$childId] = [
                    'name' => $row->child_name,
                    'jobs' => [],
                    'total' => 0
                ];
            }

            $reportData[$parentId]['statuses'][$childId]['jobs'][$jobId] = [
                'title' => $row->job_title,
                'count' => $row->count
            ];

            $reportData[$parentId]['statuses'][$childId]['total'] += $row->count;
            $reportData[$parentId]['total'] += $row->count;
        }

        // Sort the data
        ksort($reportData);
        foreach ($reportData as &$group) {
            ksort($group['statuses']);
        }
        return $reportData;
    }
    public function applyBodyshopByJob($job_id)
    {
        # get total pendding apply by job current
        $pending_status_ids = [27, 34, 38];
        $total_pending = ApplyJob::where('job_id', $job_id)
            ->select(DB::raw('COUNT(*) as total'), 'status')
            ->whereIn('status', $pending_status_ids)
            ->groupBy('status')->get()->pluck('total', 'status')->toArray();
        // dd($total_pending);

        // Fetch all required data in a single query
        $rawData = DB::table('log_change_status_applies')
            ->join('apply_jobs', 'log_change_status_applies.apply_job_id', '=', 'apply_jobs.id')
            ->join('status as child_status', 'log_change_status_applies.status_id', '=', 'child_status.id')
            ->leftJoin('status as parent_status', 'child_status.parent_id', '=', 'parent_status.id')
            // ->join('jobs', 'apply_jobs.job_id', '=', 'jobs.id')
            ->select(
                'parent_status.id as parent_id',
                'parent_status.name as parent_name',
                // 'parent_status.order as parent_order',
                'child_status.id as child_id',
                // 'child_status.parent_id as parent_id',
                'child_status.name as child_name',
                // 'child_status.order as child_order',
                'log_change_status_applies.status_id',
                DB::raw('COUNT(*) as count')
            )
            ->where('apply_jobs.job_id', $job_id)
            ->groupBy('log_change_status_applies.status_id', 'parent_status.id', 'child_status.id', 'child_status.name', 'parent_status.name')
            // ->groupBy('child_status.id')
            // ->orderBy('parent_status.order')
            // ->orderBy('child_status.order')
            ->get();

        // Process the raw data into the desired structure
        $reportData = [];
        // dd($rawData->toArray());
        foreach ($rawData as $row) {
            $parentId = $row->parent_id ?? 'null';
            $childId = $row->child_id;
            // $jobId = $row->job_id;

            if (!isset($reportData[$parentId])) {
                $reportData[$parentId] = [
                    'name' => $row->parent_name ?? 'Ungrouped',
                    'statuses' => [],
                    'total' => 0
                ];
            }

            if (!isset($reportData[$parentId]['statuses'][$childId])) {
                $reportData[$parentId]['statuses'][$childId] = [
                    'name' => $row->child_name,
                    'jobs' => [],
                    'total' => 0
                ];
            }

            if (in_array($childId, $pending_status_ids)) {
                $count = $total_pending[$childId] ?? 0;
            } else {
                $count = $row->count;
            }

            $reportData[$parentId]['statuses'][$childId]['count'] = $count;

            $reportData[$parentId]['statuses'][$childId]['total'] += $count;
            $reportData[$parentId]['total'] += $count;
        }

        // Sort the data
        ksort($reportData);
        foreach ($reportData as &$group) {
            ksort($group['statuses']);
        }
        // dd($reportData);
        return $reportData;
    }
}
