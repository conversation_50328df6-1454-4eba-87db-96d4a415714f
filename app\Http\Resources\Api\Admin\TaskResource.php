<?php

namespace App\Http\Resources\Api\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $titleTasks = $this->subTasks->pluck('title')->toArray();
        $activeTask = $this->subTasks->pluck('active')->toArray();
        $idTask = $this->subTasks->pluck('id')->toArray();
        return [
            'id'        => $this->id,
            'user_name'   => optional($this->user)->name,
            'user_email'   => optional($this->user)->email,
            'created_by_email'   => optional($this->created_by_user)->email,
            'name'        => $this->name,
            'follower'    => UserResource::collection($this->followers),
            'description' => $this->description,
            'status_id'   => $this->status_id,
            'priority'    => $this->priority,
            'start_date'  => $this->start_date,
            'user_id'     => $this->user_id,
            'end_date'    => $this->end_date,
            'created_at'  => Carbon::parse($this->created_at)->format('d-m-Y H:i:s'),
            'subTasks_title' => $titleTasks,
            'subTasks_active' => $activeTask,
            'subTasks_id' => $idTask,
        ];
    }
}
