<?php

namespace App\Http\Resources\Api\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LogChangeApplyJobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $parent_status = '';
        if ($this->status) {
            $parent_status = optional($this->status->parent)->name;
        }
        return [
            'id' => $this->id,
            'status_id' => $this->status_id,
            'status_name' => optional($this->status)->name,
            'parent_status_name' => $parent_status,
            'date_status' => Carbon::parse($this->date_status)->format('d/m/Y H:i:s'),
            'created_at' => Carbon::parse($this->created_at)->format('d/m/Y H:i:s'),
            'note' => $this->note,
            'user_id' => $this->user_id,
            'user_name' => optional($this->user)->name,
        ];
    }
}
