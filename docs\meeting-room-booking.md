# Hướng dẫn sử dụng chức năng Đặt lịch phòng họp

## Tổng quan
Chức năng đặt lịch phòng họp cho phép người dùng:
- <PERSON><PERSON><PERSON>n lý danh sách phòng họp (CRUD)
- Đặt lịch sử dụng phòng họp
- Xem lịch phòng họp dạng calendar
- G<PERSON><PERSON> email thông báo cho người tham gia

## Cài đặt

### 1. Chạy migration
```bash
php artisan migrate
```

### 2. Seed dữ liệu mẫu (tùy chọn)
```bash
php artisan db:seed --class=MeetingRoomSeeder
```

### 3. Build assets
```bash
npm run dev
# hoặc
npm run build
```

### 4. Chạy queue worker để gửi email
```bash
php artisan queue:work
```

## Các tính năng

### 1. <PERSON><PERSON><PERSON>n lý phòng họp
- Truy cập: `/admin/meeting-room`
- <PERSON><PERSON><PERSON> chức năng:
  - Thêm phòng họp mới
  - Sửa thông tin phòng họp
  - X<PERSON>a phòng họp
  - Quản lý tiện ích của phòng (máy chiếu, bảng trắng, v.v.)

### 2. Đặt lịch họp
- Truy cập: `/admin/meeting-booking`
- Các chức năng:
  - Tạo lịch họp mới
  - Chọn phòng họp
  - Chọn thời gian (hệ thống tự động kiểm tra phòng trống)
  - Tag người tham gia
  - Thêm agenda/nội dung cuộc họp
  - Thêm link họp online (nếu có)

### 3. Xem lịch phòng họp
- Truy cập: `/admin/meeting-calendar`
- Hiển thị dạng calendar
- Click vào sự kiện để xem chi tiết
- Các màu sắc theo trạng thái:
  - Cam: Chờ duyệt
  - Xanh: Đã duyệt
  - Đỏ: Từ chối
  - Xám: Đã hủy

### 4. Gửi email thông báo
- Tự động gửi email khi tạo lịch họp
- Email chứa:
  - Thông tin cuộc họp
  - Thời gian, địa điểm
  - Link xem chi tiết

## API Endpoints

### Calendar API
```
GET /api/admin/meeting-bookings/calendar
```
Trả về danh sách các cuộc họp cho calendar view

## Models và Relationships

### MeetingRoom
- Các trường: name, location, capacity, description, facilities, is_active
- Quan hệ: hasMany(MeetingBooking)

### MeetingBooking
- Các trường: title, description, meeting_room_id, created_by, start_time, end_time, status, meeting_link, agenda
- Quan hệ:
  - belongsTo(MeetingRoom)
  - belongsTo(User) as creator
  - hasMany(MeetingParticipant)
  - belongsToMany(User) through participants

### MeetingParticipant
- Các trường: meeting_booking_id, user_id, status, is_organizer, email_sent
- Quan hệ:
  - belongsTo(MeetingBooking)
  - belongsTo(User)

## Quyền truy cập
Hiện tại chức năng chưa phân quyền riêng. Tất cả user đăng nhập đều có thể:
- Xem và quản lý phòng họp
- Đặt lịch họp
- Xem calendar

Để thêm phân quyền, có thể thêm các permission:
- meeting-room.index, meeting-room.create, meeting-room.update, meeting-room.delete
- meeting-booking.index, meeting-booking.create, meeting-booking.update, meeting-booking.delete

## Lưu ý
- Cần cấu hình email trong file `.env` để gửi email thông báo
- Cần chạy queue worker để xử lý gửi email
- Thời gian đặt phòng phải trong tương lai
- Hệ thống tự động kiểm tra trùng lịch khi đặt phòng
