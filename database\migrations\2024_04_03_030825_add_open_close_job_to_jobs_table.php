<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jobs', function (Blueprint $table) {
            $table->date('opening_date')->nullable();
            $table->date('closing_date')->nullable();
            $table->string('note_opening_closing')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jobs', function (Blueprint $table) {
            $table->dropColumn('opening_date');
            $table->dropColumn('closing_date');
            $table->dropColumn('note_opening_closing');
        });
    }
};
