# Workflow: Thêm Filter Dựa Trên created_by và status cho ApplyJob DataTable

**Ngày:** 27/01/2025

## Mục tiêu

Thêm filter dựa trên `created_by` và `status` cho DataTable ApplyJob để người dùng có thể lọc dữ liệu theo người tạo và trạng thái.

## Các file đã chỉnh sửa

### 1. app/DataTables/ApplyJobDataTable.php

#### Thay đổi:

-   **Sửa lỗi linter:** Thêm property `protected $user;` để fix lỗi undefined property
-   **Thêm AJAX data handling:** Cập nhật `$this->ajaxData` để gửi filter parameters (created_by, status) qua AJAX
-   **Thêm JavaScript handling:** Cập nhật `$this->dataParameter` để xử lý sự kiện filter và reset
-   **Thêm method `getFilterHtml()`:** Tạo HTML cho filter form với:
    -   Select dropdown cho "Người tạo" (created_by)
    -   Select dropdown cho "Trạng thái" (status)
    -   Buttons "Lọc" và "Reset"

### 2. app/Services/ApplyJobService.php

#### Thay đổi:

-   **Thêm filter created_by:** Thêm điều kiện `->when(!empty($filters['created_by']))` trong method `queryList()`
-   **Thêm filter status:** Thêm điều kiện `->when(!empty($filters['status']))` trong method `queryList()`

## Cách sử dụng

### Trong Controller hoặc View:

```php
// Để hiển thị filter form
$dataTable = new ApplyJobDataTable($applyJobService);
$filterHtml = $dataTable->getFilterHtml();

// Trong view
{!! $filterHtml !!}
```

### Filter form sẽ có:

1. **Người tạo:** Dropdown chọn user từ bảng users
2. **Trạng thái:** Dropdown chọn status từ bảng statuses (group = 'apply-job')
3. **Nút Lọc:** Reload DataTable với filter parameters
4. **Nút Reset:** Xóa filter và reload DataTable

## Lưu ý kỹ thuật

-   Filter được truyền qua AJAX parameters: `created_by` và `status`
-   Service layer xử lý filter thông qua `queryList()` method
-   JavaScript được tích hợp trong DataTable configuration
-   Filter form sử dụng Bootstrap classes

## Tình trạng

✅ **Hoàn thành:** Đã implement filter cho cả created_by và status
✅ **Hoàn thành:** Đã fix lỗi linter về undefined property
✅ **Hoàn thành:** Đã tạo HTML filter form
✅ **Hoàn thành:** Đã tích hợp JavaScript handling
