document.addEventListener('notify', function (e) {

    if (e.detail[0].type == 'success') {
        new Noty({type: "success", text: e.detail[0].message,}).show();
    } else {
        new Noty({type: "warning", text: e.detail[0].message,}).show();
    }
})
if (typeof crud !== 'undefined'){
   crud.field('company').onChange( function (field) {
        if (field.value === '') {
            crud.field('contact_id').disable()
        }
        else {
            crud.field('contact_id').enable();
        }
    }).change();
}

