<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Cv;
use App\Models\JiraCv;
use App\Models\Skill;
use Illuminate\Support\Str;

class UpdateCVsFromJira extends Command
{
    protected $signature = 'update:cvs-from-jira';
    protected $description = 'Update CVs from Jira data';

    public function handle()
    {
        $this->info('Starting CV update process...');

        CV::where('source_name', 'JIRA')->where('id', '>', 1069)->chunk(100, function ($cvs) {
            foreach ($cvs as $cv) {
                $this->processCV($cv);
            }
        });

        $this->info('CV update process completed.');
    }

    private function processCV($cv)
    {
        $jiraCV = JiraCV::find($cv->source_id);

        if (!$jiraCV) {
            $this->warn("No matching Jira CV found for CV ID: {$cv->id}");
            return;
        }

        $jobFamily = $jiraCV->job_family;

        if (!$jobFamily) {
            $this->warn("No job family found for Jira CV ID: {$jiraCV->id}");
            return;
        }

        $skill = Skill::firstOrCreate(
            ['name' => $jobFamily],
            ['slug' => Str::slug($jobFamily)]
        );

        $cv->skills()->syncWithoutDetaching([$skill->id]);

        $cv->job_title = $cv->job_title . " ({$skill->name})";
        $cv->save();

        $this->info("Updated CV ID: {$cv->id}");
    }
}
