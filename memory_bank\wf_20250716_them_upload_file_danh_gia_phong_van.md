# Workflow: Thê<PERSON> chức năng upload file đánh gi<PERSON> kết quả phỏng vấn

**<PERSON><PERSON><PERSON> thực hiện:** 16/07/2025  
**<PERSON><PERSON><PERSON><PERSON> thực hiện:** AI Assistant  
**<PERSON><PERSON> tả:** Thê<PERSON> chức năng upload và preview file đánh giá kết quả phỏng vấn ứng viên trong trang show của ApplyJobCrudController

## Tổng quan thay đổi

Thêm chức năng cho phép upload file đánh giá kết quả phỏng vấn (file office như Word, Excel, PowerPoint, PDF) và hiển thị nút xem file trong tab mới cho những apply job đã có file đánh giá.

## Chi tiết các thay đổi

### 1. Database Migration
**File:** `database/migrations/2025_07_16_102000_add_interview_evaluation_file_to_apply_jobs_table.php`
- Thêm trường `interview_evaluation_file` v<PERSON><PERSON> bảng `apply_jobs`
- <PERSON><PERSON><PERSON> dữ liệu: `string`, nullable
- Comment: 'File đánh giá kết quả phỏng vấn'

### 2. Model ApplyJob
**File:** `app/Models/ApplyJob.php`
- Thêm trường `interview_evaluation_file` vào `$fillable`
- Thêm accessor `getInterviewEvaluationFileUrlAttribute()` để generate URL file từ S3

### 3. Routes
**File:** `routes/backpack/custom.php`
- Thêm route POST: `apply-job/{id}/upload-interview-evaluation`
- Thêm route GET: `apply-job/{id}/preview-interview-evaluation`

### 4. Controller Methods
**File:** `app/Http/Controllers/Admin/ApplyJobCrudController.php`
- Thêm method `uploadInterviewEvaluation()`:
  - Validate file upload (pdf, doc, docx, xlsx, xls, ppt, pptx)
  - Kiểm tra quyền truy cập
  - Upload file lên S3 folder `crm-hri-2023/interview-evaluation`
  - Cập nhật database
- Thêm method `previewInterviewEvaluation()`:
  - Kiểm tra quyền truy cập
  - Redirect đến URL file trên S3

### 5. View Updates
**File:** `resources/views/admins/apply_job/show.blade.php`
- Thêm section hiển thị file đánh giá phỏng vấn
- Thêm nút "Xem file đánh giá" (nếu có file)
- Thêm nút "Upload/Cập nhật file đánh giá"
- Thêm modal upload file với form validation
- Thêm JavaScript xử lý upload file với AJAX

## Tính năng mới

### Upload File
- Hỗ trợ các định dạng: PDF, DOC, DOCX, XLSX, XLS, PPT, PPTX
- Upload lên AWS S3
- Validation file type và size
- Thông báo thành công/lỗi

### Preview File
- Mở file trong tab mới
- Sử dụng URL trực tiếp từ S3
- Kiểm tra quyền truy cập

### UI/UX
- Nút upload/cập nhật file
- Nút xem file (chỉ hiển thị khi có file)
- Modal upload với form validation
- Loading state khi upload
- Auto reload trang sau upload thành công

## Quyền truy cập
- Sử dụng method `canShowDetailJob()` để kiểm tra quyền
- Chỉ user có quyền xem detail job mới được upload/preview file

## Lưu trữ file
- Folder S3: `crm-hri-2023/interview-evaluation`
- Tự động tạo subfolder theo tháng/năm
- File name được hash để tránh trùng lặp

## Testing
Cần test các trường hợp:
1. Upload file thành công
2. Upload file sai định dạng
3. Upload file quá lớn
4. Preview file khi có quyền
5. Preview file khi không có quyền
6. Upload file khi không có quyền

## Notes
- File được lưu trên S3, không lưu local
- Sử dụng service `FileServiceS3` có sẵn
- Tương thích với hệ thống upload CV hiện tại
- Responsive design cho mobile
