<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\MeetingRoomRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class MeetingRoomCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class MeetingRoomCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\MeetingRoom::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/meeting-room');
        CRUD::setEntityNameStrings('phòng họp', 'phòng họp');
        if (!backpack_user()->can('meeting-room.manager')) {
            CRUD::denyAccess(['list', 'create', 'update', 'delete']);
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     * 
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::column('name')
            ->type('text')
            ->label('Tên phòng');
        
        CRUD::column('location')
            ->type('text')
            ->label('Vị trí');
        
        CRUD::column('capacity')
            ->type('number')
            ->label('Sức chứa');
        
        CRUD::column('is_active')
            ->type('boolean')
            ->label('Kích hoạt');
        
        CRUD::column('created_at')
            ->type('datetime')
            ->label('Ngày tạo');
    }

    /**
     * Define what happens when the Create operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(MeetingRoomRequest::class);

        CRUD::field('name')
            ->type('text')
            ->label('Tên phòng họp')
            ->wrapper(['class' => 'form-group col-md-6']);
        
        CRUD::field('location')
            ->type('text')
            ->label('Vị trí')
            ->wrapper(['class' => 'form-group col-md-6']);
        
        CRUD::field('capacity')
            ->type('number')
            ->label('Sức chứa')
            ->attributes(['min' => 1])
            ->wrapper(['class' => 'form-group col-md-6']);
        
        CRUD::field('is_active')
            ->type('checkbox')
            ->label('Kích hoạt')
            ->default(1)
            ->wrapper(['class' => 'form-group col-md-6']);
        
        CRUD::field('description')
            ->type('textarea')
            ->label('Mô tả');
        
        CRUD::field('facilities')
            ->type('table')
            ->label('Tiện ích')
            ->entity_singular('tiện ích')
            ->columns([
                'name' => 'Tên tiện ích',
                'quantity' => 'Số lượng'
            ])
            ->max(10)
            ->min(0);
    }

    /**
     * Define what happens when the Update operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}
