<?php

namespace App\Models;

use App\Services\StatusService;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;

class Lead extends Model implements AuditableContract
{
    use CrudTrait, HasFactory, Auditable, SoftDeletes;
    use Notifiable;

    protected $guarded = ['id'];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->created_by)) {
                $model->created_by = backpack_auth()->id();
            }
        });
        static::updated(function ($model) {
            if ($model->isDirty('status')) {
                $statusOriginal = Status::find($model->getOriginal('status'));
                $statusNew = Status::find($model->status);
                LogLead::create([
                    'lead_id' => $model->id,
                    'user_id' => backpack_auth()->id(),
                    'company_id' => null,
                    'contact_id' => null,
                    'status' => $statusNew->id,
                    'time_contact' => null,
                    'content' => 'Thay đổi trạng thái từ <strong>' . optional($statusOriginal)->name . '</strong> thành <strong>' . optional($statusNew)->name . '</strong>',
                ]);
            }
        });
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function statusData()
    {
        return $this->belongsTo(Status::class, 'status', 'id')->where('group', 'status-lead');
    }

    public function sourceData()
    {
        return $this->belongsTo(Status::class, 'source', 'slug_value')->where('group', 'lead-source');
    }

    public function logLead()
    {
        return $this->hasMany(LogLead::class);
    }

    public function lastLog()
    {
        return $this->logLead()->orderBy('created_at', 'desc')->first();
    }

    public function service()
    {
        return $this->belongsTo(Status::class,'service_id')->where('group', 'service-name');
    }

    public function canEditLeads()
    {
        $user = backpack_auth()->user();
        if ($user && ($user->id == optional($this->user)->id || $user->can('lead.all-data'))) {
            return true;
        }
        return false;
    }

    public function canChangeOwner()
    {
        $user = backpack_auth()->user();
        if (empty($this->user_id)) {
            return true;
        }
        if ($user && ($user->id == $this->user_id || $user->can('lead.all-data'))) {
            return true;
        }
        return false;
    }

    public function routeNotificationForMail($notification)
    {
        $user = User::where('id', $this->user_id)->pluck('name', 'email')->toArray();
        return $user;
    }
    /*
   |--------------------------------------------------------------------------
   | SCOPES
   |--------------------------------------------------------------------------
   */

    # scope where user_id is the logged in user or user_id is null
    public function scopeMine($query)
    {
        $user = backpack_auth()->user();
        if ($user->can('lead.all-data')) {
            return $query;
        }
        return $query->where(function ($query) use ($user) {
            $query->where('user_id', $user->id)->orWhereNull('user_id');
        });
    }

    
}
