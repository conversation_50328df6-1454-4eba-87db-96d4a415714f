crud.field('candidate').onChange(field => {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
        }
    });
    let url = candidate_ajax_find_candidate_by_id;
    let id = field.value;
    if (id !== '') {
        url = url.replace(":id", id);

        $.ajax({
            url: url,
            type: 'POST',
            success: function (result) {
                crud.field('name').input.value = result.name;
                crud.field('email').input.value = result.email;
                crud.field('mobile').input.value = result.mobile;
                crud.field('mobile').input.value = result.mobile;
                crud.field('dob').input.value = result.dob;
                crud.field('gender').input.value = result.gender;
                crud.field('gender').change();
                crud.field('linkedin').input.value = result.linkedin;
                crud.field('facebook').input.value = result.facebook;
                crud.field('university').input.value = result.university;
            }
        });
    } else {
        crud.field('name').input.value = '';
        crud.field('email').input.value = '';
        crud.field('mobile').input.value = '';
        crud.field('mobile').input.value = '';
        crud.field('yoe').input.value = '';

        crud.field('salary_current').input.value = '';
        crud.field('salary_expect').input.value = '';
        crud.field('dob').input.value = '';
        crud.field('linkedin').input.value = '';
        crud.field('facebook').input.value = '';
        crud.field('university').input.value = '';
        crud.field('careerLevel').input.value = null;
        crud.field('careerLevel').change();
        crud.field('gender').input.value = null;
        crud.field('gender').change();
        crud.field('academicLevel').input.value = null;
        crud.field('academicLevel').change();
        crud.field('careerLanguages').input.value = null;
        crud.field('careerLanguages').change();
        crud.field('skills').input.value = null;
        crud.field('skills').change();

    }
});
