<?php

namespace App\Http\Resources\Api\Admin;

use App\Helpers\Utils;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LogLeadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['user'] = new UserResource($this->user);
        $data['username'] = Utils::getUsernameFromEmail(optional($this->user)->email);
        $data['status'] = new StatusResource($this->statusData);
        $data['created_at'] = Carbon::parse($this->created_at)->format('d-m-Y H:i:s');  
        return $data;
    }
}

