<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\MeetingBooking;
use App\Models\MeetingRoom;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MeetingBookingController extends Controller
{
    /**
     * Return bookings for the calendar view.
     */
    public function calendar(): JsonResponse
    {
        $bookings = MeetingBooking::with(['meetingRoom', 'creator', 'participants.user'])
            ->where('status', '!=', 'cancelled')
            ->get();

        return response()->json($bookings);
    }

    /**
     * Store a newly created meeting booking.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'meeting_room_id' => 'required|exists:meeting_rooms,id',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'description' => 'nullable|string',
            'participants' => 'nullable|array',
            'participants.*' => 'exists:users,id',
            'external_participants' => 'nullable|array',
            'external_participants.*' => 'email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if room is available
        $meetingRoom = MeetingRoom::find($request->meeting_room_id);
        if (!$meetingRoom->isAvailable($request->start_time, $request->end_time)) {
            return response()->json([
                'success' => false,
                'message' => 'Phòng họp không có sẵn trong thời gian này'
            ], 409);
        }

        $meetingBooking = MeetingBooking::create([
            'title' => $request->title,
            'meeting_room_id' => $request->meeting_room_id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'description' => $request->description,
            'created_by' => backpack_user()->id,
            'status' => 'approved'
        ]);

        // Add participants
        if ($request->has('participants') && is_array($request->participants)) {
            foreach ($request->participants as $userId) {
                $meetingBooking->addParticipant($userId, false);
            }
        }

        // Add creator as organizer
        $meetingBooking->addParticipant(backpack_user()->id, true);

        // Add external participants (this would need a separate table or field)
        if ($request->has('external_participants') && is_array($request->external_participants)) {
            foreach ($request->external_participants as $email) {
                // For now, we'll store them in a JSON field or create external participant records
                // This depends on your database structure
            }
        }

        $meetingBooking->load(['meetingRoom', 'creator', 'participants.user']);

        return response()->json([
            'success' => true,
            'message' => 'Cuộc họp đã được tạo thành công',
            'data' => $meetingBooking
        ], 201);
    }

    /**
     * Get a specific meeting booking by ID.
     */
    public function show($id): JsonResponse
    {
        try {
            $meetingBooking = MeetingBooking::with(['meetingRoom', 'creator', 'participants.user'])
                ->findOrFail($id);

            return response()->json($meetingBooking);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cuộc họp không tồn tại'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải thông tin cuộc họp'
            ], 500);
        }
    }
}
