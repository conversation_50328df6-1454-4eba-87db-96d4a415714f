<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('apply_jobs', function (Blueprint $table) {
            $table->string('desired_salary')->nullable();
            $table->string('candidate_strengths')->nullable();
            $table->string('date_onboard')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('apply_jobs', function (Blueprint $table) {
            $table->dropColumn('desired_salary');
            $table->dropColumn('candidate_strengths');
            $table->dropColumn('date_onboard');
        });
    }
};
