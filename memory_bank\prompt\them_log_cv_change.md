Tôi muốn thực hiện các việc sau.

# T<PERSON><PERSON> nút "Cập nhật CV che mới" trong resources\views\admins\apply_job\show.blade.php

-   Nút nằm dưới "CV Private"
-   Bấm vào nút sẽ hiển thị ra 1 popup có các trường sau:
    -   input upload cv
    -   note

# Khi người dùng bấm lưu, hệ thống sẽ upload cv lên aws như bình thường, sau đó:

-   Tạo 1 bản ghi meta (sử dụng Zoha\Metable) gắp vào apply_jobs, key = "lasted_cv_private", value là file cv vừa upload lên
-   Lấy cv_private cũ để ghi lại log, tạo json string và gắn meta vào apply_jobs, với key là "cv_private_logs"

```
{
    old_path: **file apply_jobs.cv.cv_private cũ**,
    new_path: ** file vừa upload lên **
    user_id: id người dùng vừa upload
}
```

# <PERSON>ê<PERSON> cạnh nút "Cập nhật CV che mới", hiển thị nút/text "Lịch sử thay đổi CV ({Số lần update})"

Bấm vào nút sẽ lấy các bản ghi Meta có key = "cv_private_logs", và hiển thị modal danh sách các lần thay đổi
