<?php

namespace App\Listeners;

use App\Events\FTPBackupFileS3Event;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Artisan;

class FTPBackupFileS3Listener implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\FTPBackupFileS3Event $event
     * @return void
     */
    public function handle(FTPBackupFileS3Event $event)
    {
        Artisan::call('ftp-backup-file-s3', ['file' => $event->filePath]);
    }

}
