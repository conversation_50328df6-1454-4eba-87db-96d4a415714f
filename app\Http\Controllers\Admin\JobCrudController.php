<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\ApplyJobDataTable;
use App\Helpers\Utils;
use App\Http\Requests\JobRequest;
use App\Models\CareerLanguage;
use App\Models\CareerLevel;
use App\Models\Company;
use App\Models\Department;
use App\Models\Job;
use App\Models\Skill;
use App\Models\Status;
use App\Models\User;
use App\Services\ApplyJobService;
use App\Services\FileServiceS3;
use App\Services\ReportService;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Prologue\Alerts\Facades\Alert;

/**
 * Class JobCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class JobCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;


    protected $statusService;


    public function __construct(StatusService $statusService)
    {
        $this->statusService = $statusService;
        parent::__construct();
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Job::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/job');
        CRUD::setEntityNameStrings('job', 'jobs');
        CRUD::denyAccess('delete');
        CRUD::disableResponsiveTable();
        CRUD::enablePersistentTable();

        if (backpack_user()->can('job.export')) {
            CRUD::enableExportButtons();
        }
        if (!backpack_user()->can('job.index')) {
            CRUD::denyAccess('list');
        }
        if (!backpack_user()->can('job.edit')) {
            CRUD::denyAccess('update');
        }
        if (!backpack_user()->can('job.create')) {
            CRUD::denyAccess('create');
        }
        if (!backpack_user()->can('job.show')) {
            CRUD::denyAccess('show');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::disableResponsiveTable();
        $status_completed = Status::where('group', 'job-status')->whereIn('slug_value', ['done', 'close'])->pluck('id')->toArray();

        // Thêm widget thống kê ở đây
        $total_open = Job::whereHas('statusJob', function ($q) {
            $q->where('group', 'job-status')
                ->where('slug_value', 'open');
        })->count();

        $total_expired = Job::whereHas('statusJob', function ($q) {
            $q->where('group', 'job-status')
                ->where('slug_value', 'open');
        })
            ->where('closing_date', '<', Carbon::yesterday()->endOfDay())
            ->count();

        $total_closed = Job::whereHas('statusJob', function ($q) {
            $q->where('group', 'job-status')
                ->whereIn('slug_value', ['done', 'close']);
        })->count();

        Widget::add([
            'type'    => 'div',
            'class'   => 'row mb-4',
            'content' => [
                [
                    'type'       => 'progress',
                    'class'      => 'col-sm-4',
                    'value'      => $total_open,
                    'description' => 'Job đang mở',
                    'icon'       => 'la la-briefcase',
                    'progress'   => 100,
                    'progressClass' => 'progress-bar bg-primary',
                    'hint'       => 'Số lượng job đang trong trạng thái mở'
                ],
                [
                    'type'       => 'progress',
                    'class'      => 'col-sm-4',
                    'value'      => $total_expired,
                    'description' => 'Job quá hạn',
                    'icon'       => 'la la-clock',
                    'progress'   => 100,
                    'progressClass' => 'progress-bar bg-danger',
                    'hint'       => 'Số lượng job đã quá hạn đang mở'
                ],
                [
                    'type'       => 'progress',
                    'class'      => 'col-sm-4',
                    'value'      => $total_closed,
                    'description' => 'Job đã đóng',
                    'icon'       => 'la la-check-circle',
                    'progress'   => 100,
                    'progressClass' => 'progress-bar bg-success',
                    'hint'       => 'Số lượng job đã đóng'
                ],
            ]
        ]);

        //        CRUD::setFromDb(); // set columns from db columns.
        $this->crud->addClause('roleData');
        $this->filterData();
        $this->crud->column([
            'label' => 'id',
            'type' => 'text',
            'name' => 'id',
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'Tiêu đề job/Công ty',
            'type' => 'custom_html',
            'value' => function ($entry) {
                $html = '<strong>' . $entry->title . '</strong><br>';

                // Kiểm tra quyền hiển thị thông tin công ty
                if (backpack_user()->can('job.show-company-info') || !$entry->hide_to_recer) {
                    $html .= '<div > ' . $entry->company->company_abbreviation . '</div>';
                }

                $html .= '<div class="font-weight-bold text-primary"><strong>#' . $entry->job_code . '</strong></div>';
                return $html;
            },
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('title', 'like', '%' . $searchTerm . '%');

                // Chỉ cho phép search theo công ty nếu có quyền
                if (backpack_user()->can('job.show-company-info')) {
                    $query->orWhereHas('company', function ($q) use ($column, $searchTerm) {
                        $q->where('company_abbreviation', 'like', '%' . $searchTerm . '%');
                    });
                } else {
                    // Nếu là recer thì không hiển thị công ty bodyshop lúc search 
                    $query->orWhereHas('company', function ($q) use ($column, $searchTerm) {
                        $q->where('company_abbreviation', 'like', '%' . $searchTerm . '%');
                    })->where('type', '!=', config('constant.job_type.bodyshop'));
                }
            },
            'visibleInExport' => false
        ]);
        $this->crud->column([
            'label' => 'Tiêu đề job',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->title;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        // Chỉ hiển thị cột công ty trong export nếu có quyền
        if (backpack_user()->can('job.show-company-info')) {
            $this->crud->column([
                'label' => 'Công ty',
                'type' => 'closure',
                'function' => function ($entry) {
                    return $entry->company->company_abbreviation;
                },
                'exportOnlyColumn' => true,
                'visibleInExport' => true,
                'visibleInTable' => false,
            ]);
        }

        $this->crud->column([
            'label' => 'Trạng thái/Loại job',
            'type' => 'custom_html',
            'value' => function ($entry) {
                return '<strong>' . $entry->statusJob->name . '</strong><br><div > ' . $entry->typeJob->name . '</div>';
            },
            'visibleInExport' => false
        ]);
        $this->crud->column([
            'label' => 'Trạng thái',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->statusJob->name;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'Loại job',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->typeJob->name;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'description',
            'type' => 'closure',
            'function' => function ($entry) {
                return Utils::htmlToText($entry->description);
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'Requirement',
            'type' => 'closure',
            'function' => function ($entry) {
                return Utils::htmlToText($entry->requirement);
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'Benefit',
            'type' => 'closure',
            'function' => function ($entry) {
                return Utils::htmlToText($entry->benefit);
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'JD_File',
            'type' => 'closure',
            'function' => function ($entry) {
                return !empty($entry->path) ? secure_file_url($entry->id, 'Job', 'path') : '';
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'Độ ưu tiên',
            'type' => 'custom_html',
            'value' => function ($entry) {
                // return '<strong>' . $entry->priorityJob->name . '</strong><br><div > ' . $entry->statusJob->name . '</div>';
                return '<strong>' . $entry->priorityJob->name . '</strong><br><div >Số lượng: ' . $entry->vacancy . '</div>';
            },
            'visibleInExport' => false
        ]);

        $this->crud->column([
            'label' => 'Độ ưu tiên',
            'type' => 'closure',
            'function' => function ($entry) {
                // return '<strong>' . $entry->priorityJob->name . '</strong><br><div > ' . $entry->statusJob->name . '</div>';
                return $entry->priorityJob->name;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);



        $this->crud->column([
            'name' => 'closing_date',
            'label' => 'Ngày mở/đóng',
            'type' => 'custom_html',
            'value' => function ($entry) use ($status_completed) {
                // if (empty($entry->closing_date)) {
                //     return '';
                // }
                // $str = '<span class="">' . Carbon::parse($entry->opening_date)->format('d/m/Y') . '</span>';
                $str = '<span class="">' . Carbon::parse($entry->created_at)->format('d/m/Y') . '</span>';
                if (!empty($entry->closing_date)) {
                    $close_date = Carbon::parse($entry->closing_date)->format('d/m/Y');
                    $str .= ' - <span class="">' . $close_date . '</span> ';
                    if ($entry->closing_date < Carbon::yesterday()->endOfDay() && !in_array($entry->status_id, $status_completed)) {
                        $str .= ' <span class="badge bg-danger">Quá hạn</span>';
                    }
                }
                return $str;
                // return '<input type="date" name="end_date" class="form-control col-md-12" value="' . $entry->end_date . '">';
            },
            'visibleInExport' => false
        ]);
        $this->crud->column([
            'label' => 'Ngày mở',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->opening_date ? Carbon::parse($entry->opening_date)->format('d/m/Y') : '';
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'label' => 'Ngày đóng',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->closing_date ? Carbon::parse($entry->closing_date)->format('d/m/Y') : '';
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);

        $this->crud->column([
            'name' => 'recer',
            'label' => 'Rercer',
            'type' => 'custom_html',
            'value' => function ($entry) {
                $str = '';
                $name_recer = [];
                foreach ($entry->recerJob as $recer) {
                    $name_recer[] = '<span class="badge badge-pill badge-info">' . Utils::getUsernameFromEmail($recer->email) . '</span>';
                }
                foreach ($entry->recTeam as $recer) {
                    $name_recer[] = '<span class="badge badge-pill badge-warning">' . $recer->name . '</span>';
                }
                return implode(', ', $name_recer);
            },
            'visibleInExport' => false
        ]);
        $this->crud->column([
            'name' => 'countByApplyStatus',
            'label' => 'Count Status Apply',
            'type' => 'custom_html',
            'value' => function ($entry) {
                $status_count = $entry->countByApplyStatusLog();
                $str = "Đã ứng tuyển : <span class='badge badge-info'>" . (isset($status_count['da-ung-tuyen']) ? $status_count['da-ung-tuyen'] : 0) . "</span>";
                $str .= "<br>Đã submit : <span class='badge badge-danger'>" . (isset($status_count['sale-da-submit-sang-kh']) ? $status_count['sale-da-submit-sang-kh'] : 0) . "</span>";
                $str .= "<br>Offered : <span class='badge badge-danger'>" . (isset($status_count['chot-offer-offered']) ? $status_count['chot-offer-offered'] : 0) . "</span>";
                return $str;
            },
            'visibleInExport' => false
        ]);

        $this->crud->column([
            'label' => 'Người phụ trách/Ngày tạo ',
            'type' => 'custom_html',
            'value' => function ($entry) {
                return '<div>' . $entry->user->name . '</div><sub > ' . $entry->created_at . '</sub>';
            },
            'visibleInExport' => false
        ]);
        $this->crud->column([
            'name' => 'user',
            'label' => 'Người phụ trách',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->user->name;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'name' => 'created_at',
            'label' => 'Ngày tạo',
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->created_at;
            },
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);

        $this->crud->column([
            'label' => 'Số lượng',
            'type' => 'closure',
            'function' => function ($entry) {
                // return '<strong>' . $entry->priorityJob->name . '</strong><br><div > ' . $entry->statusJob->name . '</div>';
                return $entry->vacancy;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'name' => 'da_ung_tuyen',
            'label' => 'Đã ứng tuyển',
            'type' => 'closure',
            'function' => function ($entry) {
                $status_count = $entry->countByApplyStatusLog();
                return isset($status_count['da-ung-tuyen']) ? $status_count['da-ung-tuyen'] : 0;
            },
            'exportOnlyColumn' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'name' => 'sale_da_submit_sang_kh',
            'label' => 'Đã submit',
            'type' => 'closure',
            'function' => function ($entry) {
                $status_count = $entry->countByApplyStatusLog();
                return isset($status_count['sale-da-submit-sang-kh']) ? $status_count['sale-da-submit-sang-kh'] : 0;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'name' => 'chot_offer_offered',
            'label' => 'Offered',
            'type' => 'closure',
            'function' => function ($entry) {
                $status_count = $entry->countByApplyStatusLog();
                return isset($status_count['chot-offer-offered']) ? $status_count['chot-offer-offered'] : 0;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);
        $this->crud->column([
            'name' => 'da_onboard',
            'label' => 'Đã onboard',
            'type' => 'closure',
            'function' => function ($entry) {
                $status_count = $entry->countByApplyStatusLog();
                return isset($status_count['onboard-onboarded']) ? $status_count['onboard-onboarded'] : 0;
            },
            'exportOnlyColumn' => true,
            'visibleInExport' => true,
            'visibleInTable' => false,
        ]);

        // Thêm button nhân bản job
        if (backpack_user()->can('apply-job.create')) {
            CRUD::addButtonFromModelFunction('line', 'duplicate_job', 'duplicateJobButton', 'beginning');
        }

        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(JobRequest::class);
        CRUD::addFields($this->fieldData());
        Widget::add()->type('script')->content('assets/js/admin/currency.js');

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        // Kiểm tra quyền edit job của người khác
        $entry = $this->crud->getCurrentEntry();
        if ($entry && $entry->created_by != backpack_user()->id && !backpack_user()->can('job.show-company-info')) {
            abort(403, 'Bạn không có quyền chỉnh sửa job này');
        }

        $this->setupCreateOperation();
        // add job code field
        $this->crud->addField([
            'name' => 'job_code',
            'label' => 'Mã job',
            'type' => 'text',
            'wrapperAttributes' => ['class' => 'form-group col-md-2'],
            'attributes' => [
                'disabled' => 'disabled'
            ]
        ])->afterField('title');
    }

    protected function fieldData()
    {
        // kiểm tra entry có tồn tại không
        if ($this->crud->getCurrentEntryId()) {
            $title_class = 'form-group col-md-4';
            $job_code_field = fieldColumnData('job_code', 'Mã job', 'text', 'form-group col-md-2', 'required');
        } else {
            $title_class = 'form-group col-md-6';
            $job_code_field = null;
        }
        $fields = [
            fieldColumnData('title', 'Tên job', 'text', $title_class, 'required'),
            selectFormArrayData('type', 'Loại job', 'form-group col-md-2', $this->statusService->getStatusTypeJob(), 'required'),
            selectFormArrayData('priority', 'Độ ưu tiên', 'form-group col-md-2', $this->statusService->getStatusPriorityJob(), 'required'),
            selectFormArrayData('status', 'Trạng thái', 'form-group col-md-2', $this->statusService->getStatusJob(), 'required'),

            fieldColumnData('vacancy', 'Số lượng', 'text', 'form-group col-md-2', 'required'),
            fieldColumnData('opening_date', 'Ngày mở Job', 'date', 'form-group col-md-2',),
            fieldColumnData('closing_date', 'Ngày đóng Job', 'date', 'form-group col-md-2',),
            fieldColumnData('note_opening_closing', 'Lý do đóng Job (nếu có)', 'text', 'form-group col-md-6',),
            // [   // 1-n relationship
            //     'label'       => "End", // Table column heading
            //     'type'        => "select2_from_ajax",
            //     'method'      => 'POST',
            //     'name'        => 'company_id', // the column that contains the ID of that connected entity
            //     'entity'      => 'company', // the method that defines the relationship in your Model
            //     'attribute'   => "name", // foreign key attribute that is shown to user
            //     'data_source' => backpack_url("job/fetch/company"), // url to controller search function (with /{id} should return model)

            //     // OPTIONAL
            //     // 'delay'                   => 500, // the minimum amount of time between ajax requests when searching in the field
            //     // 'placeholder'             => "Select a category", // placeholder for the select
            //     // 'minimum_input_length'    => 2, // minimum characters to type before querying results
            //     // 'model'                   => "App\Models\Category", // foreign key model
            //     // 'dependencies'            => ['category'], // when a dependency changes, this select2 is reset to null
            //     // 'method'                  => 'POST', // optional - HTTP method to use for the AJAX call (GET, POST)
            //     // 'include_all_form_fields' => false, // optional - only send the current field through AJAX (for a smaller payload if you're not using multiple chained select2s)
            // ],
        ];

        // Chỉ thêm field company nếu có quyền
        if (backpack_user()->can('job.show-company-info')) {
            $fields[] = [
                'type'                 => "relationship",
                'validationRules'      => 'required',
                'name'                 => 'company',
                'ajax'                 => true,
                // 'placeholder'          => 'Chọn công ty',
                'entity'               => 'company',         // the method that defines the relationship in your Model
                'attribute'            => 'company_abbreviation',            // foreign key attribute that is shown to user
                // 'model'                => Company::class,    // foreign key model
                // 'minimum_input_length' => 2,
                // 'wrapper'              => [
                //     'class' => 'form-group col-md-3',
                // ],
                // 'inline_create'        => [                                         // specify the entity in singular
                //     'modal_class' => 'modal-dialog modal-xl modal-dialog-centered', // use modal-sm, modal-lg to change width
                // ],
            ];
        }

        $fields = array_merge($fields, [
            selectFormArrayData('form_contract', 'Hình thức kí HĐ', 'form-group col-md-2', $this->statusService->getStatusJobContract(), ''),
            fieldColumnData('address', 'Địa chỉ', 'text', 'form-group col-md-3', 'required'),
            select2ModelData('Level', 'select2_multiple', 'levels', CareerLevel::class, 'name_vi', 'form-group col-md-4', 'required'),
            select2ModelData('Ngoại ngữ', 'select2_multiple', 'languages', CareerLanguage::class, 'name_vi', 'form-group col-md-3', 'required'),
            // fieldColumnData('min_salary', 'Mức lương tối thiểu', 'number', 'form-group col-md-2', 'required'),
            // fieldColumnData('max_salary', 'Mức lương tối đa', 'number', 'form-group col-md-2','required'),
            // selectFormArrayData('currency', 'Đơn vị tiền tệ', 'form-group col-md-2', $this->statusService->getStatusCurrency(), ''),
            // fieldColumnData('vacancy', 'Số lượng', 'text', 'form-group col-md-2', 'required'),
            [
                'name' => 'min_salary',
                'label' => 'Mức lương tối thiểu',
                'type'     => 'text',

                'wrapperAttributes' => ['class' => 'form-group col-md-3'],
                'attributes' => ['data-type' => 'currency'],
            ],
            [
                'name' => 'max_salary',
                'label' => 'Mức lương tối đa',
                'type'     => 'text',
                'wrapperAttributes' => ['class' => 'form-group col-md-3'],
                'attributes' => ['data-type' => 'currency'],
            ],
            selectFormArrayData('currency', 'Đơn vị tiền tệ', 'form-group col-md-3', $this->statusService->getStatusCurrency(), ''),
            select2ModelData('Job Recer', 'select2_multiple', 'recerJob', User::class, 'user_name', 'form-group col-md-3'),
            select2ModelData('Job Rec Team', 'select2_multiple', 'recTeam', Department::class, 'name', 'form-group col-md-3', 'required'),
            select2ModelData('Kĩ năng', 'select2_multiple', 'skills', Skill::class, 'name', 'form-group col-md-6', ''),

            inputUpload('path', 'Tệp đính kém (ưu tiên file pdf)', 'form-group col-md-9', 's3', ''),
            [
                # create select 2 user by created_by
                'type'                 => "relationship",
                'name'                 => 'created_by',
                'label'                => 'Người tạo',
                'ajax'                 => false,
                'placeholder'          => 'Chọn người tạo',
                'entity'               => 'user',         // the method that defines the relationship in your Model
                'attribute'            => 'user_name',            // foreign key attribute that is shown to user
                'model'                => User::class,    // foreign key model
                'minimum_input_length' => 0,
                'wrapper'              => [
                    'class' => 'form-group col-md-3',
                ],
            ],
            [
                'name' => 'description',
                'label' => 'Description',
                'type' => 'tinymce',
                'options' => [
                    'height' => 300,
                    'autoGrow_minHeight' => 500,
                    'autoGrow_bottomSpace' => 50,
                ],
            ],
            [
                'name' => 'requirement',
                'label' => 'Yêu cầu',
                'type' => 'tinymce',
                'options' => [
                    'height' => 300,
                    'autoGrow_minHeight' => 500,
                    'autoGrow_bottomSpace' => 50,
                ],
            ],
            [
                'name' => 'benefit',
                'label' => 'Quyền lợi',
                'type' => 'tinymce',
                'options' => [
                    'height' => 300,
                    'autoGrow_minHeight' => 500,
                    'autoGrow_bottomSpace' => 50,
                ],
            ],
            [
                'name'       => 'note',
                'label'      => 'Ghi chú',
                'type'       => 'textarea',
                'attributes' => [
                    'rows' => 3,
                ],
            ],
        ]);

        return $fields;
    }

    public function fetchCompany()
    {
        // Kiểm tra quyền trước khi fetch company
        if (!backpack_user()->can('job.show-company-info')) {
            abort(403, 'Bạn không có quyền truy cập thông tin công ty');
        }

        $query = request()->get('q');
        return $this->fetch([
            'model' => Company::class, // required
            'searchable_attributes' => [],
            'paginate' => 10, // items to show per page
            // 'searchOperator' => 'ILIKE',
            'query' => function ($model) use ($query) {
                // return $model->where('is_active', 1);
                // return $model;
                return $model->where(function ($q) use ($query) {
                    $q->where('company_abbreviation', 'like', '%' . $query . '%')
                        ->orWhere('name', 'like', '%' . $query . '%');
                })->select('id', 'company_abbreviation', 'name');
            } // to filter the results that are returned
        ]);
        // return $this->fetch(\App\Models\Company::class);
    }

    public function store()
    {

        $this->crud->hasAccessOrFail('create');

        // execute the FormRequest authorization and validation, if one is required
        $request = $this->crud->validateRequest();

        // register any Model Events defined on fields
        $this->crud->registerFieldEvents();

        // insert item in the db
        $data = $this->crud->getStrippedSaveRequest($request);

        $data['created_by'] = backpack_auth()->user()->id;
        if (!empty($data['path']) && is_file($data['path'])) {
            $data['path'] = FileServiceS3::getInstance()->uploadToS3($data['path'], PATH_FOLDER_SAVE_JOB);
        }
        $data['min_salary'] = str_replace(',', '', $data['min_salary']);
        $data['max_salary'] = str_replace(',', '', $data['max_salary']);
        $data['min_salary'] = explode('.', $data['min_salary'])[0];
        $data['max_salary'] = explode('.', $data['max_salary'])[0];
        $item = $this->crud->create($data);
        $this->data['entry'] = $this->crud->entry = $item;

        // show a success message
        \Alert::success(trans('backpack::crud.insert_success'))->flash();

        // save the redirect choice for next time
        $this->crud->setSaveAction();

        return $this->crud->performSaveAction($item->getKey());
    }

    public function update()
    {
        $this->crud->hasAccessOrFail('update');

        // Kiểm tra quyền edit job của người khác
        $entry = $this->crud->getCurrentEntry();
        if ($entry && $entry->created_by != backpack_user()->id && !backpack_user()->can('job.show-company-info')) {
            abort(403, 'Bạn không có quyền chỉnh sửa job này');
        }

        // execute the FormRequest authorization and validation, if one is required
        $request = $this->crud->validateRequest();

        // register any Model Events defined on fields
        $this->crud->registerFieldEvents();

        $data = $this->crud->getStrippedSaveRequest($request);
        if (!empty($data['path']) && is_file($data['path'])) {
            $data['path'] = FileServiceS3::getInstance()->uploadToS3($data['path'], PATH_FOLDER_SAVE_JOB);
        }
        $data['min_salary'] = str_replace(',', '', $data['min_salary']);
        $data['max_salary'] = str_replace(',', '', $data['max_salary']);
        $data['min_salary'] = explode('.', $data['min_salary'])[0];
        $data['max_salary'] = explode('.', $data['max_salary'])[0];
        if (!backpack_user()->can('job.change-owner')) {
            unset($data['created_by']);
        } else {
            // $data['created_by'] = backpack_auth()->user()->id;
        }
        // update the row in the db
        $item = $this->crud->update($request->get($this->crud->model->getKeyName()), $data);
        $this->data['entry'] = $this->crud->entry = $item;

        // show a success message
        \Alert::success(trans('backpack::crud.update_success'))->flash();

        // save the redirect choice for next time
        $this->crud->setSaveAction();

        return $this->crud->performSaveAction($item->getKey());
    }

    protected function setupShowOperation()
    {
        $this->crud->setShowView('admins.jobs.show');

        Widget::add([
            'type'    => 'style',
            'content' => 'assets/css/admin/custom.css',
        ]);
    }

    public function show(ApplyJobDataTable $dataTable, $id)
    {
        $this->crud->hasAccessOrFail('show');

        // get entry ID from Request (makes sure its the last ID for nested resources)
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $entry = $this->crud->getCurrentEntry();
        // dd($entry);

        $reportService = resolve(ReportService::class);
        $reportData = $reportService->applyBodyshopByJob($id);
        if (!backpack_user()->canShowDetailJob($entry)) {
            abort(403, 'Không có quyền truy cập');
        }

        // get the info for that entry (include softDeleted items if the trait is used)
        if ($this->crud->get('show.softDeletes') && in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses($this->crud->model))) {
            $this->data['entry'] = $this->crud->getModel()->withTrashed()->findOrFail($id);
        } else {
            $this->data['entry'] = $this->crud->getEntryWithLocale($id);
        } // Trong controller
        $applyJobService = resolve(ApplyJobService::class);
        $dataTable = new ApplyJobDataTable($applyJobService);
        $this->data['filterHtml'] = $dataTable->getFilterHtml($id);

        $this->data['reportData'] = $reportData;
        $this->data['crud'] = $this->crud;
        $this->data['title'] = $this->crud->getTitle() ?? trans('backpack::crud.preview') . ' ' . $this->crud->entity_name;

        // load the view from /resources/views/vendor/backpack/crud/ if it exists, otherwise load the one in the package
        return $dataTable->render($this->crud->getShowView(), $this->data);
    }

    /**
     * Nhân bản job
     */
    public function duplicate($id)
    {
        // Kiểm tra quyền
        if (!backpack_user()->can('apply-job.create')) {
            abort(403, 'Không có quyền thực hiện thao tác này');
        }

        $originalJob = Job::with(['languages', 'levels', 'skills', 'recTeam', 'recerJob'])->findOrFail($id);

        try {
            DB::beginTransaction();

            // Sao chép job chính
            $newJob = $originalJob->replicate();
            $newJob->created_by = backpack_auth()->user()->id;
            $newJob->job_code = null; // Sẽ được tự động tạo trong boot()
            $newJob->title = $originalJob->title . ' (Bản sao)';
            $newJob->created_at = now();
            $newJob->updated_at = now();
            $newJob->save();

            // Sao chép relationships
            // 1. Languages
            if ($originalJob->languages->isNotEmpty()) {
                $languageIds = $originalJob->languages->pluck('id')->toArray();
                $newJob->languages()->attach($languageIds);
            }

            // 2. Levels
            if ($originalJob->levels->isNotEmpty()) {
                $levelIds = $originalJob->levels->pluck('id')->toArray();
                $newJob->levels()->attach($levelIds);
            }

            // 3. Skills  
            if ($originalJob->skills->isNotEmpty()) {
                $skillIds = $originalJob->skills->pluck('id')->toArray();
                $newJob->skills()->attach($skillIds);
            }

            // 4. RecTeam
            if ($originalJob->recTeam->isNotEmpty()) {
                $recTeamIds = $originalJob->recTeam->pluck('id')->toArray();
                $newJob->recTeam()->attach($recTeamIds);
            }

            // 5. RecerJob
            if ($originalJob->recerJob->isNotEmpty()) {
                $recerIds = $originalJob->recerJob->pluck('id')->toArray();
                $newJob->recerJob()->attach($recerIds);
            }

            DB::commit();

            \Alert::success('Nhân bản job thành công! Job mới có mã: ' . $newJob->job_code)->flash();

            return redirect(backpack_url('job/' . $newJob->id . '/edit'));
        } catch (\Exception $e) {
            DB::rollback();
            \Alert::error('Có lỗi xảy ra khi nhân bản job: ' . $e->getMessage())->flash();
            return redirect()->back();
        }
    }

    public function filterData()
    {
        $this->crud->addFilter(
            [
                'name' => 'createdBy',
                'type' => 'select2_multiple',
                'label' => 'Người phụ trách '
            ],
            function () {
                return User::pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('createdBy', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        // $this->crud->addFilter(
        //     [
        //         'name' => 'title',
        //         'type' => 'select2_multiple',
        //         'label' => 'Job title'
        //     ],
        //     function () {
        //         return Job::pluck('title', 'id')->toArray();
        //     },
        //     function ($value) {
        //         $this->crud->query->whereIn('id', json_decode($value));
        //     }
        // );

        $this->crud->addFilter(
            [
                'name' => 'typeJob',
                'type' => 'select2_multiple',
                'label' => 'Loại Job '
            ],
            function () {
                return Status::where('group', 'job-type')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('typeJob', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'statusJob',
                'type' => 'select2_multiple',
                'label' => 'Status Job '
            ],
            function () {
                return Status::where('group', 'job-status')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('statusJob', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        // Chỉ hiển thị filter công ty nếu có quyền
        if (backpack_user()->can('job.show-company-info')) {
            $this->crud->addFilter(
                [
                    'name' => 'company',
                    'type' => 'select2_multiple',
                    'label' => 'Company '
                ],
                function () {
                    return Company::pluck('company_abbreviation', 'id')->toArray();
                },
                function ($value) {
                    $this->crud->query->whereHas('company', function ($query) use ($value) {
                        $query->whereIn('id', json_decode($value));
                    });
                }
            );
        }

        $this->crud->addFilter([
            'name' => 'recerJob',
            'type' => 'select2_multiple',
            'label' => 'Recer',
        ], function () {
            $user = User::all();
            foreach ($user as $item) {
                $user_name[$item->id] = Utils::getUsernameFromEmail($item->email);
            }
            return $user_name;
        }, function ($value) {
            $this->crud->query->whereHas('recerJob', function ($query) use ($value) {
                $query->whereIn('recer_id', json_decode($value));
            });
        });

        $this->crud->addFilter([
            'name' => 'recTeam',
            'type' => 'select2_multiple',
            'label' => 'Rec Team',
        ], function () {
            return Department::pluck('name', 'id')->toArray();
        }, function ($value) {
            $this->crud->query->whereHas('recTeam', function ($query) use ($value) {
                $query->whereIn('department_id', json_decode($value));
            });
        });
        CRUD::filter('from_to')
            ->type('date_range')
            ->label('Thời gian tạo')
            ->whenActive(function ($value) {
                $dates = json_decode($value);
                CRUD::addClause('where', 'created_at', '>=', $dates->from);
                CRUD::addClause('where', 'created_at', '<=', $dates->to . ' 23:59:59');
            });
    }
}
