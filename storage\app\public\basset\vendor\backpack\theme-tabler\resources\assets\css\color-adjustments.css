/**
    This file includes styles to help apply a skin color on top of Tabler's default
    and it ensures all css color variables declared in "./colors.css" are applied correctly,
    particularly for dark mode.
*/

[data-bs-theme=dark] ::placeholder {
    color: rgba(var(--tblr-light-text), 0.5) !important;
}

[data-bs-theme=dark] ul.pagination li.paginate_button.page-item.active a.page-link {
    color: var(--tblr-light) !important;
}

[data-bs-theme=dark] .select2-selection--multiple input:not(:focus) {
    color: rgba(var(--tblr-light-text), 0.5) !important;
}

/*cards*/
[data-bs-theme=dark] .card-body {
    box-shadow: inset 0 calc(-1 * var(--tblr-navbar-border-width)) 0 0 var(--tblr-light-border-subtle) !important;
}

[data-bs-theme=dark] .card {
    border-color: var(--tblr-light-border-subtle);
}

[data-bs-theme=dark] .card-header,
[data-bs-theme=dark] .card-footer {
    background: var(--tblr-bg-surface);
    border-color: var(--tblr-light-border-subtle) !important;
}

.card-footer a:hover .text-primary {
    text-decoration: underline;
    text-decoration-color: var(--tblr-primary);
}

.card-footer a:hover .text-secondary {
    text-decoration: underline;
    text-decoration-color: var(--tblr-secondary);
}

.card-footer a:hover .text-success {
    text-decoration: underline;
    text-decoration-color: var(--tblr-success);
}

.card-footer a:hover .text-danger {
    text-decoration: underline;
    text-decoration-color: var(--tblr-danger);
}

.card-footer a:hover .text-warning {
    text-decoration: underline;
    text-decoration-color: var(--tblr-warning);
}

.card-footer a:hover .text-info {
    text-decoration: underline;
    text-decoration-color: var(--tblr-info);
}

.progress {
    background-color: var(--tblr-light-border-subtle) !important;
}

/*inputs*/
[data-bs-theme=dark] input[type=checkbox]:not(:checked).form-check-input {
    background-color: var(--tblr-primary-border-subtle);
}

[data-bs-theme=dark] input[type=checkbox]:not(:checked).form-check-input::after {
    background-color: var(--tblr-bg-surface);
}

[data-bs-theme=dark] input[type=checkbox]:checked.form-check-input {
    background-color: var(--tblr-primary);
    border-color: var(--tblr-primary);
}

[data-bs-theme=dark] input:not(input[type=hidden]).form-control,
[data-bs-theme=dark] textarea.form-control,
[data-bs-theme=dark] select.form-control,
[data-bs-theme=dark] select.form-select,
[data-bs-theme=dark] :not(.navbar-filters) .select2-selection--single,
[data-bs-theme=dark] .select2-container .select2-dropdown .select2-search input.select2-search__field {
    background-color: var(--tblr-bg-surface);
    border-color: var(--tblr-primary-border-subtle);
    color: rgba(var(--tblr-light-text), 0.5);
}

[data-bs-theme=dark] .select2-container--bootstrap .select2-selection--single .select2-selection__rendered {
    color: rgba(var(--tblr-light-text), 0.5);
}

[data-bs-theme=dark] input[type=date].form-control::-webkit-calendar-picker-indicator {
    filter: invert(0.8);
}

[data-bs-theme=dark] :not(.navbar-filters) .select2-selection--multiple {
    background-color: var(--tblr-bg-surface) !important;
    border-color: var(--tblr-bg-surface) !important;
}

[data-bs-theme=dark] :not(.navbar-filters) .select2-container--open {
    box-shadow: none !important;
    border: none !important;
    background-color: transparent !important;
    border-color: var(--tblr-bg-surface) !important;
}

[data-bs-theme=dark] :not(.navbar-filters) .select2-results ul.select2-results__options li.select2-results__option.select2-results__option--highlighted {
    background-color: var(--tblr-primary);
}

[data-bs-theme=dark] :not(.navbar-filters) .select2-results ul.select2-results__options li.select2-results__option:not(.select2-results__option--highlighted),
[data-bs-theme=dark] .select2-container .select2-dropdown .select2-search {
    background-color: var(--tblr-dark) !important;
}

[data-bs-theme=dark] :not(.navbar-filters) .select2-results ul.select2-results__options li.select2-results__option:not(.select2-results__option--highlighted)[aria-selected=true] {
    color: var(--tblr-primary) !important;
}

[data-bs-theme=dark] select[multiple]:focus option:checked {
    background: var(--tblr-primary) linear-gradient(0deg, var(--tblr-primary) 0%, var(--tblr-primary) 100%);
}


[data-bs-theme=dark] .input-group button.popup_selector,
[data-bs-theme=dark] .input-group button.clear_elfinder_picker {
    background-color: var(--tblr-bg-surface) !important;
    border-color: var(--tblr-primary-border-subtle) !important;
    color: var(--tblr-dark-text);
}

[data-bs-theme=dark] .input-group button.popup_selector:hover,
[data-bs-theme=dark] .input-group button.clear_elfinder_picker:hover {
    background-color: var(--tblr-dark-rgb) !important;
    color: var(--tblr-light-text);
}

/*phone*/
[data-bs-theme=dark] .dial-code {
    color: red !important;
}

/*ckeditor*/
[data-bs-theme=dark] .cke_top, span.cke_button_label.cke_button__source_label {
    background-color: var(--tblr-bg-surface) !important;
}

[data-bs-theme=dark] span.cke_button_icon,
[data-bs-theme=dark] span.cke_combo_arrow {
    filter: invert(1) !important;
}

[data-bs-theme=dark] span.cke_button_label,
[data-bs-theme=dark] span.cke_combo_text {
    color: var(--tblr-light-text) !important;
}

[data-bs-theme=dark] .cke_chrome {
    border: none;
}

[data-bs-theme=dark] span.cke_combo_text {
    color: var(--tblr-light-text) !important;
}

/*filters*/
[data-bs-theme=dark] .navbar-filters ul.select2-selection__rendered {
    background-color: var(--tblr-gray) !important;
    box-shadow: 0 0 3px var(--tblr-primary) !important;
}

[data-bs-theme=dark] .navbar-filters .select2-selection--multiple {
    background-color: var(--tblr-gray-800) !important;
    color: var(--tblr-light) !important;
}

[data-bs-theme=dark] table.table.table-striped > tbody > tr:nth-of-type(even) > * {
    --tblr-table-accent-bg: var(--tblr-bg-surface);
}

#crudTable_wrapper .table-striped > tbody > tr:nth-of-type(odd) > * {
    box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.03);
}

[data-bs-theme=dark] header.top div.navbar-collapse div.navbar {
    background-color: var(--tblr-bg-surface);
}

[data-bs-theme=dark] .form-selectgroup-check {
    background-color: var(--tblr-bg-surface);
}

[data-bs-theme=dark] span.input-group-text {
    border-color: var(--tblr-primary-border-subtle);
}