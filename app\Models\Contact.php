<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Contact extends Model implements AuditableContract
{
    use HasFactory, CrudTrait, Auditable;

    protected $guarded = ['id'];
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
}
