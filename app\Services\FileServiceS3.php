<?php

namespace App\Services;


use App\Events\FTPBackupFileS3Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileServiceS3
{
    private static $instance = null;

    private function __construct() {}

    public static function getInstance(): ?FileServiceS3
    {
        if (self::$instance == null) {
            self::$instance = new FileServiceS3();
        }

        return self::$instance;
    }

    public function uploadLocalToS3($filename, $pathFolder = 'others'): ?string
    {
        try {
            if ($filename) {

                $file_content = Storage::disk('local')->get($filename);
                $ext = pathinfo($filename, PATHINFO_EXTENSION);
                $name = md5(time() . Str::slug($filename)) . '.' . $ext;

                $filePath = $pathFolder . '/' . $name;

                $flag = Storage::disk('s3')->put($filePath, $file_content);

                if ($flag) {
                    // event(new FTPBackupFileS3Event($filePath));
                    return $filePath;
                }

                return null;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function uploadToS3($file, $pathFolder = 'others', $auto_date_folder = true): ?string
    {
        try {
            if ($file) {
                $name = md5(time() . Str::slug($file->getClientOriginalName())) . "." . $file->getClientOriginalExtension();

                $filePath = $pathFolder;
                if ($auto_date_folder) {
                    $filePath .= "/" . date('Ym');
                }
                $filePath = $filePath . "/" . $name;

                $flag = Storage::disk('s3')->put($filePath, file_get_contents($file));
                if ($flag) {

                    // event(new FTPBackupFileS3Event($filePath));
                    return $filePath;
                }
                // dd(1);
                return null;
            }

            return null;
        } catch (\Exception $exception) {
            // dd($exception);
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function uploadToS3FromLink($link, $pathFolder = 'others', $options = []): ?string
    {
        try {
            if (!empty($link)) {
                $pathParts = pathinfo($link);

                $name = md5(time() . uniqid() . Str::slug($pathParts['filename'])) . '.' . $pathParts['extension'];

                $pathFolder .= "/" . date('Ym');
                $filePath = $pathFolder . '/' . $name;

                # file_get_contents with basic auth username and password

                // dd($options);
                if (isset($options['basic_auth']) && $options['basic_auth']) {
                    $file_content = file_get_contents($link, false, stream_context_create([
                        'http' => [
                            'method' => 'GET',
                            'header' => 'Authorization: Basic ' . base64_encode($options['basic_auth'])
                        ]
                    ]));
                } else {
                    $file_content = file_get_contents($link);
                }

                $flag = Storage::disk('s3')->put($filePath, $file_content);

                if ($flag) {
                    // event(new FTPBackupFileS3Event($filePath));
                    return $filePath;
                }

                return null;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function moveFileS3($pathFrom, $pathFolder): ?string
    {
        try {
            if (!empty($pathFrom) && Storage::disk('s3')->exists($pathFrom)) {
                $pathParts = pathinfo($pathFrom);

                $name = md5(time() . Str::slug($pathParts['filename'])) . '.' . $pathParts['extension'];

                $pathTo = $pathFolder . '/' . $name;

                if (Storage::disk('s3')->exists($pathTo)) {
                    return $pathTo;
                }

                Storage::disk('s3')->move($pathFrom, $pathTo);

                return $pathTo;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function uploadToS3AfterResize($file, $pathFolder, $width = 300, $height = null): ?string
    {
        try {
            $imageFile = Image::make($file); //->resize($width, $height)->stream();
            $w = $imageFile->width();
            $h = $imageFile->height();
            // $min = min([$w, $h]);
            $max = max([$w, $h]);
            if ($height) {
                $imageFile->resizeCanvas($max, $max, 'center', false, 'ffffff')->resize($width, $height)->stream();
            } else {
                $imageFile->resizeCanvas($max, $max, 'center', false, 'ffffff')->widen($width)->stream();
            }

            // $imageFile = Image::make($file)->resize($width, $height)->stream();
            $imageFile = $imageFile->__toString();

            $name = md5(time() . Str::slug($file->getClientOriginalName())) . '.' . $file->getClientOriginalExtension();

            $filePath = $pathFolder . '/' . $name;

            $flag = Storage::disk('s3')->put($filePath, $imageFile);
            if ($flag) {
                // event(new FTPBackupFileS3Event($filePath));
                return $filePath;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function deleteFileOnS3($paths)
    {
        if (is_array($paths)) {
            foreach ($paths as $path) {
                $this->deleteOnlyFile($path);
            }
        } else {
            $this->deleteOnlyFile($paths);
        }
    }

    private function deleteOnlyFile($path)
    {
        try {
            if (!empty($path) && Storage::disk('s3')->exists($path)) {
                Storage::disk('s3')->delete($path);
            }
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
        }
    }

    public function uploadToS3LiveWrite($fileContent, $name, $pathFolder = 'others'): ?string
    {
        try {
            if (!empty($fileContent)) {

                $pathFolder .= "/" . date('Ym');
                $filePath = $pathFolder . '/' . $name;

                $flag = Storage::disk('s3')->put($filePath, $fileContent);

                if ($flag) {
                    // event(new FTPBackupFileS3Event($filePath));
                    return $filePath;
                }

                return null;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }
}
