<?php

namespace App\Services;


use App\Models\Contact;
use Illuminate\Http\Request;

class ContactService
{

    public function getContact(Request $request)
    {
        $form = backpack_form_input();
        $term = $request->input('q');
       return Contact::query()->when(!empty($term), function ($q) use ($term) {
            $q->where('name', 'like', '%' . $term . '%');
        })->when(!empty($form['company']), function ($q) use ($form) {
            $q->where('company_id', $form['company']);
        })->select(['id', 'name'])->paginate(10);

    }


}
