<?php

namespace App\Console\Commands;

use App\Helpers\Utils;
use App\Models\Cv;
use App\Models\TempComment;
use App\Models\User;
use Illuminate\Console\Command;

class ReImportJiraComment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:re-import-jira-comment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re import jira comment from jira to database.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = User::pluck('id', 'email')->toArray();
        $user_ids = [];
        foreach ($users as $key => $value) {
            $key = Utils::getUsernameFromEmail($key);
            $user_ids[$key] = $value;
        }

        $not_exists_cv = [];
        TempComment::where('updated', 0)->chunk(100, function ($tempComments) use($user_ids, &$not_exists_cv) {
            foreach ($tempComments as $tempComment) {
                // dd($tempComment);
                $cv = Cv::where('source_id', $tempComment->cv_id)->first();
                if (!$cv) {
                    $not_exists_cv[] = $tempComment->cv_id;
                    continue;
                }
                $candidate = $cv->candidate;
                $comment = $candidate->comment()->create([
                    'content' => $tempComment->content,
                    'created_at' => $tempComment->created_at,
                    'created_by' => isset($user_ids[$tempComment->user]) ? $user_ids[$tempComment->user] : null,
                    'created_by_username' => $tempComment->user,
                ]);
                $tempComment->update(['updated' => 1]);
                $this->info($tempComment->content);
            }
        });
        $this->info('Done');
        dd($not_exists_cv);
    }
}
