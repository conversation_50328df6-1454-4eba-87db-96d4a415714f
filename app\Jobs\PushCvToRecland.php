<?php

namespace App\Jobs;

use App\Helpers\Utils;
use App\Models\Cv;
use App\Services\CvService;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PushCvToRecland // implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, SerializesModels; //Queueable

    protected $cv;
    public $response;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Cv $cv)
    {
        $this->cv = $cv;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $result = [
            'success' => false,
            'message' => '',
            'data' => [
                'id' => null,
            ]
        ];
        $client = new Client();
        $cv = $this->cv;

        $salary_expect = intval($cv->salary_expect);

        if ($cv->currency == 'USD') {
            $salary_expect = $salary_expect * 23000;
        }
        if ($salary_expect <= 100) {
            $salary_expect = $salary_expect * 1000000;
        }
        $currency = 'VND';
        if ($salary_expect >= 300 && $salary_expect <= 10000) {
            $currency = 'USD';
        }

        $url = 'https://recland.co/api/selling-cv/create';
        $cv->cv_public = strpos($cv->cv_public, 'https://') !== false ? $cv->cv_public : gen_url_file_s3($cv->cv_public, '', false);
        $cv_private = $cv->cv_private;

        if (!empty($cv_private)) {
            $cv_private = strpos($cv_private, 'https://') !== false ? $cv_private : gen_url_file_s3($cv_private, '', false);
        }

        $skills = $cv->skills()->pluck('name')->toArray();
        // dd($cv);
        $data = array(
            'private_cv_upload'          => '',
            'cv_public'                  => $cv->cv_public ?? '',
            'cv_private'                 => $cv_private ?? '',
            'id'                         => $cv->id,
            'candidate_name'             => $cv->name,
            'candidate_mobile'           => $cv->mobile,
            'candidate_email'            => Utils::isEmail($cv->email) ? $cv->email : $cv->candidate->email,
            'year_experience'            => $cv->yoe,
            'candidate_job_title'        => $cv->job_title,
            'rank'                       => '',
            'candidate_portfolio'        => '',
            'candidate_salary_expect'    => $salary_expect,
            'candidate_salary_expect_to' => $salary_expect,
            'candidate_currency'         => $currency,
            'career'                     => [30, 31],
            'assessment'                 => '',
            'candidate_location'         => explode(',', $cv->location),
            'skills'                     => $skills,
            'candidate_formwork'         => 1,
            'candidate_est_timetowork'   => 2,
            'selling_skill'              => '',
            'is_authority'               => 0,
            'type_of_sale'               => 'cv',
            'source'                     => 'crm.hri.com.vn',
            'created_at'                 => $cv->created_at->format('Y-m-d H:i:s'),
            'updated_at'                 => $cv->updated_at->format('Y-m-d H:i:s'),
        );
        // dd($data);

        try {
            $response = $client->post($url, [
                'json' => $data,
                'http_errors' => false
            ]);
            $response_body = $response->getBody()->getContents();
            // dd($response_body);
            $result = json_decode($response_body, true);

            if (!$result) {
                \Log::error("Không thể parse JSON response cho CV ID: " . $cv->id);
                $cv->update(['pushed_to_recland' => -1]);
                \Log::error($response_body);
                $result['message'] = 'Không thể parse JSON response';
                $result['success'] = false;
                echo 'ID: ' . $cv->id . " - Không thể parse JSON response\n";
                return $result;
            }

            $id = data_get($result, 'data.id');
            \Log::info($cv->id . ' ' . $cv->email . ' ' . ($id ? $id : 'Loi'));

            if ($id) {
                $cv->update(['pushed_to_recland' => true]);
                $result['success'] = true;
                $result['message'] = 'Đã gửi CV thành công';
                $result['data']['id'] = $id;
                echo 'ID: ' . $cv->id . " - Đã gửi CV thành công\n";
                return $result;
            }
        } catch (\Exception $e) {
            \Log::error("Lỗi khi gửi CV ID {$cv->id}: " . $e->getMessage());
            $result['message'] = 'Lỗi khi gửi CV';
            $result['success'] = false;
            echo 'ID: ' . $cv->id . " - Lỗi khi gửi CV\n";
            return $result;
        }
    }
    public function getResponse()
    {
        return 'fsfds';
    }
}
