<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('status', function (Blueprint $table) {
            $table->renameColumn('slug', 'group');
            $table->string('slug_value')->after('name');
            $table->tinyInteger('order')->nullable();
        });

        foreach (\App\Models\Status::all() as $status) {
            $status->slug_value = \Illuminate\Support\Str::slug($status->name);
            $status->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('status', function (Blueprint $table) {
            $table->dropColumn('slug_value');
            $table->dropColumn('order');
            $table->renameColumn('group', 'slug');
        });
    }

};
