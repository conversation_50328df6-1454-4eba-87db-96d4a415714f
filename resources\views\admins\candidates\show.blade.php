@extends(backpack_view('blank'))

@php
    $defaultBreadcrumbs = [
        trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
        $crud->entity_name_plural => url($crud->route),
        trans('backpack::crud.preview') => false,
    ];

    // if breadcrumbs aren't defined in the CrudController, use the default breadcrumbs
    $breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;
    $jira = $entry->getJiraInfo();
@endphp

@section('header')
    <div class="container-fluid d-flex justify-content-between my-3">
        <section class="header-operation animated fadeIn d-flex mb-2 align-items-baseline d-print-none"
            bp-section="page-header">
            <h1 class="text-capitalize mb-0" bp-section="page-heading">{!! $crud->getHeading() ?? $crud->entity_name_plural !!}</h1>
            <p class="ms-2 ml-2 mb-0" bp-section="page-subheading">{!! $crud->getSubheading() ?? mb_ucfirst(trans('backpack::crud.preview')) . ' ' . $crud->entity_name !!}</p>
            @if ($crud->hasAccess('list'))
                <p class="ms-2 ml-2 mb-0" bp-section="page-subheading-back-button">
                    <small><a href="{{ url($crud->route) }}" class="font-sm"><i class="la la-angle-double-left"></i>
                            {{ trans('backpack::crud.back_to_all') }}
                            <span>{{ $crud->entity_name_plural }}</span></a></small>
                </p>
            @endif
        </section>
        <a href="javascript: window.print();" class="btn float-end float-right"><i class="la la-print"></i></a>
    </div>
@endsection

@section('content')
    <div class="row" bp-section="crud-operation-show">
        <div class="{{ $crud->getShowContentClass() }}">
            <div class="row">
                <div class="col-12 pb-4">
                    <div class="card card-style1 border-0">
                        <div class="card-body ">
                            <div class="row align-items-center ">
                                <div class="col-lg-12 px-xl-10">
                                    <div class="bg-danger d-lg-inline-block py-1-9 px-1-9 px-sm-6 mb-1-9 rounded py-2"
                                        style="margin-bottom: 15px">
                                        <h3 class="h2 text-white mb-0">{{ $entry->name }} </h3>
                                    </div>
                                    <span class=" h2 badge bg-primary">#{{ $entry->id }}</span>
                                    <ul class="list-unstyled mb-1-9 row">
                                        <div class="col-md-4">
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Gender:</span>{{ optional($entry->statusGender)->name }}
                                            </li>
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Email:</span>
                                                {{ $entry->email }}</li>
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Day of
                                                    birth:</span> {{ $entry->dob }}</li>
                                            class="display-26 text-secondary me-2 font-weight-600">Created
                                            by:</span> {{ optional($entry->created_by_user)->email }}</li>
                                        </div>
                                        <div class="col-md-4">
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Mobile:</span>
                                                {{ $entry->mobile }}</li>
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">University:</span>{{ $entry->university }}
                                            </li>
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Address:</span>{{ $entry->address }}
                                            </li>
                                        </div>
                                        <div class="col-md-4">
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Old
                                                    company:</span>{{ $entry->old_company }}</li>
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Facebook:</span>{{ $entry->facebook }}
                                            </li>
                                            <li class="mb-2 mb-xl-3 display-28"><span
                                                    class="display-26 text-secondary me-2 font-weight-600">Linkedin:</span>{{ $entry->linkedin }}
                                            </li>
                                        </div>
                                        @if ($jira)
                                            <h3>Jira Data</h3>
                                            <div class="col-md-4">
                                                <div class="rows">
                                                    <div class="row">
                                                        <span class="col-6">Reporter:</span> <span
                                                            class="col-6">{{ $jira->reporter }}</span>
                                                    </div>
                                                    <div class="row">
                                                        <span class="col-6">created_at:</span> <span
                                                            class="col-6">{{ $jira->created_at }}</span>
                                                    </div>
                                                    <div class="row">
                                                        <span class="col-6">language:</span> <span
                                                            class="col-6">{{ $jira->language }}</span>
                                                    </div>
                                                    <div class="row">
                                                        <span class="col-6">experience:</span> <span
                                                            class="col-6">{{ $jira->experience }}</span>
                                                    </div>
                                                    <div class="row">
                                                        <span class="col-6">skill:</span> <span
                                                            class="col-6">{{ $jira->skill }}</span>
                                                    </div>
                                                    <div class="row">
                                                        <span class="col-6">source_type:</span> <span
                                                            class="col-6">{{ $jira->source_type }}</span>
                                                    </div>
                                                    <div class="row">
                                                        <span class="col-6">work_site:</span> <span
                                                            class="col-6">{{ $jira->work_site }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


            {{-- Default box --}}
            <div class="">
                @if ($crud->model->translationEnabled())
                    <div class="row">
                        <div class="col-md-12 mb-2">
                            {{-- Change translation button group --}}
                            <div class="btn-group float-right">
                                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown"
                                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    {{ trans('backpack::crud.language') }}
                                    :
                                    {{ $crud->model->getAvailableLocales()[request()->input('_locale') ? request()->input('_locale') : App::getLocale()] }}
                                    &nbsp; <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    @foreach ($crud->model->getAvailableLocales() as $key => $locale)
                                        <a class="dropdown-item"
                                            href="{{ url($crud->route . '/' . $entry->getKey() . '/show') }}?_locale={{ $key }}">{{ $locale }}</a>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif
                <div>
                    @livewire('admin.candidates.detail-view', ['candidate_id' => $entry->id])
                </div>
            </div>
        </div>
    </div>
@endsection
@section('before_styles')
    <link href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/fixedheader/3.3.1/css/fixedHeader.dataTables.min.css" rel="stylesheet"
        type="text/css" />
    <link href="https://cdn.datatables.net/responsive/2.4.0/css/responsive.dataTables.min.css" rel="stylesheet"
        type="text/css" />
@endsection
@section('after_scripts')
    <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/fixedheader/3.3.1/js/dataTables.fixedHeader.min.js"></script>
@endsection
@push('after_scripts')
    <script>
        $(document).ready(function() {
            $('#table-cvs').dataTable();
        });
    </script>
@endpush
