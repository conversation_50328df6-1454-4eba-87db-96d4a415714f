<?php

namespace App\Jobs;

use App\Services\CvService;
use App\Services\FileServiceS3;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;


class UploadHideCv implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userCv;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userCv)
    {
        $this->userCv = $userCv;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!empty($this->userCv->cv_private)) {
            return;
        }
        $cv_private = CvService::getInstance()->hideCv($this->userCv->cv_public);
        if ($cv_private) {
            $this->userCv->update(['cv_private' => $cv_private]);
        }

        // $response = Http::post(env('API_AI_HIDE_CV'), [
        //     'file' => gen_url_file_s3($this->userCv->cv_public),
        // ])->json();

        // if (isset($response['data']['file_private'])) {
        //     $this->userCv->update(
        //         ['cv_private' => FileServiceS3::getInstance()->uploadToS3FromLink($response['data']['file_private'], PATH_FOLDER_SAVE_CV)]
        //     );
        // }
    }

}
