<?php

namespace App\Listeners;

use App\Events\LogTaskCreatedEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogTaskCreatedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LogTaskCreatedEvent $event): void
    {
        $log_task = $event->logTask;
        if ($log_task->content == 'Task created') {
            return ;
        }
        $log_task->notify(new \App\Notifications\LogTaskCreated());
    }
}
