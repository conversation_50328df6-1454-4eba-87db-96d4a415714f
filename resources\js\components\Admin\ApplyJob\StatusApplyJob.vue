<template>
    <loading v-model:active="isLoading" :is-full-page="fullPage"/>
    <v-stepper alt-labels>
        <v-stepper-header>
            <template v-for="(status, index) in listStatusApply">
                <v-stepper-item :color="updateStyle(status.id, index) ? 'info' : ''"
                                :complete="updateStyle(status.id, index)" :title="status.name" :value="index + 1"
                                @click="getStatusChild(index,status.id)"
                                @click.stop="showModalChangeStatus = !showModalChangeStatus" editable></v-stepper-item>
                <v-divider v-if="index < listStatusApply.length - 1"></v-divider>
            </template>
        </v-stepper-header>
    </v-stepper>
    <div class="row">


        <!--        <div class="col-12" v-if="listStatusApply.length > 0 ">-->
        <!--            <ul class="progressbar">-->
        <!--                <li :style="{ width: `calc(100% / ${this.listStatusApply.length})`}"-->
        <!--                    title="Click để thay đổi trạng thái" @click="getStatusChild(index,status.id)"-->
        <!--                    @click.stop="showModal = !showModal" :class="updateStyle(status.id,index)"-->
        <!--                    v-for="(status,index) in listStatusApply" :key="index"> {{ status.name }}-->
        <!--                </li>-->
        <!--            </ul>-->
        <!--        </div>-->
        <v-card class="mt-3">
            <v-card-text>
                <div >
                    <h3 class="pt-5"> Lịch sử thay đổi trạng thái</h3>

                    <table class="table">
                        <thead>
                        <tr>
                            <td>#</td>
                            <td>Ngươi thực hiện</td>
                            <td>Ngày báo kết quả</td>
                            <td>Trạng thái</td>
                            <td>Ghi chú</td>
                            <td>Thời gian thực hiện</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(log,index) in listLogChangeStatusApply" :key="index">
                            <td>{{ index + 1 }}</td>
                            <td>{{ log.user_name }}</td>
                            <td>{{ log.date_status }}</td>
                            <td><strong>{{ log.parent_status_name }}</strong><br>{{ log.status_name }}</td>
                            <td>{{ log.note }}</td>
                            <td>{{ log.created_at }}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </v-card-text>
        </v-card>
        <v-dialog max-width="700" v-model="showModalChangeStatus" title="Update status apply job">
            <v-form @reset="resetForm()" @submit="onSubmit">
                <v-card class="form-group" label="Trạng thái" label-for="status">
                    <v-card-text>
                        <v-row dense>
                            <v-col cols="12" sm="12">
                                <v-select v-model="dataApply.status" :items="listStatusChild" item-title="label"
                                          item-value="code" label="Trạng thái"
                                          :rules="[v => !!v || 'The status field is required.']"
                                          required></v-select>
                            </v-col>
                        </v-row>
                        <v-row dense>
                            <v-col cols="12" sm="12">
                                <div class="form-group" label-for="note">
                                    <v-textarea id="note" v-model="dataApply.note" label="Nhập nội dung ghi chú"
                                                rows="5"/>
                                </div>
                            </v-col>
                        </v-row>
                        <v-row dense>
                            <v-col cols="12" sm="12">
                                <div class="form-group" label="Ngày báo kết quả" label-for="date">
                                    <v-date-input label="Date" variant="underlined" v-model="dataApply.date"
                                                  :config="config"
                                                  class="form-control col-12" placeholder="Select date"
                                                  name="date"></v-date-input>
                                </div>
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <div class="d-flex justify-end pa-2 ga-2">
                        <v-btn type="submit" variant="tonal" class="btn btn-success">Submit</v-btn>
                        <v-btn type="reset" variant="tonal" class="btn btn-danger"
                               @click="showModalChangeStatus = false">Cancel
                        </v-btn>
                    </div>
                </v-card>
            </v-form>
        </v-dialog>
    </div>

</template>

<script scoped>

import {VDateInput} from 'vuetify/labs/VDateInput'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';
// import vSelect from "vue-select";
// import 'vue-select/dist/vue-select.css';
// import FlatPicker from 'vue-flatpickr-component';
// import 'flatpickr/dist/flatpickr.css';

export default {
    components: {Loading, VDateInput,},
    data() {
        return {
            listLogChangeStatusApply: [],
            showModalChangeStatus: false,
            showModal: false,
            listStatusApply: [],
            listStatusChild: [],
            statusParentSelect: null,
            isLoading: true,
            fullPage: true,
            dataApply: {
                status: null,
                date: null,
                note: null,
            },
            error: {
                status: null,
                date: null,
                note: null,
            },
            statusParentData: this.statusParent,
            config: {
                className: 'col-12',
                enableTime: true,
                static: true,
                allowInput: true,
                disableMobile: true,
                wrap: true,
                time_24hr: true,
                altFormat: 'd/m/Y H:i',
                altInput: true,
                dateFormat: 'Y-m-d H:i',
            },
        }
    },
    props: {
        statusApply: {
            required: true,
        },
        statusParent: {
            required: true,
        },
        applyJobId: {
            required: true,
        }

    },
    created() {
        this.getLogApplyJob();
        this.getListStatusApply();

    },
    methods: {
        getStatusChild(index, status) {
            this.resetForm();
            this.statusParentSelect = status;
            this.listStatusApply[index].children.forEach(element => {
                this.listStatusChild.push({code: element.id, label: element.name});
            });

        },
        resetForm() {
            this.showModalChangeStatus = false;
            this.listStatusChild = [];
            this.dataApply = {status: null, date: null, note: null,};
            this.error = {status: null, date: null, note: null,};
        },
        async onSubmit(e) {
            e.preventDefault();
            this.isLoading = true;
            console.log(this.dataApply);
            await axios.post('/api/change-status-apply-job', {
                status: this.dataApply.status != null ? this.dataApply.status : null,
                note: this.dataApply.note,
                status_date: this.dataApply.date,
                apply_job_id: this.applyJobId,
            }).then(res => {
                this.isLoading = false;
                this.statusParentData = this.statusParentSelect;
                new Noty({type: "success", text: res.data.success,}).show();
                this.getLogApplyJob();
                this.resetForm();
            }).catch(err => {

                this.isLoading = false;

                if (err.response.data.errors.status) {
                    this.error.status = err.response.data.errors.status[0];
                }
                if (err.response.data.errors.date) {
                    this.error.status = err.response.data.errors.date[0];
                }
                if (err.response.data.errors.note) {
                    this.error.status = err.response.data.errors.note[0];
                }
            });

        },
        async getLogApplyJob() {
            this.isLoading = true;
            await axios.get('/api/get-log-apply-job/' + this.applyJobId).then(res => {
                this.listLogChangeStatusApply = res.data.data;
                this.isLoading = false;
            }).catch(err => {
                // this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        async getListStatusApply() {
            this.isLoading = true;
            axios.post('/api/status-apply-job').then(res => {
                if (res && res.data && res.data.data && res.data.data.length > 0) {
                    this.listStatusApply = res.data.data;
                    this.isLoading = false;
                }
            }).catch(err => {
                this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        updateStyle(status, key) {
            let keyParent = null;
            if (status == this.statusParentData) {
                return 'active';
            }
            this.listStatusApply.forEach((value, index) => {
                if (value.id == this.statusParentData) {
                    keyParent = index;
                }
            });
            if (keyParent > key) {
                return 'complete';
            }

        }
    }
}
</script>
<style scoped>
.flatpickr-wrapper {
    width: 100% !important;
}
</style>
