<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\CompanyRequest;
use App\Models\Company;
use App\Models\Job;
use App\Models\Lead;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;

/**
 * Class CompanyCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class CompanyCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;


    protected $statusService;

    public function __construct(StatusService $statusService)
    {
        $this->statusService = $statusService;
        parent::__construct();
    }


    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Company::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/company');
        CRUD::setEntityNameStrings('Khách hàng', 'Khách hàng');
        CRUD::setEditView('admins.company.edit');
        CRUD::denyAccess('delete');
        if (!backpack_user()->can('company.index')) {
            CRUD::denyAccess('list');
        }
        if (!backpack_user()->can('company.edit')) {
            CRUD::denyAccess('update');
        }
        if (!backpack_user()->can('company.create')) {
            CRUD::denyAccess('create');
        }
        if (!backpack_user()->can('company.show')) {
            CRUD::denyAccess('show');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    //    protected function setupListOperation()
    //    {
    //        CRUD::setFromDb(); // set columns from db columns.
    //
    //        /**
    //         * Columns can be defined using the fluent syntax:
    //         * - CRUD::column('price')->type('number');
    //         */
    //    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(CompanyRequest::class);
        CRUD::addFields($this->fieldData());

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function fieldData()
    {
        $validateIgnoreMst =  !empty(request('id')) ? ',mst,' . request('id') : '';
        $validateIgnoreEmail =  !empty(request('id')) ? ',email,' . request('id') : '';
        $validateIgnoreMobile =  !empty(request('id')) ? ',mobile,' . request('id') : '';
        return [
            fieldColumnData('name', 'Tên công ty', 'text', 'form-group col-md-3', 'required'),
            fieldColumnData('company_abbreviation', 'Tên viết tắt', 'text', 'form-group col-md-3', 'required'),
            selectFormArrayData('service_id', 'Dịch vụ/sản phẩm', 'form-group col-md-3', $this->statusService->getStatus('job-type', true), null),
            fieldColumnData('email', 'Email', 'text', 'form-group col-md-3', 'required|unique:companies' . $validateIgnoreEmail),
            fieldColumnData(
                'mobile',
                'Điện thoại',
                'text',
                'form-group col-md-3',
                ['required', 'regex:/^(\+?84|0)(\d{9,10})$/', 'unique:companies' . $validateIgnoreMobile]
            ),
            fieldColumnData('mst', 'Mã số thuế', 'text', 'form-group col-md-3', 'required|unique:companies' . $validateIgnoreMst),
            fieldColumnData('size', 'Quy mô', 'text', 'form-group col-md-3', 'required'),
            fieldColumnData('website', 'Website', 'text', 'form-group col-md-3'),
            fieldColumnData('number_working_year', 'Số năm hoạt động', 'number', 'form-group col-md-3'),
            fieldColumnData('address', 'Địa chỉ', 'text', 'form-group col-md-3', 'required'),
            [
                'name' => 'contact',
                'label' => 'Liên hệ',
                'type' => "relationship",
                'subfields' => [
                    [
                        'name' => 'name',
                        'label' => 'Họ tên',
                        'type' => 'text',
                        'wrapper' => [
                            'class' => 'form-group col-md-4',
                        ],
                    ],
                    [
                        'name' => 'position',
                        'label' => 'Vị trí',
                        'type' => 'text',
                        'wrapper' => [
                            'class' => 'form-group col-md-3',
                        ],
                    ],
                    [
                        'name' => 'email',
                        'label' => 'email',
                        'type' => 'text',
                        'wrapper' => [
                            'class' => 'form-group col-md-5',
                        ],
                    ],
                    [
                        'name' => 'phone',
                        'label' => 'Số điện thoại',
                        'type' => 'text',
                        'wrapper' => [
                            'class' => 'form-group col-md-5',
                        ],
                    ],
                    [
                        'name' => 'address',
                        'label' => 'Địa chỉ',
                        'type' => 'text',
                        'wrapper' => [
                            'class' => 'form-group col-md-7',
                        ],
                    ],
                ],
            ]

        ];
    }

    protected function setupListOperation()
    {
        $this->crud->addClause('roleData');
        $this->crud->column([
            'label' => 'Tên công ty ',
            'name' => 'name'
        ]);
        $this->crud->column([
            'label' => 'Tên viết tắt ',
            'name' => 'company_abbreviation'
        ]);
        $this->crud->column([
            'label' => 'Số năm hoạt động ',
            'name' => 'number_working_year'
        ]);
        $this->crud->column([
            'label' => 'Email ',
            'name' => 'email'
        ]);
        $this->crud->column([
            'label' => 'Mã số thuế',
            'name' => 'mst'
        ]);
        $this->crud->column([
            'label' => 'Địa chỉ ',
            'name' => 'address'
        ]);
        $this->crud->column([
            'label' => 'Ngày tạo',
            'name' => 'created_at'
        ]);
    }
    protected function setupInlineCreateOperation()
    {

        $this->setupCreateOperation();
    }

    public function createLeadFromCompany($id)
    {
        $company = $this->crud->getCurrentEntry($id);
        $lead = Lead::create([
            'company_id' => $company->id,
            'user_id' => backpack_auth()->user()->id,
        ]);
        return redirect(route('lead.show', $lead->id));
    }

    public function setupShowOperation()
    {
        //        $job = Job::with('company')->get();
        //        $entry = $this->crud->getCurrentEntry();
        //        $job = $entry->job;

        $this->crud->setShowView('admins.company.custom-show');
    }
}
