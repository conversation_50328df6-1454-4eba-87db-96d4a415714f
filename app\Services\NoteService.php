<?php

namespace App\Services;


use App\Models\Note;
use App\Models\User;
use Illuminate\Http\Request;

class NoteService
{

    public function queryList(array $filters = [])
    {
        return Note::query()
            ->when(!empty($filters['keyword']), function ($query) use ($filters) {
                $query->orWhere('content', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['candidate_id']), function ($query) use ($filters) {
                $query->where('candidate_id', $filters['candidate_id']);
            })
            ->when(!empty($filters['user_id']), function ($query) use ($filters) {
                $query->where('user_id', $filters['user_id']);
            })
            ->latest('id')->get();
    }

    public function create(array $data)
    {
        return Note::create($data);
    }

}
