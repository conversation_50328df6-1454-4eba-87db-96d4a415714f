<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Attachment extends Model
{
    use CrudTrait;
    use HasFactory;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'attachments';

    protected $guarded = ['id', 'created_at', 'updated_at'];


    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    # beforeSave, check if columnt "path" has extension is pdf (path is url), xlsx, doc, docx then set column "type" is "file", else set column "type" is "image"

    protected static function boot()
    {
        parent::boot();
        static::saving(function ($model) {
            if (empty($model->type)) {
                $path = $model->path;
                $extension = pathinfo($path, PATHINFO_EXTENSION);
                if (in_array($extension, ['pdf', 'xlsx', 'doc', 'docx'])) {
                    $model->type = 'file';
                } elseif (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                    $model->type = 'image';
                }
            }
            # get file name from url (column "path") then set to column "name"
            if (empty($model->name)) {
                $model->name = pathinfo($path, PATHINFO_FILENAME);
            }
        });
    }



    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function object(): MorphTo
    {
        return $this->morphTo();
    }
    public function raw_data(): MorphOne
    {
        return $this->morphOne(RawData::class, 'object');
    }

    # belongsTo CV


    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
