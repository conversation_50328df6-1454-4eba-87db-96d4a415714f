<?php

namespace App\Notifications;

use App\Channels\RingChannel;
use App\Models\Department;
use App\Models\Task;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\TaskService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use League\HTMLToMarkdown\HtmlConverter;

class JobCreatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', RingChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $job_listener = clone $notifiable;
        $converter = new HtmlConverter();

        if ($job_listener->recerJob) {
            $recerName =  User::whereIn('id', $job_listener->recerJob->pluck('recer_id')->toArray())
                ->pluck('name', 'email')->toArray();
        }

        if ($job_listener->recTeam) {
            $managerId = Department::whereIn('id', $job_listener->recTeam->pluck('department_id')->toArray())
                ->pluck('manager')->toArray();

            $managers = User::whereIn('id', $managerId)->pluck('name')->toArray();
        }

        $recerString = ($recerName && count($recerName)) ?  implode(', ', $recerName) : '';
        $managerString = ($managers && count($managers)) ?  implode(', ', $managers) : '';

        $description = $job_listener->description ? $converter->convert($job_listener->description) : '';
        $mailMessage = (new MailMessage)
            ->subject('Job: #' . $job_listener->id . ' - ' . $job_listener->title . '" mới được tạo')
            ->line('Job mới có tên '  . $job_listener->title . '** mới được tạo bởi **' . trim(optional($job_listener->user)->name) . '**')
            ->line($description)
            ->line('Thuộc recer: ' . $recerString . '**')
            ->line('Thuộc team **' . $managerString . '**');
        if ($recerString) {
            $mailMessage->line('Thuộc recer: **' . $recerString . '**');
        }
        if ($managerString) {
            $mailMessage->line('Thuộc team: **' . $managerString . '**');
        }
        $mailMessage->action('Xem thông tin job', backpack_url('job/' . $job_listener->id . '/show'));
        return $mailMessage;
    }


    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function toRing($notifiable) {}
}
