<template>
  <div>
    <!-- <div class="pb-4">
            <label for="">From : </label><input id="startDate" type="date" v-model="startDate" />
            <label for="">To : </label><input id="endDate" type="date" v-model="endDate" />
            <v-btn>Lọc</v-btn>
        </div> -->
    <v-row>
      <v-col cols="6">
        <label for="lastName">Từ ngày</label>
        <VueDatePicker class="date-picker" v-model="startDate" :enable-time-picker="false" model-type="yyyy-MM-dd" :format="formatVnDate"> </VueDatePicker>
      </v-col>
      <v-col cols="6">
        <label for="lastName">Đ<PERSON>n ngày</label>
        <VueDatePicker class="date-picker" v-model="endDate" :enable-time-picker="false" model-type="yyyy-MM-dd" :format="formatVnDate"> </VueDatePicker>
      </v-col>
    </v-row>
    <div v-if="dataLoaded || (startDate === '' && endDate === '')">
      <div class="mt-5">
        <h2 class="text-center">Bảng thống kê theo Recer</h2>
        <div>
          <v-select label="Lọc theo team" v-model="team_id" :items="recTeams" item-title="name" item-value="id" variant="solo" clearable></v-select>
        </div>
        <b-card>
          <div class="2">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Rec</th>
                  <th>CV mới</th>
                  <template v-if="statistics_teams_recer.data">
                    <th v-for="(status, status_id) in statistics_teams.data.status" :key="status_id">
                      {{ status }}
                    </th>
                  </template>
                </tr>
              </thead>
              <tbody>
                <template v-if="statistics_teams_recer.data">
                  <tr v-for="(userId, index) in Object.keys(statistics_teams_recer.data.users)" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ statistics_teams_recer.data.users[userId] }}</td>
                    <td>{{ statistics_teams_recer.data.total_cv[userId] || "-" }}</td>
                    <template v-for="(status, status_id) in statistics_teams.data.status" :key="status_id">
                      <td>
                        <span v-if="statistics_teams_recer.data.total[userId] && statistics_teams_recer.data.total[userId][status_id]">
                          <a style="color: #333" :href="`/admin/apply-job?log_status=${status_id}&user_id=${userId}&startDate=${startDate}&endDate=${endDate}`" target="_blank"> {{ statistics_teams_recer.data.total[userId][status_id] }} </a>
                        </span>
                        <span v-else>-</span>
                      </td>
                    </template>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </b-card>
      </div>

      <div class="mt-4">
        <h4>Bảng thống kê theo team</h4>
        <b-card>
          <div class="2">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Tháng</th>
                  <th>Tiêu chí</th>
                  <template v-if="statistics_teams.data">
                    <th v-for="(nameMonth, user_id) in statistics_teams.data.users" :key="user_id">
                      {{ statistics_teams.data.users[user_id] }}
                    </th>
                  </template>
                  <th>Tổng số</th>
                </tr>
              </thead>
              <tbody>
                <template v-if="statistics_teams.data && statistics_teams.data.month">
                  <template v-for="(monthData, month_id) in statistics_teams.data.month" :key="month_id" :set="(status_first = true)">
                    <tr v-for="(status, status_id) in statistics_teams.data.status" :key="status_id">
                      <td style="border-right: 1px solid #ddd" v-if="status_first == status_id" :rowspan="status_count">{{ month_id }}</td>
                      <td>{{ status }}</td>
                      <template v-for="(userData, userKey) in statistics_teams.data.users" :key="userKey">
                        <td v-if="status_id in statistics_teams.data.month[month_id] && userKey in statistics_teams.data.month[month_id][status_id]">{{ statistics_teams.data.month[month_id][status_id][userKey] > 0 ? statistics_teams.data.month[month_id][status_id][userKey] : '<sub>0</sub>' }}</td>
                        <td v-else><sub>-</sub></td>
                      </template>
                      <td v-if="status_id in statistics_teams.data.month[month_id] && 'total' in statistics_teams.data.month[month_id][status_id]">
                        <strong> {{ statistics_teams.data.month[month_id][status_id]["total"] }}</strong>
                      </td>
                      <td v-else>-</td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </b-card>
      </div>

      <div class="mt-4">
        <h4>Bảng thống kê trạng thái tuyển dụng theo tháng</h4>
        <b-card>
          <div class="2">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Tháng</th>
                  <th>Job được tạo</th>
                  <template v-if="statistics_teams.data">
                    <th v-for="(status, status_id) in statistics_teams.data.status" :key="status_id">
                      {{ status }}
                    </th>
                  </template>
                </tr>
              </thead>
              <tbody>
                <template v-if="statistics_teams.data">
                  <tr v-for="(monthData, monthId, index) in statistics_teams.data.month" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ monthId }}</td>
                    <td>{{ statistics_teams.data.jobs_created[monthId] }}</td>
                    <template v-if="statistics_teams.data.month[monthId]">
                      <td v-for="(status, status_id) in statistics_teams.data.status" :key="status_id">
                        <span>{{ monthData[status_id]?.total || "-" }}</span>
                      </td>
                    </template>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </b-card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page {
  background-color: white;
}

sub {
  color: #888;
}

::-webkit-inner-spin-button {
  display: none;
}

::-webkit-calendar-picker-indicator {
  background-color: white;
}

input[type="date"] {
  font-size: 25px;
}

::-webkit-datetime-edit-text {
  color: #555555;
}

::-webkit-datetime-edit-month-field {
  color: #555555;
}

::-webkit-datetime-edit-day-field {
  color: #555555;
}

::-webkit-datetime-edit-year-field {
  color: #555555;
}

::-webkit-calendar-picker-indicator {
  background-image: url(http://icons.iconarchive.com/icons/dakirby309/simply-styled/256/Calendar-icon.png);
  background-position: center;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  color: rgba(204, 204, 204, 0);
}
</style>

<script>
import { VDateInput } from "vuetify/labs/VDateInput";
// import DatePicker from '@/elements/DatePicker.vue'
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import moment from "moment";
import axios from "axios";

export default {
  components: {
    // DatePicker: DatePicker,
    VueDatePicker: VueDatePicker,
  },
  data() {
    return {
      load: true,
      status_first: 0,
      status_count: 0,
      statistics_teams: {},
      statistics_teams_recer: {},
      // jobs_created: {},
      dataLoaded: false,
      startDate: "",
      endDate: "",
      team_id: "",
      recTeams: [],
    };
  },
  computed: {
    // "shim" for v-date-picker
    // startDate: {
    //     get() {
    //         return this.startDate ? this.startDate.toISOString() : new Date();
    //     },
    //     set(val) {
    //         this.startDate = new Date(val)
    //     }
    // }
  },
  methods: {
    formatVnDate(date) {
      const day = date.getDate();
      const month = date.getMonth() + 1;
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    },
    loadMasterData() {
      this.load = true;
      axios
        .get("/api/master-data", {
          params: {
            key: "recTeams",
          },
        })
        .then((response) => {
          this.recTeams = response.data.data.recTeams;
          this.load = false;
        })
        .catch((err) => {
          console.log(err);
          this.load = false;
        });
    },
    async fetchData() {
      try {
        const urlParams = new URLSearchParams({
          team_id: this.team_id,
          start_date: this.startDate,
          end_date: this.endDate,
        });

        // const response = await fetch(`/api/report/total-apply-status-by-month?${urlParams}`);

        const responseRecer = await axios
          .get(`/api/report/total-recer-apply-status-by-month?${urlParams}`)
          .then((response) => {
            this.statistics_teams_recer = response.data;
          })
          .catch((err) => {
            console.log(err);
          });

        const response = await axios
          .get(`/api/report/total-apply-status-by-month?${urlParams}`)
          .then((response) => {
            this.statistics_teams = response.data;
          })
          .catch((err) => {
            console.log(err);
          });
        if (this.statistics_teams.data) {
          this.status_count = Object.keys(this.statistics_teams.data.status).length;
          if (this.status_count > 0) {
            var status_ids = Object.keys(this.statistics_teams.data.status);
            this.status_first = status_ids[0];
          }
        }
        this.dataLoaded = true;
      } catch (error) {
        console.error(error);
      }
    },
  },
  watch: {
    team_id: "fetchData",
    startDate: "fetchData",
    endDate: "fetchData",
  },
  async mounted() {
    this.loaded = true;
    this.loadMasterData();
    // get token from local storage
    var token = localStorage.getItem("access_token");
    // // if not token, return
    if (!token) {
      return;
    }
    // // set header
    axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    await this.fetchData();
  },
};
</script>
