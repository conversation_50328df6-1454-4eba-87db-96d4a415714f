<?php

namespace App\Providers;

use App\ViewComposers\ClientViewComposer;
use Illuminate\Support\Facades;
use Illuminate\Support\ServiceProvider;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // ...
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Using class based composers...
        Facades\View::composer('backpack.theme-tabler::inc.menu_notification_dropdown', ClientViewComposer::class);

    }
}
