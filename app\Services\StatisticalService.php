<?php

namespace App\Services;

use App\Helpers\Utils;
use App\Models\ApplyJob;
use App\Models\Contact;
use App\Models\LogLead;
use App\Models\Status;
use App\Models\User;
use App\Repositories\LeadRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StatisticalService
{

    public function __construct()
    {
    }

    public function changeStatusLead($start_date, $end_date)
    {
        // Lấy danh sách các trạng thái unique
        $statuses = LogLead::select('status')
            ->whereBetween('created_at', [$start_date, $end_date])
            ->distinct()
            ->pluck('status')
            ->toArray();

        // Query thống kê
        $statistics = DB::table('log_leads as ll')
            ->join('users as u', 'u.id', '=', 'll.user_id')
            ->whereBetween('ll.created_at', [$start_date, $end_date])
            ->select([
                'u.id as user_id',
                'u.name as user_name',
            ])
            ->groupBy('u.id', 'u.name');

        // Thêm các cột status động
        foreach ($statuses as $status) {
            $statistics->addSelect(DB::raw("SUM(CASE WHEN ll.status = $status THEN 1 ELSE 0 END) as status_$status"));
        }

        // Thêm cột tổng
        $statistics->addSelect(DB::raw('COUNT(*) as total'));

        $result = $statistics->get();

        return [
            'statuses' => $statuses,
            'statistics' => $result
        ];
    }

}
