<template>
    <div class="row">
        <loading v-model:active="isLoading" :is-full-page="fullPage" />
        <div class="col-12" v-if="listStatusApply.length > 0">
            <ul class="progressbar">
                <li :style="{ width: `calc(100% / ${this.listStatusApply.length})` }"
                    title="Click để thay đổi trạng thái" @click="getStatusChild(index, status.id)"
                    @click.stop="showModal = !showModal" :class="updateStyle(status.id, index)"
                    v-for="(status, index) in listStatusApply" :key="index"> {{ status.name }}
                </li>

            </ul>
        </div>
        <div class="container pt-5">
            <h5 class="pt-5"> L<PERSON>ch sử thay đổi trạng thái</h5>

            <table class="table">
                <thead>
                    <tr>
                        <td>#</td>
                        <td><PERSON><PERSON><PERSON><PERSON> thực hiện</td>
                        <td><PERSON><PERSON><PERSON> b<PERSON><PERSON> kế<PERSON> qu<PERSON></td>
                        <td>Tr<PERSON><PERSON> thái</td>
                        <td><PERSON><PERSON> chú</td>
                        <td>Thời gian thực hiện</td>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(log, index) in listLogChangeStatusApply" :key="index">
                        <td>{{ index + 1 }}</td>
                        <td>{{ log.user_name }}</td>
                        <td>{{ log.date_status }}</td>
                        <td><strong>{{ log.parent_status_name }}</strong><br>{{ log.status_name }}</td>
                        <td>{{ log.note }}</td>
                        <td>{{ log.created_at }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <q-dialog v-model="showModal">
            <q-card style="width: 700px; max-width: 80vw;">
                <q-form @reset="resetForm()" @submit="onSubmit">
                    <q-card-section>
                        <div class="text-h6">Cập nhật trạng thái ứng tuyển</div>
                    </q-card-section>

                    <q-separator />

                    <q-card-section>
                        <div class="form-group" label="Trạng thái" label-for="status">
                            <!-- <v-select id="statusApply" v-model="dataApply.status" :options="listStatusChild"
                            label="Trạng thái"></v-select> -->
                            <q-select v-model="dataApply.status" :options="listStatusChild" label="Trạng thái" />
                            <span class="text-danger"> {{ error.status }}</span>
                        </div>

                        <q-input v-model="dataApply.note" filled autogrow label="Nhập ghi chú..." />
                    </q-card-section>

                    <q-card-section>
                        <q-input filled v-model="dataApply.date" mask="date" :rules="['date']">
                            <template v-slot:append>
                                <q-icon name="event" class="cursor-pointer">
                                    <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                                        <q-date v-model="dataApply.date">
                                            <div class="row items-center justify-end">
                                                <q-btn v-close-popup label="Close" color="primary" flat />
                                            </div>
                                        </q-date>
                                    </q-popup-proxy>
                                </q-icon>
                            </template>
                        </q-input>
                        <span class="text-danger"> {{ error.date }}</span>
                    </q-card-section>

                    <q-separator />

                    <q-card-actions align="right">
                        <!-- <q-btn flat label="Decline" color="primary" v-close-popup /> -->
                        <!-- <q-btn flat label="Accept" color="primary" v-close-popup /> -->
                        <q-btn flat type="reset" color="secondary" class="float-right m-1" v-close-popup>Cancel</q-btn>
                        <q-btn type="submit" color="primary" class="float-right m-1">Submit</q-btn>
                    </q-card-actions>
                </q-form>
            </q-card>
        </q-dialog>
        <q-dialog v-model="showModalsd" v-bind:hide-footer="true" title="Update status apply job">
            <q-card>
                <q-card-section>
                    <q-form @reset="resetForm()" @submit="onSubmit">
                        <div class="form-group" label="Trạng thái" label-for="status">
                            <v-select id="statusApply" v-model="dataApply.status" :options="listStatusChild"></v-select>
                            <span class="text-danger"> {{ error.status }}</span>
                        </div>

                        <div class="form-group" label="Ghi chú" label-for="note">
                            <q-input id="note" v-model="dataApply.note" placeholder="Nhập ghi chú..." rows="5"
                                type="textarea" />
                            <span class="text-danger"> {{ error.note }}</span>
                        </div>
                        <div class="q-pa-md">
                            <q-input filled v-model="date" mask="date" :rules="['date']">
                                <template v-slot:append>
                                    <q-icon name="event" class="cursor-pointer">
                                        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                                            <q-date v-model="date">
                                                <div class="row items-center justify-end">
                                                    <q-btn v-close-popup label="Close" color="primary" flat />
                                                </div>
                                            </q-date>
                                        </q-popup-proxy>
                                    </q-icon>
                                </template>
                            </q-input>
                        </div>
                        <div class="form-group">
                            <q-btn type="submit" variant="primary" class="float-right m-1">Submit</q-btn>
                            <q-btn type="reset" variant="danger" class="float-right m-1">Cancel</q-btn>
                        </div>
                    </q-form>
                </q-card-section>
            </q-card>
        </q-dialog>
    </div>

</template>

<script scoped>


import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';
import vSelect from "vue-select";
import 'vue-select/dist/vue-select.css';
import FlatPicker from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';

export default {
    components: { vSelect, Loading, FlatPicker },
    data() {
        return {
            listLogChangeStatusApply: [],
            showModal: false,
            listStatusApply: [],
            listStatusChild: [],
            statusParentSelect: null,
            isLoading: true,
            fullPage: true,
            dataApply: {
                status: null,
                date: null,
                note: null,
            },
            error: {
                status: null,
                date: null,
                note: null,
            },
            statusParentData: this.statusParent,
            config: {
                className: 'col-12',
                enableTime: true,
                static: true,
                allowInput: true,
                disableMobile: true,
                wrap: true,
                time_24hr: true,
                altFormat: 'd/m/Y H:i',
                altInput: true,
                dateFormat: 'Y-m-d H:i',
            },
        }
    },
    props: {
        statusApply: {
            required: true,
        },
        statusParent: {
            required: true,
        },
        applyJobId: {
            required: true,
        }

    },
    created() {
        this.getLogApplyJob();
        this.getListStatusApply();

    },
    methods: {
        getStatusChild(index, status) {
            this.resetForm();
            this.statusParentSelect = status;
            this.listStatusApply[index].children.forEach(element => {
                this.listStatusChild.push({ code: element.id, label: element.name });
            });

        },
        resetForm() {
            this.showModal = false;
            this.listStatusChild = [];
            this.dataApply = { status: null, date: null, note: null, };
            this.error = { status: null, date: null, note: null, };
        },
        async onSubmit(e) {
            this.isLoading = true;
            await axios.post('/api/change-status-apply-job', {
                status: this.dataApply.status != null ? this.dataApply.status.code : null,
                note: this.dataApply.note,
                status_date: this.dataApply.date,
                apply_job_id: this.applyJobId,
            }).then(res => {
                this.isLoading = false;
                this.statusParentData = this.statusParentSelect;
                new Noty({ type: "success", text: res.data.success, }).show();
                this.getLogApplyJob();
                this.resetForm();
            }).catch(err => {

                this.isLoading = false;

                if (err.response.data.errors.status) {
                    this.error.status = err.response.data.errors.status[0];
                }
                if (err.response.data.errors.date) {
                    this.error.status = err.response.data.errors.date[0];
                }
                if (err.response.data.errors.note) {
                    this.error.status = err.response.data.errors.note[0];
                }
            });

        },
        async getLogApplyJob() {
            this.isLoading = true;
            await axios.get('/api/get-log-apply-job/' + this.applyJobId).then(res => {
                this.listLogChangeStatusApply = res.data.data;
                this.isLoading = false;
            }).catch(err => {
                // this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        async getListStatusApply() {
            this.isLoading = true;
            axios.post('/api/status-apply-job').then(res => {
                if (res && res.data && res.data.data && res.data.data.length > 0) {
                    this.listStatusApply = res.data.data;
                    this.isLoading = false;
                }
            }).catch(err => {
                this.isLoading = false;
                alert('Có lỗi xảy ra ')
            });
        },
        updateStyle(status, key) {
            let keyParent = null;
            if (status == this.statusParentData) {
                return 'active';
            }
            this.listStatusApply.forEach((value, index) => {
                if (value.id == this.statusParentData) {
                    keyParent = index;
                }
            });
            if (keyParent > key) {
                return 'complete';
            }

        }
    }
}
</script>
<style scoped>
.flatpickr-wrapper {
    width: 100% !important;
}

</style>
