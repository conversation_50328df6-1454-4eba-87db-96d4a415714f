<?php

return [
    'remote' => [
        'on'  => 1,
        'off' => 0,
    ],
    'urgent' => [
        'on'  => 1,
        'off' => 0,
    ],
    'age-range' => [

    ],
    'it_career' => [30, 31],
    'province' => [
        1 => 'Hà Nội',
        2 => '<PERSON><PERSON>',
        3 => 'An Giang',
        4 => '<PERSON><PERSON> R<PERSON> – Vũng Tàu',
        5 => 'Bạc Liêu',
        6 => 'Bắc Giang',
        7 => 'Bắc Kạn',
        8 => 'Bắc Ninh',
        9 => 'Bến Tre',
        10 => 'Bình Dương',
        11 => 'Bình Định',
        12 => 'Bình Phước',
        13 => '<PERSON><PERSON><PERSON> Thuận',
        14 => '<PERSON>à Mau',
        15 => 'Cao Bằng',
        16 => 'Cần Thơ',
        17 => 'Đà Nẵng',
        18 => 'Đắk Lắk',
        19 => 'Đắk Nông',
        20 => 'Đi<PERSON><PERSON>',
        21 => '<PERSON><PERSON><PERSON>',
        22 => '<PERSON><PERSON><PERSON>',
        23 => '<PERSON><PERSON> Lai',
        24 => '<PERSON><PERSON> G<PERSON>',
        25 => '<PERSON><PERSON> Nam',
        26 => '<PERSON><PERSON> Tĩnh',
        27 => '<PERSON><PERSON>i Dương',
        28 => 'Hải Phòng',
        29 => 'Hậu Giang',
        30 => 'Hòa Bình',
        31 => 'Hưng Yên',
        32 => 'Khánh Hòa',
        33 => 'Kiên Giang',
        34 => 'Kon Tum',
        35 => 'Lai Châu',
        36 => 'Lạng Sơn',
        37 => 'Lào Cai',
        38 => 'Lâm Đồng',
        39 => 'Long An',
        40 => 'Nam Định',
        41 => 'Nghệ An',
        42 => 'Ninh Bình',
        43 => 'Ninh Thuận',
        44 => 'Phú Thọ',
        45 => 'Phú Yên',
        46 => 'Quảng Bình',
        47 => 'Quảng Nam',
        48 => 'Quảng Ngãi',
        49 => 'Quảng Ninh',
        50 => 'Quảng Trị',
        51 => 'Sóc Trăng',
        52 => 'Sơn La',
        53 => 'Tây Ninh',
        54 => 'Thái Bình',
        55 => 'Thái Nguyên',
        56 => 'Thanh Hóa',
        57 => 'Thừa Thiên Huế',
        58 => 'Tiền Giang',
        59 => 'Trà Vinh',
        60 => 'Tuyên Quang',
        61 => 'Vĩnh Long',
        62 => 'Vĩnh Phúc',
        63 => 'Yên Bái',
    ],
    'country' => [
        1 => 'Vietnam',
        2 => 'Afghanistan',
        3 => 'Albania',
        4 => 'Algeria',
        5 => 'Andorra',
        6 => 'Angola',
        7 => 'Antigua and Barbuda',
        8 => 'Argentina',
        9 => 'Armenia',
        10 => 'Australia',
        11 => 'Austria',
        12 => 'Azerbaijan',
        13 => 'Bahamas',
        14 => 'Bahrain',
        15 => 'Bangladesh',
        16 => 'Barbados',
        17 => 'Belarus',
        18 => 'Belgium',
        19 => 'Belize',
        20 => 'Benin',
        21 => 'Bhutan',
        22 => 'Bolivia',
        23 => 'Bosnia and Herzegovina',
        24 => 'Botswana',
        25 => 'Brazil',
        26 => 'Brunei',
        27 => 'Bulgaria',
        28 => 'Burkina Faso',
        29 => 'Burundi',
        30 => 'Cabo Verde',
        31 => 'Cambodia',
        32 => 'Cameroon',
        33 => 'Canada',
        34 => 'Central African Republic',
        35 => 'Chad',
        36 => 'Chile',
        37 => 'China',
        38 => 'Colombia',
        39 => 'Comoros',
        40 => 'Congo',
        41 => 'Costa Rica',
        42 => 'Côte d’Ivoire',
        43 => 'Croatia',
        44 => 'Cuba',
        45 => 'Cyprus',
        46 => 'Czechia',
        47 => 'Denmark',
        48 => 'Djibouti',
        49 => 'Dominica',
        50 => 'Dominican Republic',
        51 => 'DR Congo',
        52 => 'Ecuador',
        53 => 'Egypt',
        54 => 'El Salvador',
        55 => 'Equatorial Guinea',
        56 => 'Eritrea',
        57 => 'Estonia',
        58 => 'Eswatini',
        59 => 'Ethiopia',
        60 => 'Fiji',
        61 => 'Finland',
        62 => 'France',
        63 => 'Gabon',
        64 => 'Gambia',
        65 => 'Georgia',
        66 => 'Germany',
        67 => 'Ghana',
        68 => 'Greece',
        69 => 'Grenada',
        70 => 'Guatemala',
        71 => 'Guinea',
        72 => 'Guinea-Bissau',
        73 => 'Guyana',
        74 => 'Haiti',
        75 => 'Holy See',
        76 => 'Honduras',
        77 => 'Hungary',
        78 => 'Iceland',
        79 => 'India',
        80 => 'Indonesia',
        81 => 'Iran',
        82 => 'Iraq',
        83 => 'Ireland',
        84 => 'Israel',
        85 => 'Italy',
        86 => 'Jamaica',
        87 => 'Japan',
        88 => 'Jordan',
        89 => 'Kazakhstan',
        90 => 'Kenya',
        91 => 'Kiribati',
        92 => 'Kuwait',
        93 => 'Kyrgyzstan',
        94 => 'Laos',
        95 => 'Latvia',
        96 => 'Lebanon',
        97 => 'Lesotho',
        98 => 'Liberia',
        99 => 'Libya',
        100 => 'Liechtenstein',
        101 => 'Lithuania',
        102 => 'Luxembourg',
        103 => 'Madagascar',
        104 => 'Malawi',
        105 => 'Malaysia',
        106 => 'Maldives',
        107 => 'Mali',
        108 => 'Malta',
        109 => 'Marshall Islands',
        110 => 'Mauritania',
        111 => 'Mauritius',
        112 => 'Mexico',
        113 => 'Micronesia',
        114 => 'Moldova',
        115 => 'Monaco',
        116 => 'Mongolia',
        117 => 'Montenegro',
        118 => 'Morocco',
        119 => 'Mozambique',
        120 => 'Myanmar',
        121 => 'Namibia',
        122 => 'Nauru',
        123 => 'Nepal',
        124 => 'Netherlands',
        125 => 'New Zealand',
        126 => 'Nicaragua',
        127 => 'Niger',
        128 => 'Nigeria',
        129 => 'North Korea',
        130 => 'North Macedonia',
        131 => 'Norway',
        132 => 'Oman',
        133 => 'Pakistan',
        134 => 'Palau',
        135 => 'Panama',
        136 => 'Papua New Guinea',
        137 => 'Paraguay',
        138 => 'Peru',
        139 => 'Philippines',
        140 => 'Poland',
        141 => 'Portugal',
        142 => 'Qatar',
        143 => 'Romania',
        144 => 'Russia',
        145 => 'Rwanda',
        146 => 'Saint Kitts & Nevis',
        147 => 'Saint Lucia',
        148 => 'Samoa',
        149 => 'San Marino',
        150 => 'Sao Tome & Principe',
        151 => 'Saudi Arabia',
        152 => 'Senegal',
        153 => 'Serbia',
        154 => 'Seychelles',
        155 => 'Sierra Leone',
        156 => 'Singapore',
        157 => 'Slovakia',
        158 => 'Slovenia',
        159 => 'Solomon Islands',
        160 => 'Somalia',
        161 => 'South Africa',
        162 => 'South Korea',
        163 => 'South Sudan',
        164 => 'Spain',
        165 => 'Sri Lanka',
        166 => 'St. Vincent & Grenadines',
        167 => 'State of Palestine',
        168 => 'Sudan',
        169 => 'Suriname',
        170 => 'Sweden',
        171 => 'Switzerland',
        172 => 'Syria',
        173 => 'Tajikistan',
        174 => 'Tanzania',
        175 => 'Thailand',
        176 => 'Timor-Leste',
        177 => 'Togo',
        178 => 'Tonga',
        179 => 'Trinidad and Tobago',
        180 => 'Tunisia',
        181 => 'Turkey',
        182 => 'Turkmenistan',
        183 => 'Tuvalu',
        184 => 'Uganda',
        185 => 'Ukraine',
        186 => 'United Arab Emirates',
        187 => 'United Kingdom',
        188 => 'United States',
        189 => 'Uruguay',
        190 => 'Uzbekistan',
        191 => 'Vanuatu',
        192 => 'Venezuela',
        193 => 'Yemen',
        194 => 'Zambia',
        195 => 'Zimbabwe',
    ],
    'career' => [
        'vi' => [
            0  => '--- Chọn ---',
            1  => 'An toàn lao động',
            2  => 'Bác sĩ',
            3  => 'Bán hàng kỹ thuật',
            4  => 'Bán hàng',
            5  => 'Bán lẻ/Bán sỉ',
            6  => 'Bảo hiểm',
            7  => 'Bảo trì/Sửa chữa',
            8  => 'Bất động sản',
            9  => 'Biên phiên dịch',
            10 => 'Cấp quản lý điều hành',
            11 => 'Chứng khoán',
            12 => 'Cơ khí',
            13 => 'Công nghệ cao',
            14 => 'Dầu khí',
            15 => 'Dệt may/Da giày',
            16 => 'Dịch vụ khách hàng',
            17 => 'Dược Phẩm/Công nghệ sinh học',
            18 => 'Dược sĩ',
            19 => 'Giáo dục/Đào tạo',
            20 => 'Hàng cao cấp',
            21 => 'Hàng gia dụng',
            22 => 'Hàng hải',
            23 => 'Hàng không/Du lịch',
            24 => 'Hàng tiêu dùng',
            25 => 'Hành chánh/Thư ký',
            26 => 'Hóa học/Hóa sinh',
            27 => 'Hoạch định/Dự án',
            28 => 'In ấn/ Xuất bản',
            29 => 'Internet/Online Media',
            30 => 'IT - Phần mềm',
            31 => 'IT-Phần cứng/Mạng',
            32 => 'Kế toán',
            33 => 'Khác',
            34 => 'Kho vận',
            35 => 'Kiểm toán',
            36 => 'Kiến trúc/Thiết kế nội thất',
            37 => 'Marketing',
            38 => 'Mới tốt nghiệp',
            39 => 'Môi trường/Xử lý chất thải',
            40 => 'Mỹ Thuật/Nghệ Thuật/Thiết Kế',
            41 => 'Ngân hàng',
            42 => 'Người nước ngoài/Việt Kiều',
            43 => 'Nhà hàng/Khách sạn',
            44 => 'Nhân sự',
            45 => 'Nông nghiệp/Lâm nghiệp',
            46 => 'Overseas Jobs',
            47 => 'Pháp lý',
            48 => 'Phi chính phủ/Phi lợi nhuận',
            49 => 'QA/QC',
            50 => 'Quảng cáo/Khuyến mãi/Đối ngoại',
            51 => 'Sản phẩm công nghiệp',
            52 => 'Sản Xuất',
            53 => 'Tài chính/Đầu tư',
            54 => 'Thời trang',
            55 => 'Thời vụ/Hợp đồng ngắn hạn',
            56 => 'Thu Mua/Vật Tư/Cung Vận',
            57 => 'Thực phẩm & Đồ uống',
            58 => 'Trình dược viên',
            59 => 'Truyền hình/Truyền thông/Báo chí',
            60 => 'Tư vấn',
            61 => 'Tự động hóa/Ô tô',
            62 => 'Vận chuyển/Giao nhận',
            63 => 'Viễn Thông',
            64 => 'Xây dựng',
            65 => 'Xuất nhập khẩu',
            66 => 'Y sĩ/Hộ lý',
            67 => 'Y tế/Chăm sóc sức khỏe',
            68 => 'Địa chất/Khoáng sản',
            69 => 'Điện lạnh/Nhiệt lạnh',
            70 => 'Điện/Điện tử',
        ],
        'en' => [
            0  => 'All',
            1  => 'HSE',
            2  => 'Doctors',
            3  => 'Sales Technical',
            4  => 'Sales',
            5  => 'Retail/Wholesale',
            6  => 'Insurance',
            7  => 'Maintenance',
            8  => 'Real Estate',
            9  => 'Interpreter/Translator',
            10 => 'Executive management',
            11 => 'Securities & Trading',
            12 => 'Mechanical',
            13 => 'High Technology',
            14 => 'Oil/Gas',
            15 => 'Textiles/Garments/Footwear',
            16 => 'Customer Service',
            17 => 'Pharmaceutical/Biotech',
            18 => 'Pharmacist',
            19 => 'Education/Training',
            20 => 'Luxury Goods',
            21 => 'Household',
            22 => 'Marine',
            23 => 'Airlines/Tourism',
            24 => 'FMCG',
            25 => 'Administrative/Clerical',
            26 => 'Chemical/Biochemical',
            27 => 'Planning/Projects',
            28 => 'Printing',
            29 => 'Internet/Online Media',
            30 => 'IT - Software',
            31 => 'IT - Hardware/Networking',
            32 => 'Accounting',
            33 => 'Other',
            34 => 'Warehouse',
            35 => 'Auditing',
            36 => 'Architecture/Interior Design',
            37 => 'Marketing',
            38 => 'Entry level',
            39 => 'Environment/Waste Services',
            40 => 'Arts/Design',
            41 => 'Banking',
            42 => 'Expatriate Jobs in Vietnam',
            43 => 'Restaurant/Hotel',
            44 => 'Human Resources',
            45 => 'Agriculture/Forestry',
            46 => 'Overseas Jobs',
            47 => 'Legal/Contracts',
            48 => 'NGO/Non-Profit',
            49 => 'QA/QC',
            50 => 'Advertising/Promotion/PR',
            51 => 'Industrial Products',
            52 => 'Production/Process',
            53 => 'Finance/Investment',
            54 => 'Fashion/Lifestyle',
            55 => 'Temporary/Contract',
            56 => 'Merchandising/Purchasing/Supply Chain',
            57 => 'Food & Beverage',
            58 => 'Pharmaceutical representatives',
            59 => 'TV/Media/Newspaper',
            60 => 'Consulting',
            61 => 'Auto/Automotive',
            62 => 'Freight/Logistics',
            63 => 'Telecommunications',
            64 => 'Civil/Construction',
            65 => 'Export-Import',
            66 => 'Doctors/nurses',
            67 => 'Health/Medical Care',
            68 => 'Geology/Mineral',
            69 => 'HVAC',
            70 => 'Electrical/Electronics',
        ],
    ],

    'rank' => [
        'vi' => [
//            0 => 'Tất cả cấp bậc',
            1 => 'Thực tập sinh/Sinh viên',
            2 => 'Mới Tốt Nghiệp',
            3 => 'Nhân viên',
            4 => 'Trưởng phòng',
            5 => 'Giám Đốc và Cấp Cao Hơn',
        ],
        'en' => [
//            0 => 'All levels',
            1 => 'Intern/Student',
            2 => 'Fresher/Entry level',
            3 => 'Experienced (non-manager)',
            4 => 'Manager',
            5 => 'Director and above',
        ],
    ],
    'rank_it' => [
        'vi' => [
            1 => 'Thực tập sinh - Intern',
            2 => 'Mới ra trường (<1 năm kinh nghiệm)',
            3 => 'Nhân viên (từ 1- dưới 2 năm kinh nghiệm)',
            4 => 'Nhân viên có kinh nghiệm (từ 2 - dưới 4 năm kinh nghiệm)',
            5 => 'Nhân viên trên 4 năm kinh nghiệm',
            6 => 'Cấp trưởng phòng',
            7 => 'Cấp giám đốc và cao hơn',
            8 => 'Thực tập sinh - Intern',
            9 => 'Mới ra trường (<1 năm kinh nghiệm)',
            10 => 'Nhân viên (từ 1- dưới 2 năm kinh nghiệm)',
            11 => 'Nhân viên có kinh nghiệm (từ 2 - dưới 4 năm kinh nghiệm)',
            12 => 'Nhân viên trên 4 năm kinh nghiệm',
            13 => 'Cấp trưởng phòng',
            14 => 'Cấp giám đốc và cao hơn',
            15 => 'Thực tập sinh - Intern',
            16 => 'Mới ra trường (<1 năm kinh nghiệm)',
            17 => 'Nhân viên (từ 1- dưới 2 năm kinh nghiệm)',
            18 => 'Nhân viên có kinh nghiệm (từ 2 - dưới 4 năm kinh nghiệm)',
            19 => 'Nhân viên trên 4 năm kinh nghiệm',
            20 => 'Cấp trưởng phòng',
            21 => 'Cấp giám đốc và cao hơn',
            22 => 'PM1 (Từ 1 - dưới 2 năm kinh nghiệm)',
            23 => 'PM2 (Từ 2 - dưới 3 năm kinh nghiệm)',
            24 => 'PM3 (Từ 3 - dưới 5 năm kinh nghiệm)',
            25 => 'PM4 (Từ 5 đến dưới 6 năm kinh nghiệm)',
            26 => 'PM5 (Trên 6 năm kinh nghiệm)',
            27 => 'Tech Lead',
            28 => 'SA1 (Từ 1 - dưới 2 năm kinh nghiệm)',
            29 => 'SA2 (Từ 2 - dưới 3 năm kinh nghiệm)',
            30 => 'SA3 (Từ 3 - dưới 5 năm kinh nghiệm)',
            31 => 'SA4 (Trên 5 năm kinh nghiệm)',
        ],
        'en' => [
            1 => 'Intern',
            2 => 'Fresh graduate (<1 year of experience)',
            3 => 'Employee (1-2 years of experience)',
            4 => 'Experienced employee (2-4 years of experience)',
            5 => 'Employee with over 4 years of experience',
            6 => 'Department head',
            7 => 'Director and higher',
            8 => 'Intern',
            9 => 'Fresh graduate (<1 year of experience)',
            10 => 'Employee (1-2 years of experience)',
            11 => 'Experienced employee (2-4 years of experience)',
            12 => 'Employee with over 4 years of experience',
            13 => 'Department head',
            14 => 'Director and higher',
            15 => 'Intern',
            16 => 'Fresh graduate (<1 year of experience)',
            17 => 'Employee (1-2 years of experience)',
            18 => 'Experienced employee (2-4 years of experience)',
            19 => 'Employee with over 4 years of experience',
            20 => 'Department head',
            21 => 'Director and higher',
            22 => 'PM1 (1-2 years of experience)',
            23 => 'PM2 (2-3 years of experience)',
            24 => 'PM3 (3-5 years of experience)',
            25 => 'PM4 (5-6 years of experience)',
            26 => 'PM5 (Over 6 years of experience)',
            27 => 'Tech Lead',
            28 => 'SA1 (1-2 years of experience)',
            29 => 'SA2 (2-3 years of experience)',
            30 => 'SA3 (3-5 years of experience)',
            31 => 'SA4 (Over 5 years of experience)',
        ],
    ],

    'type' => [
        'vi' => [
//            'all'       => 'Tất cả loại hình',
            'full-time' => 'Toàn thời gian',
            'part-time' => 'Bán thời gian',
            'intern'    => 'Thực tập',
            'freelance' => 'Nghề tự do',
            'contract'  => 'Hợp đồng thời vụ',
            'other'     => 'Khác',
        ],
        'en' => [
//            'all'       => 'All working type',
            'full-time' => 'Full-time',
            'part-time' => 'Part-time',
            'intern'    => 'Internship',
            'freelance' => 'Freelancer',
            'contract'  => 'Seasonal',
            'other'     => 'Other',
        ],
    ],

    'job_type' => [
        'vi'=> [
            'headhunt' => 'Headhunt',
            'bodyshop' => 'Bodyshop',
            'recland' => 'Recland',
            'itnavi' => 'ITNavi',
        ],
        'en'=> [
            'headhunt' => 'Headhunt',
            'bodyshop' => 'Bodyshop',
            'recland' => 'Recland',
            'itnavi' => 'ITNavi',
        ],
    ],

    'bonus_type' => [
        'vi' => [
            'cv',
            'onboard',
            'interview',
        ],

        'en' => [
            'cv',
            'onboard',
            'interview',
        ],
    ],

    'currency' => [
        'vi' => [
            'VND',
            'USD',
        ],
        'en' => [
            'VND',
            'USD',
        ],
    ],

    'priority' => [
        'Premium',
        'Hot',
        'New'
    ],

    'salary' => [
        '100 - 1000' => '$100 - $1000',
        '1000 - 2000' => '$1000 - $2000',
        '3000 - 4000' => '$3000 - $4000',
        '4000 - 5000' => '$4000 - $5000',
        '5000 - 10000' => '$5000 - $10000',
    ],

    'bonus' => [
        '0-1000'        => '$0 - $1000',
        '1000-2000'     => '$1000 - $2000',
        '2000-3000'     => '$2000 - $3000',
        '3000-4000'     => '$3000 - $4000',
        '5000'          => '>= $5000',
    ],

    'paginate' => 10,
    'status' => [
        'vi' => [
            0 => 'Dừng tuyền',
            1 => 'Đang tuyển',
            2 => 'Hết hạn tuyển',
        ],
        'en' => [
            0 => 'Stop recruiting',
            1 => 'Recruiting',
            2 => 'Recruitment expired',
        ],
    ],
    'is_active' => [
        'vi' => [
            0 => 'Inactive',
            1 => 'Active',
        ],
        'en' => [
            0 => 'Inactive',
            1 => 'Active',
        ],
    ],
];
