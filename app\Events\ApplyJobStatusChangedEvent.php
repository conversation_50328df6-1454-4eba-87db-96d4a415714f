<?php

namespace App\Events;

use App\Models\ApplyJob;
use App\Models\LogChangeStatusApply;
use App\Models\Task;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ApplyJobStatusChangedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $apply_job_log;
    /**
     * Create a new event instance.
     */
    public function __construct(LogChangeStatusApply $apply_job_log)
    {
        $this->apply_job_log = $apply_job_log;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
