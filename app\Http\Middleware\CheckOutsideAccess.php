<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CheckOutsideAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        return $next($request);
        $user = backpack_auth()->user();
        if (!$user || $user->canOutsideAccess()) {
            return $next($request);
        }
        // $clientIp = $request->ip();
        // $accept_ips = config('app.accept_ip');

        // if (!$user->outside_access && !in_array($clientIp, $accept_ips)) {
        abort(403, 'Bạn không thể truy cập CRM từ mạng Internet bên ngoài công ty. <PERSON><PERSON><PERSON> cần x<PERSON> lý gấp, vui lòng gọi điện 0932329007');
        // }

        // return $next($request);
    }
}
