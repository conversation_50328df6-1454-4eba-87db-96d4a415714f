<?php

namespace App\Http\Resources\Api\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'name'                => $this->name,
            'email'               => $this->email,
            'internal_company_id' => $this->internal_company_id,
            'department_id'       => $this->department_id,
            'created_at'          => Carbon::parse($this->created_at)->format('d-m-Y H:i:s')
        ];
    }
}
