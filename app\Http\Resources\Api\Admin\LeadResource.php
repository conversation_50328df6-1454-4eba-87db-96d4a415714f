<?php

namespace App\Http\Resources\Api\Admin;

use App\Helpers\Utils;
use App\Models\Lead;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // $lead = Lead::findOrFail($this->id);
        $source = $this->sourceData;
        // dd(class_basename($this));
        // dd($source);
        return [
            //           'lead_name' => optional($this->lead)->name,
            'id'               => $this->id,
            'company_name'     => $this->company_name,
            'contact_name'     => $this->contact_name,
            'email'            => $this->email,
            'phone'            => $this->phone,
            'status'           => $this->status,
            'can_change_owner' => $this->canChangeOwner(),
            'user'             => [
                'id'       => optional($this->user)->id,
                'name'     => optional($this->user)->name,
                'username' => Utils::getUsernameFromEmail(optional($this->user)->email),
                'email'    => optional($this->user)->email,
            ],
            'company' => [
                'company_name'         => optional($this->company)->name,
                'company_contact_name' => optional($this->contact)->name,
            ],
            'content'              => $this->content,
            'source'               => [
                'id'   => $source ? $source->id : '',
                'key'  => $source ? $source->slug_value : '',
                'name' => $source ? $source->name : '',
            ],
            'time_contact' => Carbon::parse($this->time_contact)->format('Y-m-d H:i:s'),
            'created_at'   => Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
        ];
    }
}
