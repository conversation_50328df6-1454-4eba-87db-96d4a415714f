<?php

namespace App\Http\Controllers\Api\Public;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Models\RawCv;
use Illuminate\Http\Request;

class RawCvController extends Controller
{
    public function __construct()
    {

    }

    public function create(Request $request)
    {
        $data = $request->all();
        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'cv_public' => 'required|string',
            'cv_private' => 'required|string'
        ]);
        $data['phone'] = Utils::cleanPhone($data['phone_number']);
        
        $rawCv = RawCv::create([
            'name'            => $data['full_name'],
            'email'           => $data['email'],
            'phone'           => $data['phone'],
            'raw_data'        => $data,
            'cv_public'       => $data['cv_public'],
            'cv_private'      => $data['cv_private'],
            'source'          => isset($data['source']) ? $data['source'] : null,
            'source_id'       => isset($data['source_id']) ? $data['source_id'] : null,
            'push_to_recland' => false
        ]);

        return response()->json($rawCv, 201);
    }
}
