# Workflow: <PERSON><PERSON><PERSON> <PERSON>ợ<PERSON> Google Chat Notification cho Apply Job

## <PERSON><PERSON> tả

Xây dựng hệ thống thông báo qua Google Chat khi có CV mới được tạo (Apply Job).

## C<PERSON><PERSON> tác vụ đã thực hiện

### 1. Tạo migration cho bảng `google_chat_data`

-   Tạo file migration `database/migrations/2024_07_30_000001_create_google_chat_data_table.php`
-   Bảng chứa các trường:
    -   `space_name`: Tên space
    -   `space_id`: ID của space
    -   `space_key`: Khóa của space
    -   `is_active`: Trạng thái kích hoạt

### 2. Tạo model `GoogleChat`

-   Tạo file model `app/Models/GoogleChat.php`
-   Implement các phương thức:
    -   `sendNotification()`: G<PERSON>i thông báo đến một hoặc nhiều spaces
    -   `sendToWebhook()`: <PERSON><PERSON><PERSON> request đến API webhook

### 3. Tạo Notification Channel cho Google Chat

-   Tạo file `app/Channels/GoogleChatChannel.php` để xử lý gửi notification
-   Đ<PERSON>ng ký channel trong `AppServiceProvider`

### 4. Tạo Notification Classes

-   Tạo file `app/Notifications/GoogleChatNotification.php` - notification cơ bản
-   Tạo file `app/Notifications/ApplyJobGoogleChatNotification.php` - notification dành riêng cho Apply Job

### 5. Cấu hình Event Listener

-   Chỉnh sửa `app/Providers/EventServiceProvider.php` để đăng ký listener
-   Tạo file listener `app/Listeners/SendGoogleChatNotification.php` để lắng nghe sự kiện ApplyJobCreatedEvent

## Cách sử dụng

1. Chạy migration để tạo bảng dữ liệu:

```
php artisan migrate
```

2. Thêm dữ liệu cho Google Chat Space:

```php
\App\Models\GoogleChat::create([
    'space_name' => 'Tên Space',
    'space_id' => 'spaces/AAQAWq_ivUw', // Space ID từ Google Chat
    'space_key' => 'key_value', // Nếu cần
    'is_active' => true,
]);
```

3. Gửi thông báo trực tiếp (nếu cần):

```php
\App\Models\GoogleChat::sendNotification('Nội dung thông báo', 'spaces/AAQAWq_ivUw');
```

4. Mặc định, notification sẽ tự động gửi khi có Apply Job mới được tạo.

## Cấu trúc API Webhook

API sử dụng để gửi thông báo đến Google Chat:

-   URL: https://n8n.hri.com.vn/webhook/send-google-chat-notification
-   Method: POST
-   Headers:
    -   Content-Type: application/json
    -   match-cv-api-key: hri@1008
-   Body:

```json
{
    "space_id": "spaces/AAQAWq_ivUw",
    "message": "Nội dung thông báo"
}
```
