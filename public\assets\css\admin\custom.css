.bg-orange {
    background: #F07525 !important;
    color: #FFFFFF;
}

.color-orange {
    color: #F07525 !important;
}

.float-right {
    float: right;
}

.btn-doing {
    --cui-btn-bg: #7c69ef;
    --cui-btn-border-color: #7c69ef;
    --cui-btn-hover-bg: #6852ed;
    --cui-btn-hover-border-color: #6852ed;
    --cui-btn-active-bg: #6852ed;
    --cui-btn-active-border-color: #6852ed;
    --cui-btn-disabled-bg: #7c69ef;
    --cui-btn-disabled-border-color: #7c69ef;
}

.btn-success {
    --cui-btn-color: #000015;
    --cui-btn-bg: #2eb85c;
    --cui-btn-border-color: #2eb85c;
    --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
    --cui-btn-hover-bg: #4dc374;
    --cui-btn-hover-border-color: #43bf6c;
    --cui-btn-focus-shadow-rgb: 39, 156, 81;
    --cui-btn-active-color: rgba(44, 56, 74, 0.95);
    --cui-btn-active-bg: #58c67d;
    --cui-btn-active-border-color: #43bf6c;
    --cui-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 21, 0.125);
    --cui-btn-disabled-color: #000015;
    --cui-btn-disabled-bg: #2eb85c;
    --cui-btn-disabled-border-color: #2eb85c;
}

.btn-pending {
    --cui-btn-color: rgba(44, 56, 74, 0.95);
    --cui-btn-bg: #f9b115;
    --cui-btn-border-color: #f9b115;
    --cui-btn-hover-color: #000015;
    --cui-btn-hover-bg: #d49612;
    --cui-btn-hover-border-color: #c78e11;
    --cui-btn-focus-shadow-rgb: 221, 160, 28;
    --cui-btn-active-color: #000015;
    --cui-btn-active-bg: #c78e11;
    --cui-btn-active-border-color: #bb8510;
    --cui-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 21, 0.125);
    --cui-btn-disabled-color: rgba(44, 56, 74, 0.95);
    --cui-btn-disabled-bg: #f9b115;
    --cui-btn-disabled-border-color: #f9b115;
}

.progressbar {
    counter-reset: step;
    padding: 0;

    /* for demo */
    margin: 100px auto 0;
}

.progressbar li {
    float: left;
    list-style: none;
    position: relative;
    text-align: center;
}

.progressbar li:before {
    background: #fff;
    border: 2px solid #bebebe;
    border-radius: 50%;
    color: #bebebe;
    content: counter(step);
    counter-increment: step;
    display: block;
    font-weight: 700;
    height: 30px;
    line-height: 27px;
    margin: 0 auto 10px;
    text-align: center;
    width: 30px;
    cursor: pointer;
}

.progressbar li:after {
    background: #979797;
    content: '';
    height: 3px;
    left: -50%;
    position: absolute;
    top: 15px;
    width: 100%;
    z-index: -1;
}

.progressbar li:first-child:after {
    content: none;
}

.progressbar li.active:after,
.progressbar li.complete:after {
    background: #005e80;
}

.progressbar li.active:before,
.progressbar li.complete:before {
    background: #005e80;
    border-color: #005e80;
    color: #fff;
}

.progressbar li.active {
    color: #005e80;
    font-weight: 700;
}

.not-active-background {
    background: #979797 !important;
}

.s-stepper-content .s-stepper-stage {
    display: none;
    padding: 15px 22px;
    box-shadow: 0 2px 4px 0px #aaa;
    background-color: #fff;
}

.s-stepper-content .s-stepper-stage.active {
    display: flex;
}

.s-stepper {
    display: flex;
    box-shadow: 0 2px 4px 0px #aaa;
    counter-reset: section;
    background-color: #fff;
}

.s-stepper .s-step {
    display: flex;
    flex: 1;
    padding: 8px 15px;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.6);
    position: relative;
}

.s-stepper .s-step:first-child:before {
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 0;
    content: "";
    border-top: none;
    border-bottom: none;
}

.s-stepper .s-step.active,
.s-stepper .s-step.done {
    position: relative;
    background-color: #005e80;
    color: rgba(255, 255, 255, 0.7);
    font-weight: bold;
}

.s-stepper .s-step.active:last-child:after {
    border: none;
}

.s-stepper .s-step.active:after {
    position: absolute;
    right: 0;
    top: 0;
    margin-right: -30px;
    width: 0;
    height: 0;
    content: "";
    border-top: 54px solid transparent;
    border-bottom: 54px solid transparent;
    border-left: 30px solid #005e80;
    transition: all 900ms ease-in-out;
}

.s-stepper .s-step.active .s-step-counter:after {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 45px;
    height: 45px;
    border-radius: 9999px;
    border: 1px solid rgba(255, 255, 255, 0.6);
    color: rgba(255, 255, 255, 0.6);
}

.s-stepper .s-step.done .s-step-counter:after {
    border-color: rgba(255, 255, 255, 0.6);
    color: rgba(255, 255, 255, 0.6);
}

.s-stepper .s-step .s-step-counter:after {
    counter-increment: section;
    content: counter(section);
    display: flex;
    font-size: 18px;
    border: 1px solid rgba(0, 0, 0, 0.6);
    width: 45px;
    height: 45px;
    justify-content: center;
    border-radius: 999px;
    align-items: center;
    margin-bottom: 5px;
}

@media screen and (max-width: 1024px) {
    .s-step small {
        display: none;
    }

    .s-step.active:after {
        border-top: 41px solid transparent !important;
        border-bottom: 41px solid transparent !important;
        border-left: 40px solid #005e80 !important;
        margin-right: -40px !important;
    }

    .s-step.active:last-child:after {
        border: none !important;
    }
}

@media screen and (max-width: 550px) {
    .s-stepper {
        display: none;
    }
}

.mr-2-custom {
    margin-right: 1px !important;
    background-color: #005e80;
    color: rgba(255, 255, 255, 0.6) !important;
}

.s-stepper .s-step.mr-2-custom .s-step-counter:after {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 45px;
    height: 45px;
    border-radius: 9999px;
    border: 1px solid rgba(255, 255, 255, 0.6);
    color: rgba(255, 255, 255, 0.6);
}

.ck-editor__editable_inline {
    min-height: 400px;
}

.btn-primary {
    background-color: #7c69ef !important;
}

.navbar-nav .dropdown .nav-link {
    font-size: 14px !important;
    display: flex !important;
}

.navbar-nav .dropdown .dropdown-item {
    font-size: 14px !important;
    display: flex !important;
}

.hidden {
    display: none;
}

.user-info {
    column-gap: 10px;
}

.mr-6 {
    margin-right: 6px;
}