<?php

namespace App\Listeners;

use App\Events\JobCreatedEvent;
use App\Events\LogTaskCreatedEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class JobCreatedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(JobCreatedEvent $event): void
    {
        $job_listener = $event->jobEvent;

        $job_listener->notify(new \App\Notifications\JobCreatedNotification());
    }
}
