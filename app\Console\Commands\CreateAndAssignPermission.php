<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CreateAndAssignPermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:create-and-assign {permission_name : The name of the permission to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new permission and assign it to all existing roles';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $permissionName = $this->argument('permission_name');

        try {
            // Tạo permission mới hoặc lấy permission đã tồn tại
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);

            if ($permission->wasRecentlyCreated) {
                $this->info("✓ Permission '{$permissionName}' created successfully.");
            } else {
                $this->info("✓ Permission '{$permissionName}' already exists.");
            }

            // <PERSON><PERSON>y tất cả các role
            $roles = Role::all();

            if ($roles->isEmpty()) {
                $this->warn('No roles found in the system.');
                return Command::SUCCESS;
            }

            $this->info("Found {$roles->count()} role(s). Assigning permission...");

            // Assign permission vào tất cả các role
            $assignedCount = 0;
            foreach ($roles as $role) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                    $this->line("  ✓ Assigned to role: {$role->name}");
                    $assignedCount++;
                } else {
                    $this->line("  - Role '{$role->name}' already has this permission");
                }
            }

            if ($assignedCount > 0) {
                $this->info("✓ Permission '{$permissionName}' assigned to {$assignedCount} role(s) successfully.");
            } else {
                $this->info("✓ All roles already have the permission '{$permissionName}'.");
            }

            // Clear permission cache
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
            $this->info("✓ Permission cache cleared.");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
