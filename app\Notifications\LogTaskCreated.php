<?php

namespace App\Notifications;

use App\Channels\RingChannel;
use App\Models\Task;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\TaskService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use League\HTMLToMarkdown\HtmlConverter;

class LogTaskCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', RingChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $log_task = clone $notifiable;
        $task = $log_task->task;

        // return (new MailMessage)
        //     ->subject('Task: '. $task->name .' đã được cập nhật trạng thái mới')
        //     ->view('emails.task.log_task_created', compact('log_task', 'task'));
        $converter = new HtmlConverter();

        return (new MailMessage)
            ->subject('Task: "' . $task->name . '" có cập nhật mới #' . $log_task->id)
            ->line('Thông báo cập nhật Task **' . $task->name . '**:')
            ->line($converter->convert($log_task->content))
            ->action('Xem thông tin task', backpack_url('task/' . $task->id . '/show'));
//                     ->line('Thank you for using our application!');
    }

    public function toRing($notifiable)
    {
        if ($notifiable->task_id) {
            $logTaskService = new TaskService();
            $task = $logTaskService->find($notifiable->task_id);
            $datas = [];
            if ($task) {
                $users = User::where('id', $task->user_id)->orWhereIn('id', $task->followers->pluck('id'))->select('name', 'email', 'id')->get()->toArray();
                if (count($users) > 0) {
                    foreach ($users as $user) {
                        $datas[] = [
                            'user_id'    => $user['id'],
                            'is_watched' => 0,
                            'link'       => route('task.show', $task->id),
                            'content'    => 'Thông báo cập nhật Task **' . $task->name . '**',
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ];
                    }

                }
            }
            (new NotificationService())->insert($datas);
        }


    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

}
