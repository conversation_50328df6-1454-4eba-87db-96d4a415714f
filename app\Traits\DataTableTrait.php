<?php

namespace App\Traits;

trait DataTableTrait
{
    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\Datatables\Html\Builder
     */
    protected $ajaxData = [];
    protected $dataParameter = [];

    public function html()
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->ajax($this->ajaxData)
            ->parameters($this->getBuilderParameters());
    }

    protected function getBuilderParameters():array
    {
        $parameters = [
            'paging' => true,
            'lengthMenu' => [[50, 100, 500, -1], [50, 100, 500, 'All']],
            'dom' => "<'row mt-3' <'col-md-2' <'ml-2' l>><'col-md-6' B><'col-md-4' <'mr-2' f>> <'col-md-12 table-responsive' t>>" .
                "<'row'<'col-sm-6' <'ml-2 mb-3' i>><'col-sm-6 text-sm-center' <'mr-2 mb-3' p>>>",
            'buttons' => [

                [
                    'extend' => 'print',
                    'text' => '<i class="fas fa-print"></i> Print',
                    'className' => 'btn btn-sm',
                ],
                [
                    'extend' => 'excel',
                    'text' => '<i class="fas fa-download"></i> Excel',
                    'className' => 'btn btn-sm',
                ],
            ],
            'language' => [
                'paginate' => [
                    'next' => '<i class="fas fa-angle-right"></i>',
                    'previous' => '<i class="fas fa-angle-left"></i>',
                ],
                'url' => asset2('assets/js/vietnamese.json'),
            ],

            'stateSave' => true,
            'scrollX' => true,
        ];
        return array_merge($parameters, $this->dataParameter);
    }
}
