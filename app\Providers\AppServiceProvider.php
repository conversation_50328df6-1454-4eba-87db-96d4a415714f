<?php

namespace App\Providers;


use App\Http\Controllers\Admin\UserCrudController;
use Backpack\PermissionManager\app\Http\Controllers\UserCrudController as UerBackpackCrudController;
use Illuminate\Support\ServiceProvider;
use Livewire\Component;
use App\Models\PersonalAccessToken;
use Laravel\Sanctum\Sanctum;
use App\Channels\GoogleChatChannel;
use Illuminate\Support\Facades\Notification;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(UerBackpackCrudController::class, UserCrudController::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Sanctum::usePersonalAccessTokenModel(PersonalAccessToken::class);
        Component::macro('notify', function ($message, $type = 'success') {
            $this->dispatch('notify', ['message' => $message, 'type' => $type]);
        });

        // Đăng ký channel GoogleChat
        Notification::extend('googleChat', function ($app) {
            return new GoogleChatChannel();
        });
    }
}
