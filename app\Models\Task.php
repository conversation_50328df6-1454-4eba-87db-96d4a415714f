<?php

namespace App\Models;

use App\Services\StatusService;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class Task extends Model implements Auditable
{
    use CrudTrait;
    use Notifiable;
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;
    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'tasks';
    protected $guarded = ['id'];
    // protected $auditExclude = [
    //     'published',
    // ];
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->created_by)) {
                $model->created_by = backpack_auth()->id();
            }
        });
        static::created(function ($model) {
            LogTask::create([
                'task_id'    => $model->id,
                'content'    => 'Task created',
                'created_by' => backpack_auth()->id(),
            ]);
        });
        static::updated(function ($model) {
            if ($model->isDirty('status_id')) {
                $statues = (new StatusService())->getStatus('status-task', true);
                $status = $model->getOriginal('status_id');
                LogTask::create([
                    'task_id'    => $model->id,
                    'content'    => 'Thay đổi trạng thái từ <strong>' . $statues[$status] . '</strong> thành <strong>' . $statues[$model->status_id] . '</strong>',
                    'created_by' => backpack_auth()->id(),
                ]);
            }
        });
    }

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    public function getStartDateAttribute($value)
    {
        return Carbon::parse($value)->format('Y-m-d');
    }

    public function getEndDateAttribute($value)
    {
        return Carbon::parse($value)->format('Y-m-d');
    }

    public function followers()
    {
        return $this->belongsToMany(User::class, 'tasks_followers', 'task_id', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function created_by_user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id', 'id')->where('group', 'status-task');
    }
    public function priorityTask()
    {
        return $this->belongsTo(Status::class, 'priority', 'id')->where('group', 'priority-task');
    }
    public function subTasks()
    {
        return $this->hasMany(SubTasks::class);
    }
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeRoleData($query)
    {
        // return $query->where(function ($q) {
        //     $q->when(backpack_user()->can('task.view.all-data'), function ($q2) {
        //         return $q2;
        //     })->when(backpack_user()->can('task.view.only-company'), function ($q2) {
        //         return $q2->whereHas('user', function ($q) {
        //             $q->where('internal_company_id', backpack_user()->internal_company_id);
        //         });
        //     })->when(backpack_user()->can('task.view.only-team'), function ($q2) {
        //         return $q2->whereHas('user', function ($q) {
        //             $q->where('department_id', backpack_user()->department_id);
        //         });
        //     })->orWhere('user_id', backpack_user()->id);
        // })->orWhereHas('followers', function ($q) {
        //     $q->where('user_id', backpack_user()->id);
        // });
        return $query->where(function ($q) {
            if (backpack_user()->can('task.view.all-data')) {
                return $q;
            } else {
                if (backpack_user()->can('task.view.only-company')) {
                    $q->whereHas('user', function ($q) {
                        $q->where('internal_company_id', backpack_user()->internal_company_id);
                    });
                } elseif (backpack_user()->can('task.view.only-team')) {
                    $q->whereHas('user', function ($q) {
                        $q->where('department_id', backpack_user()->department_id);
                    });
                } else {
                    $q->where('user_id', backpack_user()->id);
                }
            }
            $q->orWhereHas('followers', function ($q2) {
                $q2->where('user_id', backpack_user()->id);
            });
            $q->orWhere('created_by', backpack_user()->id);
        });
        // throw new \Exception('Bạn không có quyền truy cập', 401);

    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function routeNotificationForMail($notification)
    {
        // $task = $this->task;
        $user = User::where('id', $this->user_id)->orWhere('id', $this->created_by)->orWhereIn('id', $this->followers->pluck('id'))->pluck('name', 'email')->toArray();
        return $user;
    }
    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
