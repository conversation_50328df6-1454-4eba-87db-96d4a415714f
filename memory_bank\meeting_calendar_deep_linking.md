# Meeting Calendar Deep Linking Feature

## Tổng quan
Chức năng deep linking cho phép truy cập trực tiếp đến chi tiết cuộc họp thông qua URL parameters.

## Cách sử dụng

### 1. URL Format
```
/admin/meeting-calendar?id={meeting_id}
```

### 2. Tính năng chính
- **Auto-open modal**: Tự động mở modal chi tiết khi có parameter `id`
- **Fallback API**: Nếu meeting không có trong calendar hiện tại, sẽ fetch từ API
- **Error handling**: X<PERSON> lý các trường hợp lỗi (không tồn tại, không có quyền)
- **URL cleanup**: Tự động xóa parameter khỏi URL sau khi xử lý
- **Loading states**: Hiển thị loading indicator khi đang tải data

### 3. User Experience
- **Smooth transition**: Modal mở mượt mà sau khi data load xong
- **Calendar navigation**: Tự động navigate đến tháng chứa cuộc họp
- **Copy link**: Nút copy link trong modal với multiple fallback methods
- **Notifications**: Thông báo thân thiện cho các trạng thái khác nhau
- **Copy fallback dialog**: Dialog backup khi auto-copy không hoạt động

## Implementation Details

### Frontend (MeetingCalendar.vue)
```javascript
// Data properties
pendingMeetingId: null,     // Store meeting ID for deep linking
dataLoaded: false,          // Track calendar data loading state
notification: {...},        // Snackbar notification system

// Key methods
checkDeepLinkParameter()    // Parse URL parameters
openMeetingById(id)        // Open meeting modal by ID
fetchSpecificMeeting(id)   // Fallback API call
generateMeetingLink(id)    // Create shareable links
copyMeetingLink(id)        // Copy link to clipboard (with fallbacks)
showCopyLinkDialog(link)   // Show manual copy dialog
copyFromDialog()           // Copy from dialog input field
```

### Backend API
```php
// New endpoint
GET /api/admin/meeting-bookings/{id}

// Returns meeting with relationships
MeetingBooking::with(['meetingRoom', 'creator', 'participants.user'])
```

### Email Integration
- Email notifications now use deep links: `/admin/meeting-calendar?id={meeting_id}`
- Updated in `MeetingInvitationNotification.php`

## Error Handling

### Frontend Errors
- **Meeting not found**: "Cuộc họp không tồn tại"
- **No permission**: "Bạn không có quyền xem cuộc họp này"
- **Network error**: "Không thể tải thông tin cuộc họp"
- **Copy failed**: "Vui lòng copy link thủ công" (shows dialog)

### Copy Link Functionality
- **Primary method**: Modern `navigator.clipboard.writeText()` API
- **Fallback method**: Legacy `document.execCommand('copy')` with temp input
- **Final fallback**: Manual copy dialog with text field
- **Cross-browser support**: Works on all modern browsers and older ones

### Backend Errors
- **404**: Meeting not found
- **403**: No permission (if implemented)
- **500**: Server error

## Usage Examples

### 1. Email Links
Khi user nhận email mời họp và click "Xem chi tiết":
```
https://crm.hri.com.vn/admin/meeting-calendar?id=123
```

### 2. Sharing Links
User có thể copy link từ modal và chia sẻ:
```javascript
// Click "Copy Link" button
copyMeetingLink(meetingId)
```

### 3. Bookmarks
User có thể bookmark link để truy cập nhanh cuộc họp quan trọng.

## Technical Flow

1. **Page Load**: Component mounted, check URL parameters
2. **Parameter Found**: Store meeting ID, start loading calendar data
3. **Data Loaded**: Attempt to find meeting in current events
4. **Not Found**: Fallback to API call for specific meeting
5. **Success**: Open modal, navigate calendar, show notification
6. **Error**: Show appropriate error message

## Benefits

### For Users
- **Quick access**: Truy cập trực tiếp từ email/bookmark
- **Better UX**: Không cần navigate qua nhiều trang
- **Sharing**: Dễ dàng chia sẻ link cuộc họp

### For System
- **Email integration**: Links trong email hoạt động tốt hơn
- **SEO friendly**: URLs có ý nghĩa
- **Analytics**: Có thể track meeting access patterns

## Future Enhancements

### Possible Improvements
- **URL state management**: Maintain URL when modal opens/closes
- **Meeting highlighting**: Visual highlight trong calendar
- **Breadcrumb navigation**: Show meeting context
- **Social sharing**: Share meeting info to social platforms
- **Calendar sync**: Export to external calendars

### Security Considerations
- **Permission checking**: Ensure user has access to meeting
- **Rate limiting**: Prevent abuse of API endpoints
- **Input validation**: Validate meeting ID parameters