<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ReportService;
use App\Services\StatisticalService;
use App\Services\StatusService;
use Illuminate\Http\Request;

class StatisticalController extends Controller
{
    protected $statisticalService;

    public function __construct(StatisticalService $statisticalService)
    {
        $this->statisticalService = $statisticalService;
    }

    public function changeStatusLead(Request $request)
    {
        $start_date = $request->get('start_date', now()->startOfMonth());
        $end_date = $request->get('end_date', now()->endOfMonth());
        // dd($end_date);
        
        $data = $this->statisticalService->changeStatusLead($start_date, $end_date);
        
        return view('admins.statistical.change-status-lead', [
            'statuses' => $data['statuses'],
            'statistics' => $data['statistics']
        ]);
    }
}
