<div class="tab-container  mb-2">

    <div class="nav-tabs-custom " id="form_tabs">
        <ul class="nav nav-tabs " role="tablist">
            <li role="presentation" class="nav-item">
                <a href="#tab_note" aria-controls="tab_note" role="tab" data-toggle="tab" tab_name="note"
                    data-name="note" data-bs-toggle="tab" class="nav-link text-decoration-none active"
                    aria-selected="true" wire:click.prevent="getNotes()">Ghi
                    chú</a>
            </li>
            <li role="presentation" class="nav-item">
                <a href="#tab_cv" aria-controls="tab_cv" role="tab" data-toggle="tab" tab_name="cv" data-name="cv"
                    data-bs-toggle="tab" class="nav-link text-decoration-none" aria-selected="false"
                    tabindex="-1">CV</a>
            </li>
            <li role="presentation" class="nav-item">
                <a href="#tab_contract" aria-controls="tab_contract" role="tab" data-toggle="tab"
                    tab_name="contract" data-name="contract" data-bs-toggle="tab" class="nav-link text-decoration-none"
                    aria-selected="false" tabindex="-1">Hợp đồng</a>
            </li>
            <li role="presentation" class="nav-item">
                <a href="#tab_job" aria-controls="tab_job" role="tab" data-toggle="tab" tab_name="job"
                    data-name="job" data-bs-toggle="tab" class="nav-link text-decoration-none" aria-selected="false"
                    tabindex="-1">Job</a>
            </li>
        </ul>

        <div class="tab-content ">

            <div role="tabpanel" class="tab-pane active show" id="tab_note">

                <div class="row">
                    @if ($attachment)
                        <div class="form-group col-sm-12 mb-3 attactment-list">
                            @foreach ($attachment as $item)
                                @if (Str::contains($item->type, 'image'))
                                    <div style="display: grid; gap: 10px;">
                                        <a href="{{ gen_url_file_s3($item->path) }}" target="_blank">
                                            <img height="200" src="{{ gen_url_file_s3($item->path) }}">
                                        </a>
                                    </div>
                                @else
                                    <!-- get extension -->
                                    @php
                                        $ext = pathinfo($item->path, PATHINFO_EXTENSION);
                                    @endphp
                                    @if (isset(config('constant.file_type_icon')[$ext]))
                                        <div class="attactment-info">
                                            <a href="{{ gen_url_file_s3($item->path) }}" target="_blank">
                                                <img src="{{ asset('assets/img/icon/' . config('constant.file_type_icon')[$ext]) }}"
                                                    height="50" width="50">
                                                <div class="attactment-name">{{ $item->name }}</div>
                                            </a>
                                        </div>
                                    @endif
                                @endif
                            @endforeach
                        </div>
                    @endif
                    <div class="form-group col-sm-12 mb-3">
                        <label>Ghi chú</label>
                        <textarea name="description" class="form-control" wire:model="content"></textarea>
                        <div class="invalid-feedback" @if (!is_null($validateComment)) style="display: block" @endif>
                            <span class="text-danger">{{ $validateComment }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        Chọn file đính kèm: <input type="file" wire:model="photos" multiple>
                        <div wire:loading wire:target="photos">Uploading...</div>
                        {{-- <div x-data="{ uploading: false, progress: 0 }" x-on:livewire-upload-start="uploading = true"
                            x-on:livewire-upload-finish="uploading = false"
                            x-on:livewire-upload-cancel="uploading = false"
                            x-on:livewire-upload-error="uploading = false"
                            x-on:livewire-upload-progress="progress = $event.detail.progress">
                            <!-- File Input -->
                            <input type="file" wire:model="photo">

                            <!-- Progress Bar -->
                            <div x-show="uploading">
                                <progress max="100" x-bind:value="progress"></progress>
                            </div>
                        </div> --}}
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" wire:click.prevent="createComment()">Tạo</button>
                        <button type="button" class="btn btn-secondary ml-2" wire:click.prevent="resetComment()">Huỷ
                        </button>
                    </div>
                </div>
                {{--                <div class="row"> --}}
                {{--                    @if (count($comments)) --}}
                {{--                        @foreach ($comments as $comment) --}}
                {{--                            <div class="bg-white p-3"> --}}
                {{--                                <div class="d-flex flex-row user-info"> --}}
                {{--                                    <img class="rounded-circle" --}}
                {{--                                        src="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-chat/ava1-bg.webp" --}}
                {{--                                        width="40"> --}}
                {{--                                    <div class="d-flex flex-column justify-content-start ml-2"><span --}}
                {{--                                            class="d-block font-weight-bold name">{{ optional($comment->user)->name }}</span><span --}}
                {{--                                            class="date text-black-50"> --}}
                {{--                                            {{ \Carbon\Carbon::parse($comment->created_at)->format('d-m-Y H:i') }}</span> --}}
                {{--                                    </div> --}}
                {{--                                </div> --}}
                {{--                                <div class="mt-2"> --}}
                {{--                                    <p class="comment-text">{!! $comment->content !!}</p> --}}
                {{--                                </div> --}}
                {{--                            </div> --}}
                {{--                        @endforeach --}}
                {{--                    @else --}}
                {{--                        <div class="d-flex justify-content-center mt-3 mb-3"> --}}
                {{--                            Chưa có nội dung nào --}}
                {{--                        </div> --}}
                {{--                    @endif --}}
                {{--                </div> --}}
                <div class="row">
                    @if (count($comments))
                        @foreach ($comments as $comment)
                            <div class="bg-white p-3">
                                <div class="d-flex flex-row user-info">
                                    <img class="rounded-circle"
                                        src="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-chat/ava1-bg.webp"
                                        width="40">
                                    <div class="d-flex flex-column justify-content-start ml-2">
                                        <span
                                            class="d-block font-weight-bold name"><strong>{{ $comment->user ? App\Helpers\Utils::getUsernameFromEmail(optional($comment->user)->email) : $comment->created_by_username }}</strong></span>
                                        <span class="date text-black-50">
                                            {{ \Carbon\Carbon::parse($comment->created_at)->format('d-m-Y H:i') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p class="comment-text">{!! $comment->content !!}</p>
                                    
                                    @foreach ($comment->attachment as $item)
                                        @if (Str::contains($item->type, 'image'))
                                            <div style="display: grid; gap: 10px;">
                                                <a href="{{ gen_url_file_s3($item->path) }}" target="_blank">
                                                    <img height="200" src="{{ gen_url_file_s3($item->path) }}">
                                                </a>
                                            </div>
                                        @else
                                            <!-- get extension -->
                                            @php
                                                $ext = pathinfo($item->path, PATHINFO_EXTENSION);
                                            @endphp
                                            @if (isset(config('constant.file_type_icon')[$ext]))
                                                <div class="attactment-info">
                                                    <a href="{{ gen_url_file_s3($item->path) }}" target="_blank">
                                                        <img src="{{ asset('assets/img/icon/' . config('constant.file_type_icon')[$ext]) }}"
                                                            height="50" width="50">
                                                        <div class="attactment-name">{{ $item->name }}</div>
                                                    </a>
                                                </div>
                                            @endif
                                        @endif
                                    @endforeach
                                    {{-- @foreach ($comment->attachment as $attachment)
                                        @if (Str::contains($attachment->type, 'image'))
                                            <div style="display: grid; gap: 10px;">
                                                <a href="{{ gen_url_file_s3($attachment->path) }}"
                                                    target="_blank">{{ $attachment->name }}</a>
                                                <img height="200" src="{{ gen_url_file_s3($attachment->path) }}">
                                            </div>
                                        @else
                                            <div
                                                style="background-color:rgb(48, 48, 48); width: 10%; padding: 10px 20px; border-radius: 10px">
                                                <a style="font-size: 10px"
                                                    href="{{ gen_url_file_s3($attachment->path) }}">{{ $attachment->name }}</a>
                                            </div>
                                        @endif
                                    @endforeach --}}
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="d-flex justify-content-center mt-3 mb-3">
                            Chưa có nội dung nào
                        </div>
                    @endif
                </div>

            </div>
            <div role="tabpanel" class="tab-pane" id="tab_contract">

                <div class="row">

                </div>
            </div>
            <div role="tabpanel" class="tab-pane" id="tab_job">

                <div class="row">
                    <table
                        class="table table-striped table-hover nowrap rounded card-table table-vcenter card d-table shadow-xs border-xs dataTable dtr-inline"
                        id="table-cvs">
                        <thead>
                            <tr>
                                <th>Job title</th>
                                <th>Status</th>
                                <th>Ngày cập nhật cuối</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        @foreach ($applies as $apply)
                            <tr>
                                <td>{{ optional($apply->job)->title }}</td>
                                <td>{{ optional($apply->statusApply)->name }}</td>
                                <td>{{ \Carbon\Carbon::parse($apply->updated_at)->format('d/m/y') }}</td>
                                <td>
                                    @if (backpack_user()->can('apply-job.show'))
                                        <a href="{{ route('apply-job.show', $apply->id) }}"
                                            class="btn btn-sm btn-link">
                                            <span><i class="la la-eye"></i> Xem lại</span>
                                        </a>
                                    @endif

                                    @if (backpack_user()->can('apply-job.edit'))
                                        <a href="{{ route('apply-job.edit', $apply->id) }}"
                                            class="btn btn-sm btn-link">
                                            <span><i class="la la-edit"></i> Sửa</span>
                                        </a>
                                    @endif


                                </td>

                            </tr>
                        @endforeach
                    </table>

                </div>
            </div>
            <div role="tabpanel" class="tab-pane" id="tab_cv">

                <a href="{{ route('cv.create', ['candidate_id' => $candidate->id]) }}" class="btn btn-primary">Thêm CV mới</a>
                <div class="rows mt-2">
                    <table
                        class="table table-striped table-hover nowrap rounded card-table table-vcenter card d-table shadow-xs border-xs dataTable dtr-inline"
                        id="table-cvs">
                        <thead>
                            <tr>
                                <th>Job title</th>
                                <th>Email</th>
                                <th>Mobile</th>
                                <th>Cv public</th>
                                <th>Cv private</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        @foreach ($cvs as $cv)
                            <tr>
                                <td>{{ $cv->job_title }}</td>
                                <td>{{ $cv->email }}</td>
                                <td>{{ $cv->mobile }}</td>

                                <td>
                                    @if (!empty($cv->url_cv_public))
                                        <a href="{{ $cv->url_cv_public }}" target="_blank"
                                            class="btn btn-sm btn-link">
                                            <span><i class="la la-eye"></i> Xem Cv</span>
                                        </a>
                                    @endif

                                </td>
                                <td>
                                    @if (!empty($cv->url_cv_private))
                                        <a href="{{ $cv->url_cv_private }}" target="_blank"
                                            class="btn btn-sm btn-link">
                                            <span><i class="la la-eye"></i> Xem Cv</span>
                                        </a>
                                    @endif
                                </td>

                                <td>
                                    <a href="{{ route('cv.edit', $cv->id) }}" target="_blank"
                                        class="btn btn-sm btn-link">
                                        <span><i class="la la-edit"></i> Edit</span>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </table>

                </div>
            </div>

        </div>
    </div>
    <style>
        .attactment-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .attactment-info {
            height: 150px;
            display: inline;
            max-width: 150px;
        }
        .attactment-info img {
            width: 100%;
            height: 100%;
            max-width: 150px;
            /* object-fit: cover; */
        }

        .attactment-name {
            text-align: center;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 150px;
        }
    </style>
</div>
