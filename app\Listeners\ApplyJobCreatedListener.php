<?php

namespace App\Listeners;

use App\Events\ApplyJobCreatedEvent;
use App\Events\LogTaskCreatedEvent;
use App\Events\TaskCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ApplyJobCreatedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ApplyJobCreatedEvent $event): void
    {
        $apply_job = $event->apply_job;
        $apply_job->notify(new \App\Notifications\ApplyJobCreatedAlertNotification());
    }
}
