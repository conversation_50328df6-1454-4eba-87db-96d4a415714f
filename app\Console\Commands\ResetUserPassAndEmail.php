<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class ResetUserPassAndEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:reset {user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('user');
        $emails = explode(',', $email);
        if ($email) {
            // $users = User::where('email', $email)->get();
            $users = User::whereIn('email', $emails)->get();
        } else {
            // $users = User::all();
            $users = [];
            $this->error('Please enter email of user you want to reset password');
        }
        foreach ($users as $user) {
            // Generate a random password
            $password = $this->generatePassword(10);
            $user->password = bcrypt($password);
            $user->save();
            $noti = new \App\Notifications\SendEmailPassword();
            $noti->setPassword($password);
            $user->notify($noti);
            $this->info('Reset password for user ' . $user->email . ' successfully (' . $password . ')');
        }
    }

    function generatePassword($length = 8)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';
        $characterCount = strlen($characters);

        for ($i = 0; $i < $length; $i++) {
            $randomIndex = rand(0, $characterCount - 1);
            $password .= $characters[$randomIndex];
        }

        return $password;
    }
}
