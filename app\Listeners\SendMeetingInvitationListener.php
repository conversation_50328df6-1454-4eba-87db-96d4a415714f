<?php

namespace App\Listeners;

use App\Events\MeetingBookingCreatedEvent;
use App\Notifications\MeetingInvitationNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendMeetingInvitationL<PERSON>ener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MeetingBookingCreatedEvent $event): void
    {
        $meetingBooking = $event->meetingBooking;
        
        // Load relationships
        $meetingBooking->load(['participants.user', 'meetingRoom', 'creator']);
        
        // Send email to each participant
        foreach ($meetingBooking->participants as $participant) {
            $participant->user->notify(new MeetingInvitationNotification($meetingBooking));
            
            // Update email_sent status
            $participant->update(['email_sent' => true]);
        }
    }
}
