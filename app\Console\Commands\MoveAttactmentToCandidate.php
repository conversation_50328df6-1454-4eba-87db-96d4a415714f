<?php

namespace App\Console\Commands;

use App\Models\ApplyJob;
use App\Models\Attachment;
use App\Models\Candidate;
use Illuminate\Console\Command;

class MoveAttactmentToCandidate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:move-attactment-to-candidate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move attactment to candidate';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Attachment::where('object_type', 'App\Models\Cv')->chunk(100, function ($attachments) {
            foreach ($attachments as $attachment) {
                $cvs = $attachment->object;
                $candidate = $cvs->candidate;
                if ($candidate) {
                    $new_attactment = $candidate->attachment()->create([
                        'name' => $attachment->name,
                        'path' => $attachment->path,
                        'created_by' => $attachment->created_by,
                    ]);
                    $attachment->delete();
                    $this->info('Move attactment to candidate: ' . $new_attactment->id . ' - ' . $new_attactment->name);
                }
                // dd($new_attactment);
                
            }
        });
    }
}
