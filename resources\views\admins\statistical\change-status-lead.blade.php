@extends('admins.layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Thống kê thay đổi trạng thái Lead</h3>
                    </div>

                    <div class="card-body">
                        <!-- Form lọc theo thời gian -->
                        <form method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Từ ngày</label>
                                        <input type="date" name="start_date" class="form-control" 
                                               value="{{ request('start_date') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Đến ngày</label>
                                        <input type="date" name="end_date" class="form-control" 
                                               value="{{ request('end_date') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="submit" class="btn btn-primary d-block">Lọc</button>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- Bảng thống kê -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Nhân viên</th>
                                        @foreach($statuses as $status)
                                            <th>Trạng thái {{ $status }}</th>
                                        @endforeach
                                        <th>Tổng</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($statistics as $stat)
                                        <tr>
                                            <td>{{ $stat->user_name }}</td>
                                            @foreach($statuses as $status)
                                                <td>{{ $stat->{"status_".$status} }}</td>
                                            @endforeach
                                            <td>{{ $stat->total }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th>Tổng cộng</th>
                                        @foreach($statuses as $status)
                                            <th>{{ $statistics->sum("status_".$status) }}</th>
                                        @endforeach
                                        <th>{{ $statistics->sum('total') }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .table th, .table td {
        text-align: center;
        vertical-align: middle !important;
    }
    .table th:first-child, .table td:first-child {
        text-align: left;
    }
</style>
@endpush
