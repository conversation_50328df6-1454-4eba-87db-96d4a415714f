<?php

namespace App\Notifications;

use App\Models\MeetingBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MeetingInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $meetingBooking;

    /**
     * Create a new notification instance.
     */
    public function __construct(MeetingBooking $meetingBooking)
    {
        $this->meetingBooking = $meetingBooking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Lời mời họp: ' . $this->meetingBooking->title)
            ->greeting('Xin chào ' . $notifiable->name . '!')
            ->line('Bạn được mời tham gia cuộc họp:')
            ->line('**Tiêu đề:** ' . $this->meetingBooking->title)
            ->line('**Phòng họp:** ' . $this->meetingBooking->meetingRoom->name)
            ->line('**Địa điểm:** ' . $this->meetingBooking->meetingRoom->location)
            ->line('**Thời gian bắt đầu:** ' . $this->meetingBooking->start_time->format('d/m/Y H:i'))
            ->line('**Thời gian kết thúc:** ' . $this->meetingBooking->end_time->format('d/m/Y H:i'))
            ->when($this->meetingBooking->description, function ($message) {
                return $message->line('**Mô tả:** ' . $this->meetingBooking->description);
            })
            ->when($this->meetingBooking->agenda, function ($message) {
                return $message->line('**Nội dung:** ' . $this->meetingBooking->agenda);
            })
            ->when($this->meetingBooking->meeting_link, function ($message) {
                return $message->line('**Link họp trực tuyến:** ' . $this->meetingBooking->meeting_link);
            })
            ->line('**Người tạo:** ' . $this->meetingBooking->creator->name)
            ->action('Xem chi tiết', url('/admin/meeting-calendar?id=' . $this->meetingBooking->id))
            ->line('Cảm ơn bạn!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'meeting_booking_id' => $this->meetingBooking->id,
            'title' => $this->meetingBooking->title,
            'start_time' => $this->meetingBooking->start_time,
            'end_time' => $this->meetingBooking->end_time,
            'room' => $this->meetingBooking->meetingRoom->name,
            'creator' => $this->meetingBooking->creator->name
        ];
    }
}
