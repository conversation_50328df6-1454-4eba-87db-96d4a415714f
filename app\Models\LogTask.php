<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class LogTask extends Model
{
    use Notifiable;
    use HasFactory;

    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();
        static::created(function ($model) {
            event(new \App\Events\LogTaskCreatedEvent($model));
        });
        static::updated(function ($model) {
            
        });
    }
    public function task()
    {
        return $this->belongsTo(Task::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function routeNotificationForMail($notification)
    {
        $task = $this->task;
        $user = User::where('id', $task->user_id)->orWhereIn('id', $task->followers->pluck('id'))->pluck('name', 'email')->toArray();
        return $user;
    }

}
