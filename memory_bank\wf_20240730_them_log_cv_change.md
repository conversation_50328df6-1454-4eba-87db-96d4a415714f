# WorkFlow: Thê<PERSON> log thay đổi CV

## <PERSON><PERSON> tả

Thêm chức năng cập nhật CV che mới và lưu lại lịch sử thay đổi CV.

## <PERSON><PERSON><PERSON> tác vụ cần thực hiện

1. <PERSON><PERSON><PERSON><PERSON> nú<PERSON> "Cập nhật CV che mới" và "Lịch sử thay đổi CV" vào file show.blade.php
2. Tạo modal popup cho việc upload CV mới
3. Tạo route xử lý các request API
4. Tạo controller mới để xử lý việc:
    - Upload CV mới
    - Lưu meta data của ApplyJob
    - Hi<PERSON>n thị lịch sử thay đổi CV

## Các file sẽ được sửa đổi/tạo mới

1. `resources/views/admins/apply_job/show.blade.php` - Thêm nút và modal
2. `routes/backpack/custom.php` - Thêm route mới
3. `app/Http/Controllers/Admin/ApplyJobCvController.php` - Controller mới

## <PERSON><PERSON><PERSON> yêu cầu kỹ thuật

-   <PERSON><PERSON> dụng Zoha\Metable để lưu meta data của ApplyJob
-   Lưu CV mới vào AWS S3 sử dụng FileServiceS3
-   Cập nhật key "lasted_cv_private" với giá trị là đường dẫn file mới
-   Ghi log thay đổi CV vào meta key "cv_private_logs" dưới dạng JSON
