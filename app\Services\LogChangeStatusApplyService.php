<?php

namespace App\Services;



use App\Models\LogChangeStatusApply;
use App\Models\Status;

class LogChangeStatusApplyService
{

    public function queryList(array $filters = [])
    {
        $limit = !empty($filters['limit']) ? $filters['limit'] : 10;
        return LogChangeStatusApply::query()
            ->when(!empty($filters['apply_job_id']), function ($query) use ($filters) {
                $query->where('apply_job_id', $filters['apply_job_id']);
            })->limit($limit)
            ->latest('id');
    }

    public function create(array $data)
    {
        $status_id = $data['status_id'];
        $apply_job_id = $data['apply_job_id'];
        // Tự động thêm các trạng thái bị thiếu
        $auto_create_status = config('constant.apply-job.auto-create-status');
        $current_status = Status::find($status_id);
        if ($current_status && isset($auto_create_status[$current_status->slug_value])) {
            $auto_create_status = $auto_create_status[$current_status->slug_value];
            foreach ($auto_create_status as $status) {
                $status_obj = Status::where('slug_value', $status)->where('group', 'apply-job')->first();
                if ($status_obj) {
                    // Kiểm tra đã có log chưa 
                    $log_change_status_apply = LogChangeStatusApply::where('apply_job_id', $apply_job_id)->where('status_id', $status_obj->id)->first();
                    if (!$log_change_status_apply) {
                        LogChangeStatusApply::create([
                            'user_id' => $data['user_id'],
                            'apply_job_id' => $apply_job_id,
                            'status_id' => $status_obj->id,
                            'note' => $data['note'] ?? '',
                        ]);
                    }
                }
            }
        }
        // Thêm log trạng thái hiện tại
        return LogChangeStatusApply::query()->create($data);
    }
}
