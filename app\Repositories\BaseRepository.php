<?php

namespace App\Repositories;

use App\Models\User;
use DB;

class BaseRepository
{
    const MODEL = User::class;

    public function optionQuery($options, $query = null)
    {
        if (is_null($query)) {
            $query = $this->query();
        }
        foreach ($options as $key => $option) {
            if (is_array($option)) {
                if (strtolower($option[1]) == "in") {
                    $query->whereIn($option[0], $option[2]); //["start","in",[1,2,3]]
                } else {
                    $query->where($option[0], $option[1], $option[2]); //["start",">",666]
                }
            } else {
                $query->where($key, $option);
            }
        }
        return $query;
    }

    public function find($id, $options = [])
    {
        $query = $this->optionQuery($options);
        return $query->findOrFail($id);
    }

    public function create($attributes = [])
    {
        return $this->query()->create($attributes);
        /*try {
            return $this->query()->create($attributes);
        } catch (\Illuminate\Database\QueryException $ex) {
            return $ex;
        }*/
    }

    public function insert($attributes = [])
    {
        return $this->query()->insert($attributes);
    }

    public function update($id, $options = [], $attributes = [])
    {
        $result = $this->find($id, $options);

        if ($result) {
            $result->update($attributes);
            return $result;
        }
        return false;
    }

    public function delete($id, $options = [])
    {
        $result = $this->find($id, $options);
        if ($result) {
            $result->delete();
            return true;
        }
        return false;
    }

    public function increment($id, $attribute, $valueIncrement = 1)
    {
        $result = $this->find($id);
        if ($result) {
            $result->increment($attribute, $valueIncrement);
            return $result;
        }
        return false;
    }

    public function decrement($id, $attribute, $valueDecrement = 1)
    {
        $result = $this->find($id);
        if ($result) {
            $result->decrement($attribute, $valueDecrement);
            return $result;
        }
        return false;
    }

    public function query()
    {
        return call_user_func(static::MODEL . '::query');
    }

}
