<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_chat_data', function (Blueprint $table) {
            $table->id();
            $table->string('space_id')->nullable();
            // $table->string('space_key')->nullable();
            $table->string('space_name')->nullable();
            $table->string('space_action')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('google_chat_data');
    }
};
