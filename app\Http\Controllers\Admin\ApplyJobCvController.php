<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Jobs\MatchCvJob;
use App\Models\ApplyJob;
use App\Models\MatchScoreCv;
use App\Notifications\ApplyJobGoogleChatNotification;
use App\Notifications\NewFilePrivateApplyJobGoogleChatNotification;
use App\Services\FileServiceS3;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApplyJobCvController extends Controller
{
    /**
     * Cập nhật CV che mới
     */
    public function updateCvPrivate(Request $request, $id)
    {
        $request->validate([
            'cv_private' => 'required|file|mimes:pdf,doc,docx,xlsx,xls',
            'note' => 'nullable|string',
        ]);

        $applyJob = ApplyJob::findOrFail($id);
        $user = backpack_user();

        // Lấy đường dẫn CV private cũ
        $oldCvPrivatePath = $applyJob->getMeta('lasted_cv_private');

        if (empty($oldCvPrivatePath)) {
            // Nếu không có trong metadata, sử dụng CV private từ model Cv
            if (!$applyJob->cv || empty($applyJob->cv->cv_private)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Không thể upload CV'
                ], 500);
            }
            $oldCvPrivatePath = $applyJob->cv->cv_private;
        }

        // Upload CV mới lên AWS S3
        $newCvPrivatePath = null;
        if ($request->hasFile('cv_private')) {
            $newCvPrivatePath = FileServiceS3::getInstance()->uploadToS3(
                $request->file('cv_private'),
                PATH_FOLDER_CV_PRIVATE
            );
        }

        if ($newCvPrivatePath) {
            // Lưu meta data cho CV private mới
            $applyJob->setMeta('lasted_cv_private', $newCvPrivatePath);

            // Tạo log thay đổi CV
            $logData = [
                'old_path' => $oldCvPrivatePath,
                'new_path' => $newCvPrivatePath,
                'user_id' => $user->id,
                'note' => $request->input('note', ''),
                'created_at' => now()->format('Y-m-d H:i:s')
            ];

            // Lấy logs hiện tại hoặc tạo mới nếu chưa có
            $currentLogs = $applyJob->getMeta('cv_private_logs') ?? [];

            // Thêm log mới vào đầu mảng
            if (is_string($currentLogs)) {
                $currentLogs = json_decode($currentLogs, true) ?? [];
            }
            if (!is_array($currentLogs)) {
                $currentLogs = $currentLogs->toArray();
            }

            array_unshift($currentLogs, $logData);

            // Lưu logs mới
            $applyJob->setMeta('cv_private_logs', $currentLogs);
            $data_notify = [
                'user_name' => Utils::getUsernameFromEmail($user->name),
                // 'apply_job_id' => $applyJob->id,
                // 'apply_job_title' => $applyJob->job->title,
                // 'apply_job_company' => $applyJob->job->company->name,
                // 'apply_job_candidate' => $applyJob->candidate->name,
                // 'apply_job_cv' => $newCvPrivatePath,

            ];
            $applyJob->notify(new NewFilePrivateApplyJobGoogleChatNotification($applyJob, $data_notify));

            return response()->json([
                'status' => 'success',
                'message' => 'Cập nhật CV che thành công',
                'data' => [
                    'cv_path' => $newCvPrivatePath,
                    'logs_count' => count($currentLogs)
                ]
            ]);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Không thể upload CV'
        ], 500);
    }

    public function matchScore($id)
    {
        try {
            // $applyJobId = $id;

            // if (!$applyJobId) {
            //     return response()->json(['success' => 0, 'message' => 'Thiếu tham số apply_job_id'], 400);
            // }

            // $matchScore = \App\Models\MatchScoreCv::where('parent_type', 'App\Models\ApplyJob')
            //     ->where('parent_id', $applyJobId)
            //     ->first();
            $matchScore = \App\Models\MatchScoreCv::findOrFail($id);

            if (!$matchScore) {
                return response()->json(['success' => 0, 'message' => 'Không tìm thấy dữ liệu đánh giá'], 404);
            }

            // dd($matchScore->raw_data);
            $rawData = $matchScore->raw_data;
            // dd($rawData);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json(['success' => 0, 'message' => 'Lỗi khi xử lý dữ liệu đánh giá'], 500);
            }

            return response()->json([
                'success' => 1,
                'data' => $rawData,
                'overview_score' => intval($matchScore->overview_score),
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => 0, 'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()], 500);
        }
        // $matchScore = MatchScoreCv::findOrFail($id);
        // return view('admins.jobs.match-score', compact('matchScore'));
    }

    public function matchScoreByApplyId($id)
    {
        $applyJob = ApplyJob::findOrFail($id);
        $matchScore = $applyJob->matchScoreCv;
        if ($matchScore) {
            return $this->matchScore($matchScore->id);
        }
        return response()->json(['success' => 0, 'message' => 'Không tìm thấy dữ liệu đánh giá'], 404);
    }

    public function createMatchScore($id)
    {
        $applyJob = ApplyJob::findOrFail($id);
        $matchScore = $applyJob->matchScoreCv;
        if ($matchScore) {
            return response()->json(['success' => 1]);
        }
        MatchCvJob::dispatchSync($applyJob, $applyJob->job);
        $matchScore = $applyJob->matchScoreCv;
        return response()->json(['success' => 1, 'message' => 'Vui lòng làm mới trang để xem kết quả']);
    }

    /**
     * Lấy lịch sử thay đổi CV
     */
    public function getCvPrivateLogs($id)
    {
        $applyJob = ApplyJob::findOrFail($id);

        // Lấy logs từ meta data
        $logs = $applyJob->getMeta('cv_private_logs') ?? [];

        if (is_string($logs)) {
            $logs = json_decode($logs, true) ?? [];
        }
        $data = [];

        // Thêm thông tin người dùng vào logs
        foreach ($logs as $log) {
            if (isset($log['user_id'])) {
                $user = \App\Models\User::find($log['user_id']);
                $log['user_name'] = $user ? Utils::getUsernameFromEmail($user->name) : 'Unknown';
            } else {
                dd($log);
                $log['user_name'] = 'Unknown';
            }
            if (isset($log['new_path'])) {
                $log['new_path'] = gen_url_file_s3($log['new_path']);
            }
            if (isset($log['old_path'])) {
                $log['old_path'] = gen_url_file_s3($log['old_path']);
            }
            $data[] = $log;
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'logs' => $data,
                'count' => count($data)
            ]
        ]);
    }
}
