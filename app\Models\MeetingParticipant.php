<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\HasRelationshipFields;

class MeetingParticipant extends Model
{
    use HasFactory, HasRelationshipFields;

    protected $fillable = [
        'meeting_booking_id',
        'user_id',
        'status',
        'is_organizer',
        'email_sent'
    ];

    protected $casts = [
        'is_organizer' => 'boolean',
        'email_sent' => 'boolean'
    ];

    /**
     * Get the meeting booking.
     */
    public function meetingBooking()
    {
        return $this->belongsTo(MeetingBooking::class);
    }

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
