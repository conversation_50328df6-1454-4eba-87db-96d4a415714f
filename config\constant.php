<?php
const PATH_FOLDER_SAVE_CV = 'crm-hri-2023/cvs';
const PATH_FOLDER_SAVE_JOB = 'crm-hri-2023/job';
const PATH_FOLDER_ATTACTMENT = 'crm-hri-2023/attactment';
const PATH_FOLDER_CV_PRIVATE = 'crm-hri-2023/cv-private';
const PATH_FOLDER_INTERVIEW_EVALUATION = 'crm-hri-2023/interview-evaluation';
const CACHE_TTL = 86400; // 1 DAY

return [
    'file_type_icon' => [
        'rar'    => 'rar.png',
        'xls'    => 'excel.png',
        'xlsx'   => 'excel.png',
        'zip'    => 'zip.png',
        'doc'    => 'word.png',
        'docx'   => 'word.png',
        'readme' => 'readme.png',
        'psd'    => 'psd.png',
        'pptx'   => 'pptx.png',
        'pdf'    => 'pdf.png',
        // 'undefined' => 'undefined.png',
    ],
    'apply-job' => [
        'skip-check-duplicate' => [
            'sale-danh-gia-trung',
        ],
        'auto-create-status' => [
            'hri-phong-van-pass' => [
                'sale-danh-gia-pass'
            ],
            'sale-da-submit-sang-kh' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass'
            ],
            'kh-cham-cv-pass' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass',
                'sale-da-submit-sang-kh'
            ],
            'kh-phong-van-pass' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass',
                'sale-da-submit-sang-kh',
                'kh-cham-cv-pass'
            ],
            'chot-offer-offering' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass',
                'sale-da-submit-sang-kh',
                'kh-cham-cv-pass',
                'kh-phong-van-pass',
            ],
            'chot-offer-offered' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass',
                'sale-da-submit-sang-kh',
                'kh-cham-cv-pass',
                'kh-phong-van-pass',
            ],
            'onboard-onboarded' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass',
                'sale-da-submit-sang-kh',
                'kh-cham-cv-pass',
                'kh-phong-van-pass',
                'chot-offer-offered',
            ],
            'contract-signed' => [
                'sale-danh-gia-pass',
                'hri-phong-van-pass',
                'sale-da-submit-sang-kh',
                'kh-cham-cv-pass',
                'kh-phong-van-pass',
                'chot-offer-offered',
                'onboard-onboarded',
            ],
        ]
    ],
    'job_type' => [
        'headhunt'          => 11,
        'bodyshop'          => 51,
        'itnavi'            => 84,
        'recland'           => 92,
        'internal'          => 93,
        'recland-interview' => 94,
        'recland-onboard'   => 95,
        'recland-bonusCV'   => 96,
    ],
    'meeting_status' => [
        'pending' => 'Chờ duyệt',
        'approved' => 'Đã duyệt',
        'rejected' => 'Từ chối',
        'cancelled' => 'Hủy bỏ'
    ],
    'cities' => [
        'Hà Nội',
        'Hồ Chí Minh',
        'Đà Nẵng',
        'ĐBSCL',
        'An Giang',
        'Bà Rịa - Vũng Tàu',
        'Bắc Kạn',
        'Bắc Giang',
        'Bạc Liêu',
        'Bắc Ninh',
        'Bến Tre',
        'Biên Hòa',
        'Bình Định',
        'Bình Dương',
        'Bình Phước',
        'Bình Thuận',
        'Cà Mau',
        'Cần Thơ',
        'Cao Bằng',
        'Đắk Lắk',
        'Điện Biên',
        'Đồng Nai',
        'Đồng Tháp',
        'Gia Lai',
        'Hà Giang',
        'Hà Nam',
        'Hà Tây',
        'Hà Tĩnh',
        'Hải Dương',
        'Hải Phòng',
        'Hòa Bình',
        'Huế',
        'Hưng Yên',
        'Khánh Hòa',
        'Kon Tum',
        'Lai Châu',
        'Lâm Đồng',
        'Lạng Sơn',
        'Lào Cai',
        'Long An',
        'Nam Định',
        'Nghệ An',
        'Ninh Bình',
        'Ninh Thuận',
        'Phú Thọ',
        'Phú Yên',
        'Quảng Bình',
        'Quảng Nam',
        'Quảng Ngãi',
        'Quảng Ninh',
        'Quảng Trị',
        'Sóc Trăng',
        'Sơn La',
        'Tây Ninh',
        'Thái Bình',
        'Thái Nguyên',
        'Thanh Hóa',
        'Thừa Thiên-Huế',
        'Tiền Giang',
        'Trà Vinh',
        'Tuyên Quang',
        'Kiên Giang',
        'Vĩnh Long',
        'Vĩnh Phúc',
        'Yên Bái',
        'Khác',
        'Quốc tế',
        'Hậu Giang',
        'Đắk Nông',
    ],
];
