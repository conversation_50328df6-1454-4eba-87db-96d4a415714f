<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Application Report</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
        }
        .group-header {
            background-color: #e6e6e6;
            font-weight: bold;
        }
        .status-header {
            background-color: #f0f0f0;
        }
        .job-title {
            text-align: left;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Job Application Report</h1>
    <table>
        <thead>
            <tr>
                <th rowspan="2">Job Title</th>
                @foreach($reportData as $groupId => $group)
                    <th colspan="{{ count($group['statuses']) }}" class="group-header">{{ $group['name'] }}</th>
                @endforeach
            </tr>
            <tr>
                @foreach($reportData as $group)
                    @foreach($group['statuses'] as $statusId => $status)
                        <th class="status-header">{{ $status['name'] }}</th>
                    @endforeach
                @endforeach
            </tr>
        </thead>
        <tbody>
            @php
                $allJobs = collect($reportData)->flatMap(function($group) {
                    return collect($group['statuses'])->flatMap(function($status) {
                        return $status['jobs'];
                    });
                })->unique('title')->sortBy('title');
            @endphp

            @foreach($allJobs as $jobId => $job)
                <tr>
                    <td class="job-title">{{ $job['title'] }}</td>
                    @foreach($reportData as $group)
                        @foreach($group['statuses'] as $statusId => $status)
                            <td>
                                {{ $status['jobs'][$jobId]['count'] ?? 0 }}
                            </td>
                        @endforeach
                    @endforeach
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr>
                <th>Total</th>
                @foreach($reportData as $group)
                    @foreach($group['statuses'] as $statusId => $status)
                        <th>{{ $status['total'] }}</th>
                    @endforeach
                @endforeach
            </tr>
        </tfoot>
    </table>
</body>
</html>