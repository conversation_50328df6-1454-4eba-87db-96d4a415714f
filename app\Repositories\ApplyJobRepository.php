<?php

namespace App\Repositories;

use App\Models\ApplyJob;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApplyJobRepository extends BaseRepository
{
    const MODEL = ApplyJob::class;

    public function getData()
    {
        $data = $this->query()->select("c.name", "j.title as job_title", "company.name as company_name", "apply_jobs.status", "status.name as status_name",
            "apply_jobs.updated_at", "users.name as users_updated_by", "apply_jobs.note")
            ->join('cvs as c', 'apply_jobs.cv_id', '=', 'c.id')
            ->join('jobs as j', 'apply_jobs.job_id', '=', 'j.id')
            ->join('companies as company', 'j.company_id', '=', 'company.id')
            ->join('status', 'apply_jobs.status', '=', 'status.id')
            ->join('users', 'apply_jobs.updated_by', '=', 'users.id')
            ->orderBy('apply_jobs.updated_at', 'DESC')
            ->limit(20)
            ->get();
        return $data;
    }

    public function getLineChartWeek()
    {
        $start_date = new \DateTime('2024-01-01');
        $end_date = new \DateTime();
        $result = [];

        while($start_date <= $end_date) {
            $end_week = (clone $start_date)->modify('+6 days');

            $count = $this->query()
                ->whereBetween('created_at', [$start_date->format('Y-m-d'), $end_week->format('Y-m-d')])
                ->count();

            $formatted_date = $start_date->format('d/m') . ' - ' . $end_week->format('d/m');

            $result[] = [
                'count' => $count,
                'formatted_date' => $formatted_date,
            ];

            $start_date->modify('+7 days');
        }

        return $result;
    }

}
