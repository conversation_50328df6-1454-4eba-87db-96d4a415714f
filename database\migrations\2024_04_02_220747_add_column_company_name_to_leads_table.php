<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->integer('company_id')->nullable()->change();
            $table->string('company_name')->after('contact_id')->nullable();
            $table->string('contact_name')->after('company_name')->nullable();
            $table->string('email')->after('contact_name')->nullable();
            $table->string('phone')->after('email')->nullable();
            $table->string('source')->after('phone')->nullable();
        });
        Schema::table('log_leads', function (Blueprint $table) {
            $table->integer('company_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->integer('company_id')->nullable(false)->change();
            $table->dropColumn('company_name');
            $table->dropColumn('contact_name');
            $table->dropColumn('email');
            $table->dropColumn('phone');
            $table->dropColumn('source');
        });
        Schema::table('log_leads', function (Blueprint $table) {
            $table->integer('company_id')->nullable(false)->change();
        });
    }
};
