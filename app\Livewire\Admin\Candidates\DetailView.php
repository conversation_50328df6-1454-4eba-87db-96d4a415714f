<?php

namespace App\Livewire\Admin\Candidates;

use App\Models\Candidate;
use App\Models\Comment;
use App\Models\User;
use App\Services\CandidateService;
use App\Services\FileServiceS3;
use App\Services\NoteService;
use AWS\CRT\HTTP\Request;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;

class DetailView extends Component
{
    use WithFileUploads;

    public $candidate_id;

    public $comments;
    public $cvs;
    public $applies;
    public $content = '';
    public $validateComment;

    protected $noteService;
    protected $candidateService;

    #[Validate(['photos.*' => 'image|max:1024'])]
    public $photos = [];

    public function __construct()
    {
        $this->noteService = new NoteService();
        $this->candidateService = new CandidateService();
    }

    public function render()
    {
        $attachment = false;
        $candidate = $this->candidateService->findOrFail($this->candidate_id);
        $this->comments = $candidate->comment()->orderBy('created_at', 'desc')->get();
        if ($candidate){
            $this->cvs = $candidate->cvs;
            $this->applies = $candidate->applies;
            $attachment = $candidate->attachment;
        }
        return view('livewire.admin.candidates.detail-view', compact('attachment', 'candidate'));
    }

    public function getNotes()
    {
        $candidate = $this->candidateService->findOrFail($this->candidate_id);
        $this->comments = $candidate->comment()->orderBy('created_at', 'desc')->get();
    }

    public function createComment()
    {
        // dd($this->photos);
        if ($this->content == '') {
            $this->validateComment = 'Vui lòng nhập bình luận';
            return 0;
        }

        $candidate = $this->candidateService->findOrFail($this->candidate_id);

        $comment = $candidate->comment()->create([
            'content'      => $this->content
        ]);
        if (count($this->photos)) {
            foreach ($this->photos as $photo) {
                $name = md5(time() . Str::slug($photo->getClientOriginalName())) . "." . $photo->getClientOriginalExtension();
                $image = FileServiceS3::getInstance()->uploadToS3LiveWrite($photo->get(), $name, PATH_FOLDER_ATTACTMENT);
                if ($image) {
                    $comment->attachment()->create([
                        'name' => $photo->getClientOriginalName(),
                        'path' => $image
                    ]);
                }
            }
        }

        $this->resetComment();
        $this->notify('Ghi chú thành công', 'success');
        return 1;
    }

    function resetComment()
    {
        $this->validateComment = null;
        $this->content = '';
        $this->photos = [];
    }

}
