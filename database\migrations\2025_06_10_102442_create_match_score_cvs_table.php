<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_score_cvs', function (Blueprint $table) {
            $table->id();
            $table->string('parent_type'); // App\Models\Cv hoặc App\Models\ApplyJob
            $table->unsignedBigInteger('parent_id'); // ID của CV hoặc ApplyJob
            $table->unsignedBigInteger('job_id');
            $table->unsignedBigInteger('cv_id');
            $table->decimal('experience_score', 5, 2)->default(0);
            $table->decimal('skills_score', 5, 2)->default(0);
            $table->decimal('overview_score', 5, 2)->default(0);
            $table->json('raw_data')->nullable(); // Dữ liệu thô từ API
            $table->timestamps();

            // Indexes
            $table->index(['parent_type', 'parent_id']);
            $table->index(['job_id']);
            $table->index(['cv_id']);
            $table->unique(['parent_type', 'parent_id', 'job_id', 'cv_id'], 'unique_match_score');

            // Foreign keys
            $table->foreign('job_id')->references('id')->on('jobs')->onDelete('cascade');
            $table->foreign('cv_id')->references('id')->on('cvs')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_score_cvs');
    }
};
