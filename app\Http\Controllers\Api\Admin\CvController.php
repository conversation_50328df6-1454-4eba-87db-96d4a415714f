<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Admin\ChangeStatusLeadRequest;
use App\Http\Resources\Api\Admin\CvResource;
use App\Http\Resources\Api\Admin\LeadResource;
use App\Http\Resources\Api\Admin\TaskResource;
use App\Models\Cv;
use App\Repositories\CvsRepository;
use App\Services\CvService;
use App\Services\FileServiceS3;
use App\Services\LeadService;
use App\Services\LogLeadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CvController extends Controller
{
    protected $leadService;
    protected $logLeadService;
    protected $cvRepo;

    public function __construct(LogLeadService $logLeadService, LeadService $leadService, CvsRepository $cvRepo)
    {
        $this->logLeadService = $logLeadService;
        $this->leadService = $leadService;
        $this->cvRepo = $cvRepo;
    }



    public function getDetail(Request $request)
    {
        $cv_id = $request->id;
        $cv = Cv::find($cv_id);
        if (!$cv) {
            return response()->json(['message' => 'Not found'], 404);
        }

        return response()->json(CvResource::make($cv));
    }


    public function uploadCvFile(Request $request)
    {
        $cv_id = $request->cv_id;
        $cv_file = $request->cv_file;
        $type = $request->type;
        $cv = Cv::find($cv_id);
        if (!$cv) {
            return response()->json(['message' => 'Not found'], 404);
        }

        if ($type == 'private') {
            $folder = PATH_FOLDER_CV_PRIVATE;
        } else {
            $folder = PATH_FOLDER_SAVE_CV;
        }

        # upload to s3
        $file_path = FileServiceS3::getInstance()->uploadToS3($cv_file, $folder);
        if ($type == 'private') {
            $cv->update(['cv_private' => $file_path]);
        } else {
            $cv->update(['cv_public' => $file_path]);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Upload file success',
            'data' => CvResource::make($cv)
        ]);
    }


    public function genPrivateCv(Request $request)
    {
        $result = ['status' => 'error', 'message' => ''];
        $cv_id = $request->id;
        $cv = Cv::find($cv_id);
        if (!$cv) {
            $result['message'] = 'Not found';
            return response()->json($result, 404);
        }

        $cv_private = CvService::getInstance()->hideCv($cv->cv_public);
        // dd($cv_private);
        if (empty($cv->cv_private) || !$cv->cv_private) {
            $cv->update(['cv_private' => $cv_private]);
        }
        $result = [
            'status' => 'success',
            'message' => 'Generate private cv success',
            'cv_private' => $cv_private
        ];
        return response()->json($result);
    }
    public function getChart()
    {
        return response()->json(['data' => $this->cvRepo->getLineChartWeek()]);
    }


}
