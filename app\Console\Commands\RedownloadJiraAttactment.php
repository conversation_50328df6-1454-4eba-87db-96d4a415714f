<?php

namespace App\Console\Commands;

use App\Jobs\DownloadAttactment;
use App\Models\Attachment;
use Illuminate\Console\Command;

class RedownloadJiraAttactment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:redownload-jira-attactment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-Download Jira Attactment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // while (true) {
            Attachment::where('path', 'like', 'https://rec.hri.com.vn/secure%')->chunk(100, function ($attachments) {
                foreach ($attachments as $attachment) {
                    DownloadAttactment::dispatch($attachment);
                    $this->info($attachment->path);
                }
            });
        // }
    }
}
