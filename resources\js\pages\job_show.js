import { createApp } from 'vue'
import PushJobToReclandModal from '../components/Admin/Job/PushJobToReclandModal.vue';

// Vuetify
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

const vuetify = createVuetify({
    components,
    directives
})

// Tạo app khi document ready
$(document).ready(function () {
    // Tạo div container cho modal
    // $('body').append('<div id="login-to-buy-cv-app"><push-job-to-recland-modal></push-job-to-recland-modal></div>');


    const pushJobApp = createApp({
        components: {
            'push-job-to-recland-modal': PushJobToReclandModal,
        },
        data() {
            return {
                showPushModal: false
            }
        },
        methods: {
            handleSuccess(message) {
                new Noty({
                    type: "success",
                    text: message || 'Push job thành công!',
                }).show();
            },
            handleError(message) {
                new Noty({
                    type: "error",
                    text: message || 'Có lỗi xảy ra!',
                }).show();
            }
        },
        mounted() {
            // Bind click event to push job button
            document.getElementById('push-job-to-recland-btn').addEventListener('click', () => {
                this.showPushModal = true;
            });
        }
    });
    // Chỉ mount app nếu element tồn tại
    if (document.getElementById('app-push-job-to-recland')) {
        pushJobApp.use(vuetify);
        pushJobApp.mount('#app-push-job-to-recland');
    }

    // Xử lý sự kiện click 
    // $(document).on('click', '.show-login-to-buy-cv', function () {
    //     app.sellingId = $(this).data('selling-id');
    //     app.token = $(this).data('token');
    //     $("#modal-candidate-profile").modal('hide');
    //     $('#login-to-buy-cv-modal').modal('show');
    // });
}); 