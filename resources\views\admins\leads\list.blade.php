@extends(backpack_view('blank'))

@php
    $defaultBreadcrumbs = [
        trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
        $crud->entity_name_plural => url($crud->route),
        trans('backpack::crud.list') => false,
    ];

    // if breadcrumbs aren't defined in the CrudController, use the default breadcrumbs
    $breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;
@endphp

@section('header')
    <section class="header-operation container-fluid animated fadeIn d-flex mb-2 align-items-baseline d-print-none"
        bp-section="page-header">
        <h1 class="text-capitalize mb-0" bp-section="page-heading">{!! $crud->getHeading() ?? $crud->entity_name_plural !!}</h1>
        <p class="ms-2 ml-2 mb-0" id="datatable_info_stack" bp-section="page-subheading">{!! $crud->getSubheading() ?? '' !!}</p>
    </section>
@endsection

@section('content')
    {{-- Default box --}}
    <div class="row" bp-section="crud-operation-list">

        {{-- THE ACTUAL CONTENT --}}
        <div class="{{ $crud->getListContentClass() }}">

            <div class="row mb-2 align-items-center">
                <div class="col-sm-9">
                    @if ($crud->buttons()->where('stack', 'top')->count() || $crud->exportButtons())
                        <div class="d-print-none {{ $crud->hasAccess('create') ? 'with-border' : '' }}">
                            @include('crud::inc.button_stack', ['stack' => 'top'])
                            <button class="btn btn-primary" onclick="openImportModal()">
                                <i class="fas fa-file-import"></i> Import Leads
                            </button>
                            <a href="{{ asset('assets/misc/lead_template_import.xlsx') }}" class="text-secondary me-3">
                                <i class="fas fa-download"></i> Tải file mẫu
                            </a>
                        </div>
                    @endif
                </div>
                @if ($crud->getOperationSetting('searchableTable'))
                    <div class="col-sm-3">
                        <div id="datatable_search_stack" class="mt-sm-0 mt-2 d-print-none">
                            <div class="input-icon">
                                <span class="input-icon-addon">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24"
                                        viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                        <path d="M21 21l-6 -6"></path>
                                    </svg>
                                </span>
                                <input type="search" class="form-control"
                                    placeholder="{{ trans('backpack::crud.search') }}..." />
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            {{-- Backpack List Filters --}}
            @if ($crud->filtersEnabled())
                @include('crud::inc.filters_navbar')
            @endif

            <div class="{{ backpack_theme_config('classes.tableWrapper') }}">
                <table id="crudTable"
                    class="{{ backpack_theme_config('classes.table') ?? 'table table-striped table-hover nowrap rounded card-table table-vcenter card d-table shadow-xs border-xs' }}"
                    data-responsive-table="{{ (int) $crud->getOperationSetting('responsiveTable') }}"
                    data-has-details-row="{{ (int) $crud->getOperationSetting('detailsRow') }}"
                    data-has-bulk-actions="{{ (int) $crud->getOperationSetting('bulkActions') }}"
                    data-has-line-buttons-as-dropdown="{{ (int) $crud->getOperationSetting('lineButtonsAsDropdown') }}"
                    data-line-buttons-as-dropdown-minimum="{{ (int) $crud->getOperationSetting('lineButtonsAsDropdownMinimum') }}"
                    data-line-buttons-as-dropdown-show-before-dropdown="{{ (int) $crud->getOperationSetting('lineButtonsAsDropdownShowBefore') }}"
                    cellspacing="0">
                    <thead>
                        <tr>
                            {{-- Table columns --}}
                            @foreach ($crud->columns() as $column)
                                @php
                                    $exportOnlyColumn = $column['exportOnlyColumn'] ?? false;
                                    $visibleInTable = $column['visibleInTable'] ?? ($exportOnlyColumn ? false : true);
                                    $visibleInModal = $column['visibleInModal'] ?? ($exportOnlyColumn ? false : true);
                                    $visibleInExport = $column['visibleInExport'] ?? true;
                                    $forceExport =
                                        $column['forceExport'] ?? (isset($column['exportOnlyColumn']) ? true : false);
                                @endphp
                                <th data-orderable="{{ var_export($column['orderable'], true) }}"
                                    data-priority="{{ $column['priority'] }}" data-column-name="{{ $column['name'] }}"
                                    {{--
                    data-visible-in-table => if developer forced column to be in the table with 'visibleInTable => true'
                    data-visible => regular visibility of the column
                    data-can-be-visible-in-table => prevents the column to be visible into the table (export-only)
                    data-visible-in-modal => if column appears on responsive modal
                    data-visible-in-export => if this column is exportable
                    data-force-export => force export even if columns are hidden
                    --}}
                                    data-visible="{{ $exportOnlyColumn ? 'false' : var_export($visibleInTable) }}"
                                    data-visible-in-table="{{ var_export($visibleInTable) }}"
                                    data-can-be-visible-in-table="{{ $exportOnlyColumn ? 'false' : 'true' }}"
                                    data-visible-in-modal="{{ var_export($visibleInModal) }}"
                                    data-visible-in-export="{{ $exportOnlyColumn ? 'true' : ($visibleInExport ? 'true' : 'false') }}"
                                    data-force-export="{{ var_export($forceExport) }}">
                                    {{-- Bulk checkbox --}}
                                    @if ($loop->first && $crud->getOperationSetting('bulkActions'))
                                        {!! View::make('crud::columns.inc.bulk_actions_checkbox')->render() !!}
                                    @endif
                                    {!! $column['label'] !!}
                                </th>
                            @endforeach

                            @if ($crud->buttons()->where('stack', 'line')->count())
                                <th data-orderable="false" data-priority="{{ $crud->getActionsColumnPriority() }}"
                                    data-visible-in-export="false" data-action-column="true">
                                    {{ trans('backpack::crud.actions') }}</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                    <tfoot>
                        <tr>
                            {{-- Table columns --}}
                            @foreach ($crud->columns() as $column)
                                <th>
                                    {{-- Bulk checkbox --}}
                                    @if ($loop->first && $crud->getOperationSetting('bulkActions'))
                                        {!! View::make('crud::columns.inc.bulk_actions_checkbox')->render() !!}
                                    @endif
                                    {!! $column['label'] !!}
                                </th>
                            @endforeach

                            @if ($crud->buttons()->where('stack', 'line')->count())
                                <th>{{ trans('backpack::crud.actions') }}</th>
                            @endif
                        </tr>
                    </tfoot>
                </table>
            </div>

            @if ($crud->buttons()->where('stack', 'bottom')->count())
                <div id="bottom_buttons" class="d-print-none text-sm-left">
                    @include('crud::inc.button_stack', ['stack' => 'bottom'])
                    <div id="datatable_button_stack" class="float-right float-end text-right hidden-xs"></div>
                </div>
            @endif

        </div>

    </div>
@endsection

@section('after_styles')
    {{-- DATA TABLES --}}
    {{-- @basset('https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap5.min.css')
    @basset('https://cdn.datatables.net/fixedheader/3.3.1/css/fixedHeader.dataTables.min.css')
    @basset('https://cdn.datatables.net/responsive/2.4.0/css/responsive.dataTables.min.css') --}}

    {{-- CRUD LIST CONTENT - crud_list_styles stack --}}
    @stack('crud_list_styles')
	@endsection
	
	@section('after_scripts')
    {{-- @vite('resources/js/app.js') --}}
	<script src="{{ asset('assets/js/admin/axios.min.js') }}"></script>	
    @include('crud::inc.datatables_logic')

    <div id="importModal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Import Leads từ Excel</h5>
                    {{-- <button type="button" class="close" data-dismiss="modal">&times;</button> --}}
                </div>
                <div class="modal-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <div class="custom-file-upload" id="drop-zone">
                                <input type="file" id="fileUpload" accept=".xlsx,.xls" class="d-none">
                                <div class="drop-zone-text">
                                    Kéo thả file Excel hoặc click để chọn file
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="importLeads()">Import</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeImportModal()">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openImportModal() {
            $('#importModal').modal('show');

            // Xử lý drag & drop
            const dropZone = document.getElementById('drop-zone');
            const fileInput = document.getElementById('fileUpload');

            // Click để chọn file
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });

            // Xử lý khi file được chọn
            fileInput.addEventListener('change', function(e) {
                handleFileSelect(e.target.files[0]);
            });

            // Xử lý khi kéo file vào vùng drop
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const file = e.dataTransfer.files[0];
                handleFileSelect(file);
            });
        }

        function closeImportModal() {
            $('#importModal').modal('hide');
        }

        function handleFileSelect(file) {
            if (file) {
                // Kiểm tra định dạng file
                const validExtensions = ['xlsx', 'xls'];
                const fileExtension = file.name.split('.').pop().toLowerCase();

                if (validExtensions.includes(fileExtension)) {
                    document.querySelector('.drop-zone-text').textContent = `File đã chọn: ${file.name}`;
                } else {
                    alert('Vui lòng chọn file Excel (.xlsx hoặc .xls)');
                    document.getElementById('fileUpload').value = '';
                }
            }
        }

        function importLeads() {
            const fileInput = document.getElementById('fileUpload');
            if (!fileInput.files[0]) {
                new Noty({
                    type: "error",
                    text: 'Vui lòng chọn file Excel để import'
                }).show();
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            // Hiển thị loading
            const loadingNoty = new Noty({
                type: 'info',
                text: 'Đang xử lý import...',
                timeout: false,
                closeWith: []
            }).show();

            // Gửi request import
            axios.post('{{ url($crud->route."/import") }}', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                loadingNoty.close();
                new Noty({
                    type: "success",
                    text: response.data.message || 'Import leads thành công'
                }).show();
                
                // Đóng modal và refresh table
                $('#importModal').modal('hide');
                crud.table.ajax.reload();
            })
            .catch(error => {
                loadingNoty.close();
                new Noty({
                    type: "error",
                    text: error.response?.data?.message || 'Đã có lỗi xảy ra khi import leads'
                }).show();
            });
        }
    </script>

    <style>
        .custom-file-upload {
            border: 2px dashed #ccc;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .custom-file-upload:hover,
        .custom-file-upload.dragover {
            border-color: #2196F3;
            background-color: #E3F2FD;
        }

        .drop-zone-text {
            color: #666;
            font-size: 16px;
        }
    </style>

    {{-- CRUD LIST CONTENT - crud_list_scripts stack --}}
    @stack('crud_list_scripts')
@endsection
