<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

if (!function_exists('fieldColumnData')) {
    function fieldColumnData($name, $label, $type, $classCss, $validationRules = '', $value = null)
    {
        return [
            'name'            => $name,
            'label'           => $label,
            'type'            => $type,
            'value'           => $value,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'validationRules' => $validationRules,
        ];
    }
}

if (!function_exists('select2ModelData')) {
    function select2ModelData($label, $type, $name, $model, $column, $classCss = '', $validationRules = '', $value = null)
    {
        return [  // Select2
            'label'           => $label,
            'type'            => $type,
            'name'            => $name,     // the db column for the foreign key

            // optional
            'entity'          => $name,     // the method that defines the relationship in your Model
            'model'           => $model,    // foreign key model
            'attribute'       => $column,   // foreign key attribute that is shown to user
            'validationRules' => $validationRules,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'default'         => $value,
            'placeholder'     => 'Choose option',
            'allows_clear'    => true,
            'options'         => (function ($query) use ($column) {
                return $query->select('id', $column)->get();
            }),
        ];
    }
}
if (!function_exists('selectFormArrayData')) {
    function selectFormArrayData($name, $label, $classCss, $options, $validationRules)
    {
        return [
            'name'            => $name,
            'label'           => $label,
            'type'            => 'select_from_array',
            'allows_null'     => false,
            'options'         => ['' => '---'] + $options,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'validationRules' => $validationRules,
        ];
    }
}

if (!function_exists('inputUpload')) {
    function inputUpload($name, $label, $classCss, $disk = 's3', $validationRules = [])
    {
        return [   // Upload
            'name'            => $name,
            'label'           => $label,
            'type'            => 'upload',
            'upload'          => true,
            'path'            => 'cv',
            'disk'            => $disk,
            'wrapper'         => [
                'class' => $classCss,
            ],
            'validationRules' => $validationRules,
        ];
    }
}

if (!function_exists('gen_url_file_s3')) {
    function gen_url_file_s3($path, $pathDefault = '', $version = true): string
    {
        if ($path) {
            if (Str::startsWith($path, 'http')) {
                return $path;
            }

            if (file_exists(public_path($path))) {
                return asset2($path);
            }

            return Storage::disk('s3')->url($path) . ($version ? version_asset() : null);
        }

        if ($pathDefault && Storage::disk('s3')->exists($pathDefault)) {
            return Storage::disk('s3')->url($pathDefault) . ($version ? version_asset() : null);
        }

        return $pathDefault ? asset2($pathDefault, $version) : '';
    }
}

if (!function_exists('version_asset')) {
    function version_asset(): string
    {
        $version = Cache::remember('version', 86400, function () {
            if (!file_exists(public_path('version.txt'))) {
                file_put_contents(public_path('version.txt'), time());
            }
            return file_get_contents(public_path('version.txt'));
        });

        return '?v=' . $version;
    }
}

if (!function_exists('asset2')) {
    function asset2($path, $version = true): string
    {
        return asset($path) . ($version ? version_asset() : null);
    }

if (!function_exists('secure_file_url')) {
    /**
     * Tạo URL an toàn cho file với logging và bảo mật
     * 
     * @param int $recordId ID của record chứa file
     * @param string $modelName Tên model (Cv, ApplyJob, Job, etc.)
     * @param string $fieldName Tên field chứa file path
     * @param array $options Các tùy chọn bổ sung
     * @return string URL an toàn
     */
    function secure_file_url($recordId, $modelName, $fieldName, $options = []): string
    {
        // Generate hash token for security
        $hash = hash('sha256', $recordId . '|' . $modelName . '|' . $fieldName . '|' . config('app.key'));
        
        // Build URL parameters
        $params = [
            'id' => $recordId,
            'model' => $modelName,
            'field' => $fieldName,
            'hash' => $hash
        ];
        
        return route('secure-file.download', $params);
    }
}

if (!function_exists('secure_file_url_from_path')) {
    /**
     * Wrapper function để dễ dàng migration từ gen_url_file_s3()
     * Tự động detect model và field từ context
     * 
     * @param string $filePath Đường dẫn file
     * @param string $pathDefault Đường dẫn mặc định (backward compatibility)
     * @param bool $version Có thêm version không (backward compatibility)
     * @param array $context Context để xác định model và field
     * @return string
     */
    function secure_file_url_from_path($filePath, $pathDefault = '', $version = true, $context = []): string
    {
        // Nếu không có context, fallback về gen_url_file_s3 cũ
        if (empty($context) || !isset($context['model']) || !isset($context['field']) || !isset($context['id'])) {
            return gen_url_file_s3($filePath, $pathDefault, $version);
        }
        
        return secure_file_url($context['id'], $context['model'], $context['field']);
    }
}

if (!function_exists('get_secure_file_context')) {
    /**
     * Helper để tạo context cho secure file URL
     * 
     * @param object $model Instance của model
     * @param string $fieldName Tên field
     * @return array Context array
     */
    function get_secure_file_context($model, $fieldName): array
    {
        $modelName = class_basename($model);
        
        return [
            'id' => $model->id,
            'model' => $modelName,
            'field' => $fieldName
        ];
    }
}
}
