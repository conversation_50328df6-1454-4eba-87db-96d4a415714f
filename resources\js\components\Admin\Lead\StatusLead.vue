<template>
    <loading v-model:active="isLoading" :is-full-page="fullPage" />
    <v-stepper alt-labels>
        <v-stepper-header>
            <template v-for="(status, index) in listStatusLead">
                <v-stepper-item :color="updateStyle(status.id, index) ? 'info' : ''"
                    :complete="updateStyle(status.id, index)" :title="status.name" :value="index + 1"
                    @click="setStatus(status.id)" editable></v-stepper-item>
                <v-divider v-if="index < listStatusLead.length - 1"></v-divider>
            </template>
        </v-stepper-header>
    </v-stepper>
    <div class="row">

        <!-- <div class="col-12" v-if="listStatusLead.length > 0">
            <div class="s-stepper ">
                <div :id="`stage-` + `${index + 1}` + `-step`" :class="updateStyle(status.id, index)"
                    @click="setStatus(status.id)" v-for="(status, index) in listStatusLead">
                    <span class="s-step-counter"></span>
                    <small>{{ status.name }}</small>
                </div>
            </div>
        </div> -->
    </div>


    <v-card class="mt-3">
        <v-card-text>
            <div class="tab-pane fade show active profile-overview" id="profile-overview" role="tabpanel">
                <div class="row mt-2">
                    <div class="col-lg-3 col-md-4 label ">Tên liên hệ :</div>
                    <div class="col-lg-3 col-md-8">{{ this.lead.contact_name }}</div>
                    <div class="col-lg-3 col-md-4 label ">Công ty :</div>
                    <div class="col-lg-3 col-md-8">{{ this.lead.company_name }}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-lg-3 col-md-4 label ">Số điện thoại :</div>
                    <div class="col-lg-3 col-md-8">{{ this.lead.phone }}</div>
                    <div class="col-lg-3 col-md-4 label ">Email :</div>
                    <div class="col-lg-3 col-md-8">{{ this.lead.email }}</div>
                </div>
                <div v-if="this.lead.company && this.lead.company.company_name">
                    <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label ">Tên công ty :</div>
                        <div class="col-lg-9 col-md-8">{{ this.lead.company.company_name }}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-lg-3 col-md-4 label ">Người liên hệ :</div>
                        <div class="col-lg-9 col-md-8">{{ this.lead.company.company_contact_name }}</div>
                    </div>
                </div>
                <div class="row mt-2" v-if="this.lead.user && this.lead.user.id">
                    <div class="col-lg-3 col-md-4 label">Người tạo lead:</div>
                    <div class="col-lg-9 col-md-8">{{ this.lead.user.username }}</div>
                </div>
                <h5 class="card-title mt-4">Nội dung Lead</h5>

                <p class="mt-2 fs-7" v-html="this.lead.content"></p>
            </div>
        </v-card-text>
        <!-- <template v-slot:actions> -->
        <div class="d-flex flex-row-reverse mb-3 mr-2" w-100>
            <v-btn variant="elevated" color="info" prepend-icon="mdi mdi-pencil" float-right
                :href="`/admin/lead/` + this.lead.id + `/edit`" class=" mr-2">
                Edit
            </v-btn>
            <v-btn variant="elevated" color="success" prepend-icon="mdi mdi-domain"
                :href="`/admin/lead/` + this.lead.id + `/create-company`" class=" mr-2">Tạo thông tin
                khách
                hàng</v-btn>
            <v-btn v-if="this.lead.can_change_owner" variant="elevated" color="orange-lighten-3"
                prepend-icon="mdi mdi-account-convert" @click="showModalChangeUser = true" class=" mr-2">Đổi người phụ
                trách</v-btn>
            <!-- <a :href="`/admin/lead/` + this.lead.id + `/edit`" class="btn btn-sm btn-primary float-right"><i
                    class="nav-icon la la-pencil"></i> Edit</a>
            <a :href="`/admin/lead/` + this.lead.id + `/create-company`"
                class="btn btn-sm btn-success float-right mr-6"><i class="nav-icon la la-plus"></i> Tạo thông tin
                khách
                hàng</a>
            <button v-if="this.lead.can_change_owner" class="btn btn-sm btn-info float-right mr-6"
                @click="showModal = true">Đổi người phụ trách</button> -->
        </div>
        <!-- </template> -->
    </v-card>
    <v-card class="mt-3">
        <v-card-text>
            <v-form @reset="resetForm()" @submit="submitData">
                <v-textarea label="Nội dung cập nhật" v-model="dataSubmit.content"></v-textarea>

                <div class="d-flex flex-row-reverse mb-3 mr-2" w-100>
                    <v-btn variant="elevated" color="info" append-icon="mdi mdi-send-variant" type="submit"
                        class="">Submit
                    </v-btn>
                </div>
            </v-form>
        </v-card-text>
    </v-card>
    <div class=" mt-3">

        <v-card>
            <v-card-text>
                <div class=" 2">
                    <h5 class="pb-3"> Lịch sử thay đổi</h5>
                    <table class="table">
                        <thead>
                            <tr>
                                <td>#</td>
                                <td>Tạo bởi</td>
                                <td>Nội dung</td>
                                <td>Thời gian tạo</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(log, index) in listLogLeads" :key="index">
                                <td>{{ index + 1 }}</td>
                                <td>{{ log.username }}</td>
                                <td v-html="log.content"></td>
                                <td>{{ log.created_at }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </v-card-text>
        </v-card>
    </div>
    <div class="pa-4 text-center">
        <v-dialog v-model="showModalChangeUser" max-width="600">
            <v-card prepend-icon="mdi-account" title="Đổi người phụ trách lead">
                <v-card-text>
                    <v-row dense>
                        <v-col cols="12" sm="12">
                            <v-autocomplete v-model="new_lead_user_id" :items="listUser" item-title="name" item-value="id" label="Người phụ trách mới"
                                required></v-autocomplete>
                        </v-col>

                    </v-row>

                    <!-- <small class="text-caption text-medium-emphasis">*indicates required field</small> -->
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>

                    <v-btn text="Close" variant="plain" @click="showModalChangeUser = false"></v-btn>

                    <v-btn color="primary" text="Save" variant="tonal" @click="changeUser"></v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
    <!-- <BModal v-model="showModal" v-bind:hide-footer="true" title="Update status apply job">
        <BForm @reset="resetForm()" @submit="onSubmit">
            <BFormGroup class="form-group" label="Trạng thái" label-for="status">
                <v-select id="statusApply" v-model="dataApply.status" :options="listStatusChild"></v-select>
                <span class="text-danger"> {{ error.status }}</span>
            </BFormGroup>

            <BFormGroup class="form-group">
                <BButton type="submit" variant="primary" class="float-right m-1">Submit</BButton>
                <BButton type="reset" variant="danger" class="float-right m-1">Cancel</BButton>
            </BFormGroup>
        </BForm>
    </BModal> -->
</template>

<script>

import moment from 'moment'
import axios from 'axios'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';


export default {
    components: { Loading },
    props: {
        lead_id: {
            required: true,
        },
        user_id: {
            required: true,
        },
        company_id: {
            required: true
        },
        contact_id: {
            required: true
        },
        status: {
            required: true
        },
        content: {
            required: true
        }
    },
    data() {
        return {
            moment: moment,
            showModal: false,
            showModalChangeUser: false,
            listStatusLead: [],
            listLogLeads: [],
            lead: [],
            listUser: [],
            isLoading: true,
            fullPage: true,
            keyStatus: null,
            new_lead_user_id: null,
            dataSubmit: {
                time_contact: null,
                content: null,
                lead_id: this.lead_id,
                user_id: this.user_id,
                company_id: this.company_id,
                contact_id: this.contact_id,
                status: this.status,
            },
            config: {
                enableTime: true,
                static: true,
                allowInput: true,
                disableMobile: true,
                wrap: true,
                time_24hr: true,
                altFormat: 'd/m/Y H:i',
                altInput: true,
                dateFormat: 'Y-m-d H:i',
            },
            error: {
                status: null,
                time_contact: null,
                note: null,
            },
        }
    },
    mounted() {
        this.getListStatusLead();
        this.getLogLead();
        this.getListUser();
    },


    methods: {
        changeUser() {
            this.isLoading = true;
            axios.post('/api/change-user-lead/' + this.lead_id, { user_id: this.new_lead_user_id }).then(res => {
                this.isLoading = false;
                this.showModalChangeUser = false;
                this.lead.user_id = this.new_lead_user_id;
                this.lead.user.username = this.listUser.find(x => x.id == this.new_lead_user_id).name;
                // console.log(this.lead.username);
                new Noty({ type: "success", text: res.data.success, }).show();
            }).catch(err => {
                this.isLoading = false;
                alert('Có lỗi xảy ra ');
            });
        },
        getListUser() {
            this.isLoading = true;
            axios.get('/api/get-list-user').then(res => {
                this.listUser = res.data.users;
                this.isLoading = false;
            }).catch(err => {
                this.isLoading = false;
                alert('(getListUser) Có lỗi xảy ra ')
            });
        },
        getLeadDetail() {
            this.isLoading = true;
            axios.get('/api/lead/' + this.lead_id).then(res => {
                this.isLoading = false;
                // console.log(res.data.)
                this.lead = res.data.data;
                // alert(this.lead.user_email)
                this.setKeyActive(this.lead.status_name);
            }).catch(err => {
                this.isLoading = false;
                alert('(getLeadDetail) Có lỗi xảy ra')
            });
        },
        async changeStatus(status) {
            this.isLoading = true;
            await axios.post('/api/change-status-lead/' + this.lead_id, { status: status }).then(res => {
                this.isLoading = false;
                this.getLogLead();
                new Noty({ type: "success", text: res.data.success, }).show();
            }).catch(err => {
                alert('Có lỗi xảy ra ');
            });

        },
        getLogLead() {
            this.isLoading = true;
            axios.get('/api/get-log-lead/' + this.lead_id).then(res => {
                this.listLogLeads = res.data.data;
                this.isLoading = false;
            }).catch(err => {
                // this.isLoading = false;
                alert('(getLogLead) Có lỗi xảy ra ')
            });
        },
        resetForm() {
            this.showModal = false;
            this.dataSubmit = {
                time_contact: null,
                content: null,
                lead_id: this.lead_id,
                user_id: this.user_id,
                company_id: this.company_id,
                contact_id: this.contact_id,
                status: this.status,
            };

            this.error = { time_contact: null, content: null, };
        },
        setKeyActive(status) {
            this.listStatusLead.forEach((item, index) => {
                if (item.id == status) {
                    this.keyStatus = index;
                }
            });
        },
        setStatus(status) {

            if (this.dataSubmit.status != status) {
                this.dataSubmit.status = status;
                this.changeStatus(status);
                this.setKeyActive(status);
            }
        },
        getListStatusLead() {
            this.isLoading = true;
            axios.post('/api/status-parent/status-lead').then(res => {
                if (res && res.data && res.data.data && res.data.data.length > 0) {
                    this.listStatusLead = res.data.data;
                    this.getLeadDetail();
                    this.isLoading = false;
                }
            }).catch(err => {
                this.isLoading = false;
                alert('(getListStatusLead) Có lỗi xảy ra ')
            });
        },

        async submitData(e) {
            e.preventDefault();
            this.isLoading = true;
            await axios.post('/api/create-log-lead', this.dataSubmit).then(res => {
                this.isLoading = false;
                this.getLogLead();
                this.resetForm();
                new Noty({ type: "success", text: res.data.success, }).show();
            }).catch(err => {
                this.isLoading = false;
                if (err.response.data.errors.content) {
                    this.error.content = err.response.data.errors.content[0];
                } else {
                    alert('Có lỗi xảy ra ');
                }

            });
        },
        updateStyle(status, key) {
            let keyParent = this.keyStatus;
            if (key == keyParent || status >= this.status) {
                return true;
                return 's-step active';
            }
            if (keyParent > key) {
                return true;
                return 's-step mr-2-custom';
            }
            return false;
            return 's-step';
        }
    }
}
</script>

<style  scoped>
.label {
    font-weight: bold;
    font-size: 14px;
    color: #666666;
}

.s-stepper {
    text-align: center;
}

.fs-7 {
    font-size: 14px;
}

#list1 .form-control {
    border-color: transparent;
}

#list1 .form-control:focus {
    border-color: transparent;
    box-shadow: none;
}

#list1 .select-input.form-control[readonly]:not([disabled]) {
    background-color: #fbfbfb;
}
</style>
