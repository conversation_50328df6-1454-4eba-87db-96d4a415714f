<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Helpers\Utils;

class UpdateUserNames extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:update-usernames';
    protected $description = 'Update user_name field based on email';

    public function handle()
    {
        $users = User::whereNull('user_name')->get();

        foreach ($users as $user) {
            $user->user_name = Utils::getUsernameFromEmail($user->email);
            $user->save();
        }

        $this->info('User names updated successfully.');
    }
}
