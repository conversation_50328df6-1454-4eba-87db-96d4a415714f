<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class SubTasks extends Model implements AuditableContract
{
    use CrudTrait, HasFactory, Auditable, Notifiable;
    protected $guarded = ['id'];

    public function task(){
        return $this->belongsTo(Task::class,'task_id','id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id', 'id')->where('group', 'status-task');
    }
}
