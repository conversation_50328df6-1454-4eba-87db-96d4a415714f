<?php

namespace App\Console\Commands;

use App\Models\Status;
use App\Models\Task;
use Illuminate\Console\Command;

class AlertTaskExpiring extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:alert-task-expiring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Alert user when task is expiring.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $status_completed = Status::where('group', 'status-task')->where('slug_value', 'completed')->first();
        $tasks = Task::where('end_date', now()->format('Y-m-d'))->where('status_id', '!=', $status_completed->id)->get();
        if (count($tasks)) {
            foreach ($tasks as $task) {
                $task->notify(new \App\Notifications\TaskExpiringAlertNotification());
                $this->info('Alert task expiring for task: ' . $task->id . ' - ' . $task->name . ' successfully.');
            }
        }
        $this->info('Alert task expiring successfully.');
    }
}
