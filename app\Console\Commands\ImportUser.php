<?php

namespace App\Console\Commands;

use App\Models\Department;
use App\Models\InternalCompany;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class ImportUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import user from data array';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $data = $this->data();
        foreach ($data as $item) {
            $role_org = Role::where('name', $item['role_org'])->first();
            $role = Role::where('name', $item['role'])->first();
            $company = InternalCompany::firstOrCreate(['name' => $item['company']]);
            $team = Department::firstOrCreate(['name' => $item['team'], 'internal_company_id' => $company->id]);
            // $team = Department::where('name', $item['team'])->first();
            if ($role_org && !$role) {
                $permission = $role_org->permissions;
                $role = Role::create(['name' => $item['role']]);
                $role->syncPermissions($permission);
                // dd($permission);
            }
            $user = \App\Models\User::where('email', $item['email'])->first();
            if (!$user) {
                $user = new \App\Models\User();
                $user->name = $item['name'];
                $user->email = $item['email'];
                $user->internal_company_id =  $company->id;
                $user->department_id = $team->id;
                $user->password = bcrypt('123456');
                $user->save();
                $user->assignRole($role);
                $this->info($user->email . ' created');
            }
            




            // $company = \App\Models\Company::where('name', $item['company'])->first();
            // if (!$company) {
            //     $company = new \App\Models\Company();
            //     $company->name = $item['company'];
            //     $company->save();
            // }
            // $team = \App\Models\Team::where('name', $item['team'])->first();
            // if (!$team) {
            //     $team = new \App\Models\Team();
            //     $team->name = $item['team'];
            //     $team->save();
            // }
            // $role = \App\Models\Role::where('name', $item['role'])->first();
            // if (!$role) {
            //     $role = new \App\Models\Role();
            //     $role->name = $item['role'];
            //     $role->save();
            // }
            // $role_org = \App\Models\Role::where('name', $item['role_org'])->first();
            // if (!$role_org) {
            //     $role_org = new \App\Models\Role();
            //     $role_org->name = $item['role_org'];
            //     $role_org->save();
            // }
            // $user->companies()->sync([$company->id]);
            // $user->teams()->sync([$team->id]);
            // $user->roles()->sync([$role->id]);
            // $user->roles_org()->sync([$role_org->id]);
        }
    }


    public function data()
    {
        return [
            ['name' => 'Truong Van CUONG', 'company' => 'HRI.Tech', 'team' => 'HRI.Tech', 'role' => 'Giám đốc', 'role_org' => 'Giám đốc', 'email' => '<EMAIL>',],
            ['name' => 'Le Thi DUYEN', 'company' => 'ITNAVI', 'team' => 'HRI Sale', 'role' => 'Nhân viên Sale', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Ngo Lien GIANG', 'company' => 'ITNAVI', 'team' => 'Recland Marketing', 'role' => 'Marketing', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Nguyen Duc MANH', 'company' => 'ITNAVI', 'team' => 'HRI Dev', 'role' => 'Coder', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Do Viet ANH', 'company' => 'HRI', 'team' => 'Recland Marketing', 'role' => 'Marketing', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Tran Thi Ngoc  ANH', 'company' => 'HRI', 'team' => 'HRI Kế Toán', 'role' => 'Kế toán', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Pham Viet  ANH', 'company' => 'HRI', 'team' => 'Recland Marketing', 'role' => 'Marketing', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Phung Thi Thuy DUNG', 'company' => 'HRI', 'team' => 'HRI HR', 'role' => 'HR Manager', 'role_org' => 'Team leader', 'email' => '<EMAIL>',],
            ['name' => 'Pham Hai HA', 'company' => 'HRI.Tech', 'team' => 'HRI.Tech', 'role' => 'Nhân viên Sale', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Bui Thanh HANG', 'company' => 'HRI', 'team' => 'HRI HR', 'role' => 'HR', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Nguyen Cao LONG', 'company' => 'HRI', 'team' => 'HRI HR', 'role' => 'HR', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Nguyen Ha  MY', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Tran Bich NGOC', 'company' => 'HRI', 'team' => 'HRI Sale', 'role' => 'Saler Leader', 'role_org' => 'Team leader', 'email' => '<EMAIL>',],
            ['name' => 'Phan Thi Anh NGUYET', 'company' => 'HRI', 'team' => 'Recland Marketing', 'role' => 'Project Manager', 'role_org' => 'Team leader', 'email' => '<EMAIL>',],
            ['name' => 'Nguyen Thi Lan  PHUONG', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Tran Thi Thanh THAO', 'company' => 'HRI', 'team' => 'HRI.Tech', 'role' => 'Nhân viên Sale', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Nguyen Thi Anh  THU', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Trinh Thi Xuan THU', 'company' => 'HRI', 'team' => 'HRI Kế Toán', 'role' => 'Kế toán', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Nghiem Thanh THUY', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Rec Leader', 'role_org' => 'Team leader', 'email' => '<EMAIL>',],
            ['name' => 'Nguyen Thi Thanh HAI ', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Hoang Thi THANH ', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Tran Tien TOAN ', 'company' => 'HRI', 'team' => 'HRI Sale', 'role' => 'Nhân viên Sale', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Dang Thi Thuy LINH ', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
            ['name' => 'Be Thi Hong NHUNG ', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Rec Leader', 'role_org' => 'Team leader', 'email' => '<EMAIL>',],
            ['name' => 'Tran Thi Bao THOA ', 'company' => 'HRI', 'team' => 'HRI Rec', 'role' => 'Recer', 'role_org' => 'Team member', 'email' => '<EMAIL>',],
        ];
    }
}
