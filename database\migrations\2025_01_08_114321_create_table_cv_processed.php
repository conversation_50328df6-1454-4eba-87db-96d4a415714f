<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('cv_processed')) {
            Schema::create('cv_processed', function (Blueprint $table) {
                $table->id();
                $table->integer('cv_id');
                $table->string('public_path');
                $table->string('private_path');
                $table->string('created_file_private');
                $table->string('base_file_name');
                $table->text('raw_data');
                $table->boolean('push_to_recland');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cv_processed');
    }
};
