<?php

namespace App\DataTables;

use App\Services\ApplyJobService;
use App\Traits\DataTableTrait;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Services\DataTable;

class ApplyJobDataTable extends DataTable
{
    use DataTableTrait;

    protected $applyJobService;
    protected $user;

    public function __construct(ApplyJobService $applyJobService)
    {
        $this->ajaxData = [
            'type' => 'GET',
            'data' => 'function(d){
                var created_by = $("#filter_created_by").val();
                var status = $("#filter_status").val();
                if(created_by) d.created_by = created_by;
                if(status) d.status = status;
            }',
        ];

        $this->dataParameter = [
            'stateSaveParams' => "function (settings,data) {

            }",
            'stateLoadParams' => "function (settings,data) {

            }",
            'searching'       => true,
            'initComplete' => "function(settings, json) {
                var api = this.api();
                $('#btn-filter').on('click', function() {
                    api.ajax.reload();
                });
                
                $('#btn-reset-filter').on('click', function() {
                    $('#filter_created_by').val('').trigger('change');
                    $('#filter_status').val('').trigger('change');
                    api.ajax.reload();
                });
            }",
        ];
        $this->user = backpack_auth()->user();
        $this->applyJobService = $applyJobService;
    }

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        $userLogin = $this->user;
        return (new EloquentDataTable($query))
            ->addColumn('candidate_name', function ($item) {
                if (!empty($item->candidate)) {
                    return '<a href="' . backpack_url('/candidate/' . $item->candidate->id . '/show') . '" target="_blank" class="btn btn-sm btn-link"><span> ' . $item->candidate->name . '</span></a>';
                } else {
                    return '';
                }
            })
            ->addColumn('candidate_email', function ($item) {
                return optional($item->candidate)->email;
            })
            // ->addColumn('job_id', function ($item) {
            //     return optional($item->job)->title;
            // })
            ->addColumn('cv_job_title', function ($item) {
                return optional($item->cv)->job_title;
            })
            ->addColumn('status', function ($item) {
                return optional($item->statusApply)->name;
            })
            ->addColumn('cv_id', function ($item) {
                return optional($item->statusApply)->name;
            })
            ->addColumn('url_cv_public', function ($item) {
                if (!empty($item->cv->url_cv_public)) {
                    if ($item->canViewCv()) {
                        return '<a href="' . $item->cv->url_cv_public . '" target="_blank" class="btn btn-sm btn-link"><span><i class="la la-eye"></i> Xem Cv</span></a>';
                    }
                }
                return '';
            })
            ->addColumn('url_cv_private', function ($item) {
                if (!empty($item->cv->url_cv_private)) {
                    if ($item->canViewCv()) {
                        return '<a href="' . $item->cv->url_cv_private . '" target="_blank" class="btn btn-sm btn-link"><span><i class="la la-eye"></i> Xem Cv</span></a>';
                    }
                }
                return '';
            })
            ->addColumn('match_score', function ($item) {
                if ($item->matchScoreCv) {
                    return '<a href="javascript:showMatchScore(' . $item->matchScoreCv->id . ')" class="btn btn-sm btn-link">' . $item->matchScoreCv->overview_score . '%</a>';
                }
                return '<a id="btn-create-match-score-' . $item->id . '" href="javascript:createMatchScore(' . $item->id . ')" class="btn btn-sm btn-link">Chấm điểm</a>';
            })

            ->addColumn('created_at', function ($item) {
                return Carbon::parse(optional($item)->created_at)->format('d/m/Y H:i A');
            })
            ->addColumn('created_by', function ($item) {
                return optional($item->createdBy)->name;
            })

            ->addColumn('action', function ($item) use ($userLogin) {
                return view('admins.apply_job.actions', compact('item', 'userLogin'))->render();
            })
            ->addIndexColumn()
            ->rawColumns(['action', 'url_cv_private', 'candidate_name', 'url_cv_public', 'match_score']);
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(Request $request): QueryBuilder
    {
        $data = $request->all();
        if ($request->route()->getName() === 'job.show') $data['job_id'] = $request->id;
        return $this->applyJobService->queryList($data);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            'DT_RowIndex'     => ['title' => 'STT', 'data' => 'DT_RowIndex', 'orderable' => false, 'searchable' => false, 'style' => 'width:1% !important'],
            'candidate_name'  => ['title' => 'Tên ứng viên'],
            'candidate_email' => ['title' => 'Email'],
            'cv_job_title'    => ['title' => 'CV Title'],
            'status'          => ['title' => 'Trạng thái'],
            'cv_id'           => ['title' => 'CV apply'],
            'url_cv_public'   => ['title' => 'Cv public'],
            'url_cv_private'  => ['title' => 'Cv private'],
            'match_score'     => ['title' => 'Match score'],
            'created_by'      => ['title' => 'Người apply vào job'],
            'created_at'      => ['title' => 'Ngày tạo'],
            'action'          => ['title' => 'Hành động'],
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'ApplyJob_' . date('YmdHis');
    }

    /**
     * Get filter HTML for DataTable
     */
    public function getFilterHtml($id = 0): string
    {
        // Get all users for created_by filter
        $users = \App\Models\User::select('users.id', 'users.name')
            ->join('apply_jobs', 'apply_jobs.created_by', '=', 'users.id')
            ->where('apply_jobs.job_id', $id)->orderBy('users.name')
            ->groupBy('users.id', 'users.name')->get();

        // Get all statuses for status filter
        $statuses = \App\Models\Status::where('group', 'apply-job')->join('apply_jobs', 'apply_jobs.status', '=', 'status.id')
            ->where('apply_jobs.job_id', $id)->select('status.id', 'status.name')->orderBy('status.name')
            ->groupBy('status.id', 'status.name')->get();

        $html = '<div class="row mb-3">';

        // Created by filter
        $html .= '<div class="col-md-4">';
        $html .= '<label for="filter_created_by" class="form-label">Người tạo:</label>';
        $html .= '<select id="filter_created_by" class="form-select">';
        $html .= '<option value="">Tất cả</option>';
        foreach ($users as $user) {
            $html .= '<option value="' . $user->id . '">' . $user->name . '</option>';
        }
        $html .= '</select>';
        $html .= '</div>';

        // Status filter
        $html .= '<div class="col-md-4">';
        $html .= '<label for="filter_status" class="form-label">Trạng thái:</label>';
        $html .= '<select id="filter_status" class="form-select">';
        $html .= '<option value="">Tất cả</option>';
        foreach ($statuses as $status) {
            $html .= '<option value="' . $status->id . '">' . $status->name . '</option>';
        }
        $html .= '</select>';
        $html .= '</div>';

        // Filter buttons
        $html .= '<div class="col-md-4 d-flex align-items-end">';
        $html .= '<button type="button" id="btn-filter" class="btn btn-primary me-2">Lọc</button>';
        $html .= '<button type="button" id="btn-reset-filter" class="btn btn-secondary">Reset</button>';
        $html .= '</div>';

        $html .= '</div>';

        return $html;
    }
}
