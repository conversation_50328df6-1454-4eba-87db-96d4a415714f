<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\MatchCvJob;
use App\Models\ApplyJob;
use App\Models\Cv;
use App\Models\Job;
use App\Models\MatchScoreCv;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessOldCvMatching extends Command
{
    protected $signature = 'cv:process-old-matching 
                            {--chunk=100 : Number of records to process in each chunk}
                            {--year=2025: Year to filter records}
                            {--type=apply-job : Type of processing (apply-job, cv, both)}
                            {--job-id= : Specific job ID to match CVs against}';
    protected $description = 'Process CV matching for ApplyJobs or CVs from specified year';

    public function handle(): int
    {
        $type = $this->option('type');
        $year = (int) $this->option('year');
        $chunkSize = (int) $this->option('chunk');
        $jobId = $this->option('job-id');

        $this->info("Starting CV matching process...");
        $this->info("Type: {$type}");
        $this->info("Year: {$year}");
        $this->info("Chunk size: {$chunkSize}");

        if ($jobId) {
            $this->info("Specific Job ID: {$jobId}");
        }

        switch ($type) {
            case 'apply-job':
                return $this->processApplyJobs($year, $chunkSize, $jobId);
            case 'cv':
                return $this->processCvs($year, $chunkSize, $jobId);
            case 'both':
                $this->processApplyJobs($year, $chunkSize);
                return $this->processCvs($year, $chunkSize, $jobId);
            default:
                $this->error("Invalid type. Use: apply-job, cv, or both");
                return self::FAILURE;
        }
    }

    private function processApplyJobs(int $year, int $chunkSize, ?string $jobId = null): int
    {
        $this->info('Processing ApplyJobs...');
        $totalProcessed = 0;

        ApplyJob::query()
            ->whereYear('created_at', $year)
            ->whereDoesntHave('matchScores', function ($query) {
                $query->where('parent_type', ApplyJob::class);
            })
            ->when($jobId, function ($query) use ($jobId) {
                $query->where('job_id', $jobId);
            })
            ->with(['job', 'cv'])
            ->chunk($chunkSize, function ($applyJobs) use (&$totalProcessed) {
                foreach ($applyJobs as $applyJob) {
                    if (!$applyJob->cv || !$applyJob->job) {
                        $this->warn("Skipping ApplyJob ID: {$applyJob->id} - Missing CV or Job");
                        continue;
                    }

                    try {
                        $this->info("Processing ApplyJob ID: {$applyJob->id}");

                        MatchCvJob::dispatch($applyJob, $applyJob->job);

                        $totalProcessed++;

                        if ($totalProcessed % 10 === 0) {
                            $this->info("Processed {$totalProcessed} ApplyJobs...");
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to dispatch CV matching job for ApplyJob', [
                            'apply_job_id' => $applyJob->id,
                            'cv_id' => $applyJob->cv_id,
                            'job_id' => $applyJob->job_id,
                            'error' => $e->getMessage(),
                        ]);
                        $this->error("Failed to process ApplyJob ID: {$applyJob->id}");
                    }
                }
            });

        $this->info("Completed ApplyJobs! Total processed: {$totalProcessed}");
        return self::SUCCESS;
    }

    private function processCvs(int $year, int $chunkSize, ?string $jobId = null): int
    {
        $this->info('Processing CVs...');
        $totalProcessed = 0;

        if ($jobId) {
            $job = Job::find($jobId);
            if (!$job) {
                $this->error("Job ID {$jobId} not found");
                return self::FAILURE;
            }
            $this->info("Matching CVs against Job: {$job->job_name} (ID: {$job->id})");
        }

        $query = Cv::query()
            ->whereYear('created_at', $year)
            ->whereNotNull('cv_public');

        if (!$jobId) {
            // Nếu không có job ID cụ thể, chỉ xử lý CV chưa có match score nào
            $query->whereDoesntHave('matchScores', function ($query) {
                $query->where('parent_type', Cv::class);
            });
        } else {
            // Nếu có job ID cụ thể, chỉ xử lý CV chưa match với job đó
            $query->whereDoesntHave('matchScores', function ($query) use ($jobId) {
                $query->where('parent_type', Cv::class)
                    ->where('job_id', $jobId);
            });
        }

        $query->chunk($chunkSize, function ($cvs) use (&$totalProcessed, $jobId) {
            foreach ($cvs as $cv) {
                try {
                    if ($jobId) {
                        // Match với job cụ thể
                        $job = Job::find($jobId);
                        $this->info("Processing CV ID: {$cv->id} against Job ID: {$jobId}");
                        MatchCvJob::dispatch($cv, $job);
                        $totalProcessed++;
                    } else {
                        // Match với tất cả jobs active
                        $activeJobs = Job::where('status', '!=', 'closed')->get();

                        foreach ($activeJobs as $job) {
                            $this->info("Processing CV ID: {$cv->id} against Job ID: {$job->id}");
                            MatchCvJob::dispatch($cv, $job);
                        }
                        $totalProcessed++;
                    }

                    if ($totalProcessed % 5 === 0) {
                        $this->info("Processed {$totalProcessed} CVs...");
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to dispatch CV matching job for CV', [
                        'cv_id' => $cv->id,
                        'job_id' => $jobId,
                        'error' => $e->getMessage(),
                    ]);
                    $this->error("Failed to process CV ID: {$cv->id}");
                }
            }
        });

        $this->info("Completed CVs! Total processed: {$totalProcessed}");
        return self::SUCCESS;
    }
}
