<?php

namespace App\Console\Commands;

use App\Models\Job;
use Illuminate\Console\Command;

class UpdateJobCodes extends Command
{
    protected $signature = 'jobs:update-codes';
    protected $description = 'Update job codes for all existing jobs';

    public function handle()
    {
        $this->info('Bắt đầu cập nhật job codes...');
        
        $jobs = Job::whereNull('job_code')->orWhere('job_code', '')->get();
        $bar = $this->output->createProgressBar($jobs->count());
        
        $jobs->each(function ($job) use ($bar) {
            $job->job_code = 'J' . str_pad($job->id, 5, '0', STR_PAD_LEFT);
            $job->saveQuietly();
            $bar->advance();
        });
        
        $bar->finish();
        $this->newLine();
        $this->info('Đã cập nhật xong ' . $jobs->count() . ' job codes!');
    }
} 