<?php

namespace App\Events;

use App\Models\Job;
use App\Models\LogTask;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class JobCreatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $jobEvent;
    /**
     * Create a new event instance.
     */
    public function __construct(Job $jobEvent)
    {
        $this->jobEvent = $jobEvent;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
