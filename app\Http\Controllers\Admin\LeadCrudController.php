<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Requests\LeadRequest;
use App\Models\Company;
use App\Models\Contact;
use App\Models\LogLead;
use App\Models\Status;
use App\Models\User;
use App\Services\LogLeadService;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Class LeadCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class LeadCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;


    protected $statusService;
    protected $leadService;


    public function __construct(StatusService $statusService, LogLeadService $leadService)
    {
        $this->statusService = $statusService;
        $this->leadService = $leadService;
        parent::__construct();
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Lead::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/lead');
        CRUD::setEntityNameStrings('lead', 'leads');
        CRUD::addClause('mine');
        // CRUD::denyAccess('update');
        CRUD::denyAccess('delete');

        $this->crud->setListView('admins.leads.list');
        //        if($this->CanEditLeads()){
        //            CRUD::allowAccess('update');
        //        }
        //        $this->crud->enableAjaxTable();
    }


    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(LeadRequest::class);
        CRUD::addFields($this->fieldData());

        Widget::add()->type('script')->content('assets/js/admin/custom.js');

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        if ($this->crud->getCurrentEntry()->canEditLeads()) {
            $this->setupCreateOperation();
        } else {
            abort(403, 'Unauthorized action.');
        }
    }

    protected function setupShowOperation()
    {
        $this->crud->setShowView('admins.leads.show');
        Widget::add([
            'type'    => 'style',
            'content' => 'assets/css/admin/custom.css',
        ]);
    }

    public function fetchCompany()
    {
        return $this->fetch(\App\Models\Company::class);
    }

    protected function fieldData()
    {
        // dd($this->statusService->getStatusWithSlug('lead-source'));
        return [
            [
                'name' => 'company_name',
                'label' => 'Tên công ty',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Chưa có khách hàng',
            ],
            [
                'name' => 'contact_name',
                'label' => 'Tên người liên hệ',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Chưa có khách hàng',
            ],
            [
                'name' => 'email',
                'label' => 'Email người liên hệ',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Chưa có khách hàng',
            ],
            [
                'name' => 'phone',
                'label' => 'Số điện thoại người liên hệ',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Chưa có khách hàng',
            ],
            [
                'type'                 => "relationship",
                'name'                 => 'company_id',
                'attribute'            => 'name',             // foreign key attribute that is shown to user
                'ajax'                 => true,
                'placeholder'          => 'Chọn công ty',
                // 'default'              => $candidate_id,
                'label'                => "Công ty",
                'entity'               => 'company',        // the method that defines the relationship in your Model
                'model'                => Company::class,   // foreign key model
                'minimum_input_length' => 0,
                'wrapper'              => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Đã có khách hàng',
                // 'inline_create' => [ // specify the entity in singular
                //     'entity' => 'company', // the entity in singular
                //     // OPTIONALS
                //     'force_select' => true, // should the inline-created entry be immediately selected?
                //     // 'modal_class' => 'modal-dialog modal-xl', // use modal-sm, modal-lg to change width
                //     // 'modal_route' => route('candidate-inline-create'), // InlineCreate::getInlineCreateModal()
                //     // 'create_route' =>  route('candidate-inline-create-save'), // InlineCreate::storeInlineCreate()
                //     'add_button_label' => 'Thêm công ty mới', // configure the text for the `+ Add` inline button
                //     'modal_class' => 'modal-dialog modal-xl modal-dialog-centered', // use modal-sm, modal-lg to change width
                //     // 'include_main_form_fields' => [ 'name', 'email', 'mobile', 'gender', 'dob', 'address', 'university',], // pass certain fields from the main form to the modal, get them with: request('main_form_fields')
                // ]
                // 'inline_create' => true
            ],
            // select2ModelData('Company', 'select2', 'company', Company::class, 'name', 'form-group col-md-6', ''),
            [
                'label'                   => "Liên hệ", // Table column heading
                'type'                    => 'select2_from_ajax',
                'name'                    => 'contact_id',                // the column that contains the ID of that connected entity;
                'entity'                  => 'contact',                   // the method that defines the relationship in your Model
                'attribute'               => 'name',                      // foreign key attribute that is shown to user
                'data_source'             => route('contact.getContact'), // url to controller search function (with /{id} should return model)
                'placeholder'             => 'Select an option',          // placeholder for the select
                'include_all_form_fields' => true,                        //sends the other form fields along with the request so it can be filtered.
                'minimum_input_length'    => 0,                           // minimum characters to type before querying results
                'dependencies'            => ['company'],                 // when a dependency changes, this select2 is reset to null
                'method'                  => 'POST',                      // optional - HTTP method to use for the AJAX call (GET, POST)
                'wrapperAttributes'       => ['class' => 'form-group col-md-6'],
                'tab' => 'Đã có khách hàng',

            ],
            [   // Textarea
                'name'  => 'lead_content',
                'label' => 'Nội dung chăm sóc',
                'type'  => 'textarea',
            ],
            [
                'name'            => 'source',
                'label'           => 'Nguồn',
                'type'            => 'select_from_array',
                'allows_null'     => false,
                'options'         => ['' => '---'] + $this->statusService->getStatusWithSlug('lead-source'),
                'wrapper'         => [
                    'class' => 'form-group col-md-6',
                ],
                'validationRules' => '',
            ],
            selectFormArrayData('service_id', 'Nhóm sản phẩm KH', 'form-group col-md-3', $this->statusService->getStatus('service-name', true), null),
            //            select2ModelData('Người phụ trách', 'select', 'user_id', User::class, 'name', 'form-group col-md-3'),
            [
                // 1-n relationship
                'label'     => 'Người phụ trách', // Table column heading
                'type'      => 'select2',
                'name'      => 'user_id', // the column that contains the ID of that connected entity;
                'entity'    => 'user', // the method that defines the relationship in your Model
                'attribute' => 'name', // foreign key attribute that is shown to user
                'model'     => User::class, // foreign key model
                'wrapper'              => [
                    'class' => 'form-group col-md-3',
                ],
            ],
        ];
    }

    protected function setupListOperation()
    {
        $this->filterData();
        // $this->crud->removeButton('create');
        //        CRUD::setFromDb();
        CRUD::column('row_number')->type('row_number')->label('#')->orderable(false);
        $this->crud->column([
            'name' => 'company_id',
            'label' => "Company Name",
            // 'type' => 'relationship',
            // 'entity'    => 'company',
            // 'attribute' => 'name',
            // 'model'     => Company::class,
            'type' => 'closure',
            'function' => function ($entry) {
                return $entry->company ? $entry->company->name : $entry->company_name;
            },
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('company_name', 'like', '%' . $searchTerm . '%')
                    ->orWhereHas('company', function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%');
                    });
            }
        ]);
        $this->crud->column([
            'name' => 'contact_id',
            'label' => "Contact Name",
            // 'type' => 'relationship',
            // 'entity'    => 'contact',
            // 'attribute' => 'name',
            // 'model'     => Contact::class,
            'type' => 'closure',
            'function' => function ($entry) {
                $data = $entry->contact ? $entry->contact->name : $entry->contact_name;
                return Str::limit($data, 20);
            }
        ]);
        //        $this->crud->column([
        //            'name' => 'lead_content',
        //            'label' => 'Content'
        //        ]);
        $this->crud->column([
            'name' => 'status',
            'label' => "Status",
            'type' => 'relationship',
            'entity'    => 'statusData',
            'attribute' => 'name',
            'model'     => Status::class,
        ]);
        //        $this->crud->column([
        //            'name' => 'createdBy',
        //            'label' => "Người tạo",
        //            'type' => 'relationship',
        //            'entity'    => 'createdBy',
        //            'attribute' => 'name',
        //            'model'     => User::class,
        //        ]);
        //        $this->crud->column([
        //            'name' => 'service_id',
        //            'label' => "Nhóm sản phẩm KH",
        //            'type' => 'relationship',
        //            'entity'    => 'service',
        //            'attribute' => 'name',
        //            'model'     => User::class,
        //        ]);
        $this->crud->column([
            'label' => 'Nhóm sản phẩm KH/Nguồn KH',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if (!empty($entry->service) || !empty($entry->sourceData)) {
                    return '<strong>' . optional($entry->service)->name . '</strong><br><div > ' . optional($entry->sourceData)->name . '</div>';
                }
                return '';
            }
        ]);
        $this->crud->column([
            'label' => 'Người tạo/Người phụ trách',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if (!empty($entry->createdBy) || !empty($entry->user)) {
                    return '<strong>' . optional($entry->createdBy)->name . '</strong><br><div > ' . optional($entry->user)->name . '</div>';
                }
                return '';
            }
        ]);

        CRUD::addColumn([
            'name' => 'last',
            'label' => 'Cập nhật cuối',
            // Table column heading
            'type' => 'model_function_attribute',
            'function_name' => 'lastLog',
            // the method in your Model
            // 'function_parameters' => [$one, $two], // pass one/more parameters to that method
            'attribute' => 'content',
            // 'limit' => 100, // Limit the number of characters shown
            'escaped' => false, // echo using {!! !!} instead of {{ }}, in order to render HTML
        ]);

        $this->crud->column([
            'name' => 'created_at',
            'label' => "Created at",
        ]);
    }

    public function filterData()
    {
        $this->crud->addFilter(
            [
                'name' => 'company',
                'type' => 'select2_multiple',
                'label' => 'Company'
            ],
            function () {
                return Company::pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('company', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'contact',
                'type' => 'select2_multiple',
                'label' => 'Contact'
            ],
            function () {
                return Contact::pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('contact', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        $this->crud->addFilter([
            'name' => 'createdBy',
            'type' => 'select2_multiple',
            'label' => 'Người tạo',
        ], function () {
            $user = User::all();
            foreach ($user as $item) {
                $user_name[$item->id] = Utils::getUsernameFromEmail($item->email);
            }
            return $user_name;
        }, function ($value) {
            $this->crud->query->whereHas('createdBy', function ($query) use ($value) {
                $query->whereIn('id', json_decode($value));
            });
        });

        $this->crud->addFilter([
            'name' => 'user',
            'type' => 'select2_multiple',
            'label' => 'Người phụ trách',
        ], function () {
            $user = User::all();
            foreach ($user as $item) {
                $user_name[$item->id] = Utils::getUsernameFromEmail($item->email);
            }
            return $user_name;
        }, function ($value) {
            $this->crud->query->whereHas('user', function ($query) use ($value) {
                $query->whereIn('id', json_decode($value));
            });
        });

        $this->crud->addFilter(
            [
                'name' => 'statusData',
                'type' => 'select2_multiple',
                'label' => 'Status'
            ],
            function () {
                return Status::where('group', 'status-lead')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('statusData', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'service_id',
                'type' => 'select2',
                'label' => 'Nhóm sản phẩm KH'
            ],
            function () {
                $status = $this->statusService->getStatus('service-name', true);
                return $status;
            },
            function ($value) {
                // dd($value);
                $this->crud->addClause('where', 'service_id', $value);
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'source',
                'type' => 'select2',
                'label' => 'Nguồn KH'
            ],
            function () {
                $status = $this->statusService->getStatus('lead-source', true);
                return $status;
            },
            function ($value) {
                // dd($value);
                $this->crud->addClause('where', 'source', $value);
            }
        );

        CRUD::filter('from_to')
            ->type('date_range')
            ->label('Khoảng thời gian tạo ')
            ->whenActive(function ($value) {
                $dates = json_decode($value);
                CRUD::addClause('where', 'updated_at', '>=', $dates->from);
                CRUD::addClause('where', 'updated_at', '<=', $dates->to . ' 23:59:59');
            });
    }

    public function import(Request $request)
    {
        try {
            if (!$request->hasFile('file')) {
                return response()->json([
                    'message' => 'Vui lòng chọn file để import'
                ], 400);
            }

            $file = $request->file('file');
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Bỏ qua hàng tiêu đề
            array_shift($rows);

            $validSources = Status::where('group', 'lead-source')->pluck('slug_value', 'name')->toArray();
            $validProductGroups = Status::where('group', 'service-name')->pluck('id', 'name')->toArray();
            $validStatus = Status::where('group', 'status-lead')->pluck('id', 'name')->toArray();
            $user_ids = User::pluck('id', 'email')->toArray();

            // Cache danh sách users để tránh query nhiều lần
            $userEmails = User::pluck('id', 'email')->toArray();

            $successCount = 0;
            $errors = [];
            $logLeadService = new LogLeadService();
            foreach ($rows as $index => $row) {
                try {
                    // Map dữ liệu từ excel
                    $leadData = [
                        'company_name' => $row[0] ?? null,
                        'contact_name' => $row[1] ?? null,
                        'phone'        => $row[2] ?? null,
                        'email'        => $row[3] ?? null,
                        'lead_content' => $row[4] ?? null,
                        // 'source'       => isset($validSources[$row[5]]) ? $validSources[$row[5]] : null,
                        'source'       => isset($validSources[$row[5]]) ? $validSources[$row[5]] : 'import-excel',
                        'service_id'   => isset($validProductGroups[$row[6]]) ? $validProductGroups[$row[6]] : null,
                        'status'       => isset($validStatus[$row[7]]) ? $validStatus[$row[7]] : null,
                        'user_id'      => isset($user_ids[strtolower(trim($row[8]))]) ? $user_ids[strtolower(trim($row[8]))] : null
                    ];
                    // dd($leadData);
                    // // Kiểm tra và gán user_id nếu có email người phụ trách
                    // $assigneeEmail = $row[8] ?? null;
                    // if ($assigneeEmail && isset($userEmails[strtolower(trim($assigneeEmail))])) {
                    //     $leadData['user_id'] = $userEmails[strtolower(trim($assigneeEmail))];
                    // }

                    // Validate dữ liệu bắt buộc
                    if (empty($leadData['company_name']) || empty($leadData['contact_name'])) {
                        continue;
                        // throw new \Exception('Tên và công ty không được để trống');
                    }

                    // Tạo lead mới
                    $lead = $this->crud->create($leadData);
                    if (isset($row[9]) && !empty($row[9])) {

                        $log = $logLeadService->create([
                            'lead_id'      => $lead->id,
                            'user_id'      => backpack_auth()->id(),
                            'company_id'   => null,
                            'content'      => $row[9],
                            'contact_id'   => null,
                            'status'       => null,
                            'time_contact' => null,
                        ]);
                        // dd($log);
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Dòng " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            // Tạo thông báo kết quả
            $message = "Import thành công $successCount leads.";
            if (!empty($errors)) {
                $message .= "\nCó " . count($errors) . " lỗi:\n" . implode("\n", $errors);
            }

            return response()->json([
                'message' => $message,
                'success_count' => $successCount,
                'error_count' => count($errors),
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Lỗi khi import: ' . $e->getMessage()
            ], 500);
        }
    }
}
