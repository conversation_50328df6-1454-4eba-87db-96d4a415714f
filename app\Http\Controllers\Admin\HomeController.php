<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\ApplyJobRepository;
use App\Repositories\CvsRepository;

class HomeController extends Controller
{
    protected $applyJobRepo;
    protected $cvRepo;

    public function __construct(ApplyJobRepository $applyJobRepo, CvsRepository $cvRepo)
    {
        $this->applyJobRepo = $applyJobRepo;
        $this->cvRepo = $cvRepo;
    }

    public function index()
    {
        return response()->json(['data' => $this->applyJobRepo->getData()]);
    }

    public function show()
    {
        $entry = $this->applyJobRepo->getData();
        return view('admins.home.index', compact('entry'));
    }

}
