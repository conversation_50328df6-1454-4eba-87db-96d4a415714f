<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GoogleChatData extends Model
{
    use CrudTrait, HasFactory;

    protected $table = 'google_chat_data';

    protected $fillable = [
        'space_name',
        'space_id',
        'space_key',
        'is_active'
    ];

    /**
     * Gửi thông báo đến Google Chat
     *
     * @param string $message Nội dung thông báo
     * @param string|null $spaceId ID của space, nếu null thì sẽ gửi đến tất cả space active
     * @return array Kết quả gửi thông báo
     */
    public static function sendNotification($message, $space_action)
    {
        if (empty($space_action)) {
            return;
        }
        $query = self::query();

        $query->where('space_action', 'like', '%|' . $space_action . '|%');

        $spaces = $query->get();
        // dd($spaces);
        $results = [];

        foreach ($spaces as $space) {
            $result = self::sendToWebhook($space->space_id, $message);
            $results[] = [
                'space_id' => $space->space_id,
                'space_name' => $space->space_name,
                'result' => $result
            ];
        }

        return $results;
    }

    /**
     * Gửi thông báo đến webhook
     *
     * @param string $spaceId
     * @param string $message
     * @return mixed
     */
    protected static function sendToWebhook($spaceId, $message)
    {
        $url = env('N8N_BASE_URL', 'https://n8n.hri.com.vn') . '/webhook/send-google-chat-notification';

        $data = [
            'space_id' => $spaceId,
            'message' => $message
        ];

        $headers = [
            'Content-Type: application/json',
            'match-cv-api-key: ' . env('N8N_KEY')
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => $error
            ];
        }

        return [
            'success' => true,
            'response' => $response
        ];
    }
}
