<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class PublicApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!empty(env('PUBLIC_API_KEY')) && $request->header('Authorization') == env('PUBLIC_API_KEY')) {
            return $next($request);
        }
        abort(401, 'Unauthorized action.');
    }
}
