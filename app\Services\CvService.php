<?php

namespace App\Services;


use App\Models\Candidate;
use App\Models\Cv;
use App\Models\Note;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class CvService
{
    private static $instance = null;

    public static function getInstance(): ?CvService
    {
        if (self::$instance == null) {
            self::$instance = new CvService();
        }

        return self::$instance;
    }


    public function queryList(array $filters = [])
    {
        $limit = !empty($filters['limit']) ? $filters['limit'] : 10;
        return Cv::query()
            ->when(!empty($filters['candidate_id']), function ($query) use ($filters) {
                $query->where('candidate_id', $filters['candidate_id']);
            })->when(!empty($filters['keyword']), function ($query) use ($filters) {
                $query->where('name', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['job_id_apply']), function ($query) use ($filters) {
                $query->whereHas('applies', function ($q) use ($filters) {
                    $q->where('job_id', '<>', $filters['job_id_apply']);
                });
            })->limit($limit)
            ->latest('id');
    }

    public function hideCv($public_url)
    {

        $response = Http::post(env('API_AI_HIDE_CV'), [
            'file' => gen_url_file_s3($public_url),
        ])->json();

        if (isset($response['statusCode']) && $response['statusCode'] == '200') {
            return FileServiceS3::getInstance()->uploadToS3FromLink($response['data']['file_private'], PATH_FOLDER_CV_PRIVATE);
        }
        return false;
    }

    public function findOrFail($id)
    {
        return Candidate::findOrFail($id);
    }
}
