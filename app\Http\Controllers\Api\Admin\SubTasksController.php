<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubTasks;
use Illuminate\Http\Request;

class SubTasksController extends Controller
{
    public function changeSubtaskActive(Request $request)
    {
        $subtask = SubTasks::find($request->subtask_id);
        if ($subtask) {
            $subtask->active = $request->active;
            $subtask->save();
            return response()->json(['message' => 'Subtask active state updated successfully']);
        } else {
            return response()->json(['message' => 'Subtask not found'], 404);
        }
    }
}
