<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\CheckOutsideAccess;

// --------------------------
// Custom Backpack Routes
// --------------------------
// This route file is loaded automatically by Backpack\Base.
// Routes you generate using Backpack\Generators will be placed here.

Route::group([
    'prefix'     => config('backpack.base.route_prefix', 'admin'),
    'middleware' => array_merge(
        (array)config('backpack.base.web_middleware', 'web'),
        (array)config('backpack.base.middleware_key', 'admin'),
    ),
    'namespace'  => 'App\Http\Controllers\Admin',
], function () { // custom admin routes
    Route::crud('user', 'UserCrudController');
    Route::crud('status', 'StatusCrudController');
    Route::post('status/get-status', 'StatusCrudController@getStatus')->name('status.ajax.get-status');
    Route::post('find-candidate-by-id/{id}', 'CandidateCrudController@findModelById')->name('candidate.ajax.find-candidate-by-id');
    Route::crud('candidate', 'CandidateCrudController');
    Route::get('candidate/ajax-search', 'CandidateCrudController@ajaxSearch')->name('candidate.ajax-search');
    Route::crud('cv', 'CvCrudController');
    Route::get('cv/ajax-search', 'CvCrudController@ajaxSearch')->name('cv.ajax-search');
    Route::get('/dashboard', 'HomeController@show')->name('home.show');

    Route::crud('academic-level', 'AcademicLevelCrudController');
    Route::crud('career-language', 'CareerLanguageCrudController');
    Route::crud('career-level', 'CareerLevelCrudController');
    Route::crud('skill', 'SkillCrudController');
    Route::crud('company', 'CompanyCrudController');
    Route::crud('job', 'JobCrudController');
    Route::get('job/{id}/duplicate', 'JobCrudController@duplicate')->name('job.duplicate');
    Route::crud('apply-job', 'ApplyJobCrudController');
    Route::post('apply-job/{id}/update-cv-private', 'ApplyJobCvController@updateCvPrivate')->name('apply-job.update-cv-private');
    Route::get('apply-job/{id}/cv-private-logs', 'ApplyJobCvController@getCvPrivateLogs')->name('apply-job.cv-private-logs');
    Route::post('apply-job/{id}/upload-interview-evaluation', 'ApplyJobCrudController@uploadInterviewEvaluation')->name('apply-job.upload-interview-evaluation');
    Route::get('apply-job/{id}/preview-interview-evaluation', 'ApplyJobCrudController@previewInterviewEvaluation')->name('apply-job.preview-interview-evaluation');

    Route::get('company/create-lead/{id}', 'CompanyCrudController@createLeadFromCompany')->name('company.create_lead_from_company');
    Route::crud('lead', 'LeadCrudController');
    Route::post('contact/search-data-contact', 'ContactController@getContact')->name('contact.getContact');

    Route::crud('task', 'TaskCrudController');
    Route::crud('internal-company', 'InternalCompanyCrudController');
    Route::crud('department', 'DepartmentCrudController');
    Route::crud('user', 'UserCrudController');

    Route::post('user/{id}/toggle-status', 'App\Http\Controllers\Admin\UserCrudController@toggleStatus')->name('user.toggle-status');
    Route::crud('meeting-room', 'MeetingRoomCrudController');
    Route::crud('meeting-booking', 'MeetingBookingCrudController');
    Route::get('meeting-calendar', 'MeetingBookingCrudController@calendar')->name('meeting.calendar');
});

