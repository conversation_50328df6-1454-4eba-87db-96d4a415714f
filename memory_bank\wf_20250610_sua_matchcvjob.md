# Workflow: Sửa lại MatchCvJob cho dự án CRM HRI

**<PERSON><PERSON><PERSON> thực hiện:** 10/06/2025  
**M<PERSON><PERSON> tiêu:** Sửa lại file `app/Jobs/MatchCvJob.php` từ dự án khác để phù hợp với dự án CRM hiện tại

## Vấn đề ban đầu

File `MatchCvJob.php` được copy từ dự án khác có những vấn đề sau:

1. Sử dụng các model không tồn tại: `SubmitCv`, `WarehouseCv`, `MatchScoreCv`
2. Sử dụng service không tồn tại: `App\Services\Frontend\JobService`
3. Logic không phù hợp với cấu trúc dự án hiện tại

## Các thay đổi đã thực hiện

### 1. Sửa file `app/Jobs/MatchCvJob.php`

**Thay đổi chính:**

-   Thay `SubmitCv|WarehouseCv` thành `Cv|ApplyJob`
-   Loại bỏ dependency vào `JobService` không tồn tại
-   Tạo method `getJobDescription()` tự viết để generate job description từ Job model
-   Cập nhật logic xử lý để phù hợp với models của dự án

**Chi tiết thay đổi:**

```php
// Trước
private SubmitCv|WarehouseCv $cv;
// Sau
private Cv|ApplyJob $cvOrApply;

// Trước
$jobService = resolve(\App\Services\Frontend\JobService::class);
$jobDescription = $jobService->getJobDescription($this->jobModel);
// Sau
$jobDescription = $this->getJobDescription($this->jobModel);
```

### 2. Tạo model mới `app/Models/MatchScoreCv.php`

**Tính năng:**

-   Lưu trữ điểm matching giữa CV và Job
-   Hỗ trợ polymorphic relation với Cv hoặc ApplyJob
-   Có method tính điểm trung bình
-   Cast JSON cho raw_data

**Cấu trúc:**

```php
protected $fillable = [
    'parent_type', 'parent_id', 'job_id', 'cv_id',
    'experience_score', 'skills_score', 'overview_score', 'raw_data'
];
```

### 3. Tạo migration `database/migrations/2025_06_10_102442_create_match_score_cvs_table.php`

**Cấu trúc bảng:**

-   `parent_type`: String - class name của parent (Cv hoặc ApplyJob)
-   `parent_id`: BigInt - ID của parent
-   `job_id`: BigInt - ID của Job
-   `cv_id`: BigInt - ID của CV
-   `experience_score`: Decimal(5,2) - Điểm kinh nghiệm
-   `skills_score`: Decimal(5,2) - Điểm kỹ năng
-   `overview_score`: Decimal(5,2) - Điểm tổng quan
-   `raw_data`: JSON - Dữ liệu thô từ API

**Indexes và constraints:**

-   Unique constraint trên (`parent_type`, `parent_id`, `job_id`, `cv_id`)
-   Foreign keys tới `jobs` và `cvs` tables
-   Indexes trên các cột quan trọng

## Cách sử dụng

```php
// Với CV
$job = Job::find(1);
$cv = Cv::find(1);
MatchCvJob::dispatch($cv, $job);

// Với ApplyJob
$applyJob = ApplyJob::find(1);
MatchCvJob::dispatch($applyJob, $job);
```

## API được sử dụng

-   **URL:** `https://n8n.hri.com.vn/webhook/match-score-cv`
-   **Method:** POST
-   **Headers:**
    -   `content-type: application/json`
    -   `match-cv-api-key: hri@1008`
-   **Payload:**
    ```json
    {
        "job_description": "string",
        "cv_url": "string"
    }
    ```

## Migration cần chạy

```bash
php artisan migrate
```

## 4. Sửa file `app/Console/Commands/ProcessOldCvMatching.php`

**Vấn đề cũ:**

-   Sử dụng model `SubmitCv` không tồn tại

**Thay đổi:**

-   Thay `SubmitCv` thành `ApplyJob` và `Cv`
-   Thêm nhiều options cho command:
    -   `--type` để chọn xử lý ApplyJob, CV hoặc cả hai
    -   `--job-id` để match CV với job cụ thể
    -   `--year` và `--chunk` để tối ưu performance
-   Tách thành 2 methods riêng: `processApplyJobs()` và `processCvs()`

**Cách sử dụng command:**

```bash
# Xử lý ApplyJobs
php artisan cv:process-old-matching --type=apply-job --year=2024

# Xử lý CVs với job cụ thể
php artisan cv:process-old-matching --type=cv --job-id=123

# Xử lý cả hai
php artisan cv:process-old-matching --type=both --year=2024 --chunk=50
```

### 5. Thêm relations vào models

**ApplyJob.php và Cv.php:**

```php
public function matchScores()
{
    return $this->morphMany(MatchScoreCv::class, 'parent');
}
```

## Ghi chú

-   Job description được tạo tự động từ các trường: job_name, description, requirement, benefit, skills, levels
-   API trả về format: `{experience: {score}, skills: {score}, overview: {score}}`
-   Có xử lý lỗi và logging chi tiết
-   Sử dụng queue để xử lý bất đồng bộ
-   Command hỗ trợ xử lý cả ApplyJob và CV riêng lẻ
-   Có kiểm tra tránh xử lý trùng lặp với `whereDoesntHave('matchScores')`
