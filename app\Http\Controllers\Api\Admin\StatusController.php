<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Admin\StatusResource;
use App\Services\StatusService;
use Illuminate\Http\Request;

class StatusController extends Controller
{
    protected $service;

    public function __construct(StatusService $service)
    {
        $this->service = $service;
    }

    public function getAllStatusApplyJob()
    {

        return response()->json(['data' => StatusResource::collection($this->service->getAllStatus('apply-job', true))]);
    }


    public function getAllStatusNullParent($key)
    {

        return response()->json(['data' => StatusResource::collection($this->service->getAllStatus($key, true))]);
    }

}
