<?php

namespace App\Jobs;

use App\Models\Cv;
use App\Services\FileServiceS3;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DownloadCvAttactment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public $cv;
    public function __construct(Cv $cv)
    {
        $this->cv = $cv;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        $path = $this->cv->cv_public;
        $options = [];
        if (strpos($path, 'https://rec.hri.com.vn/secure/attachment') !== false) {
            $options = [
                'basic_auth' => env('JIRA_USER') . ':' . env('JIRA_PASS'),
            ];
        }
        // print_r("bat dau: $path");

        if (
            $path && (strpos($path, 'https://') !== false || strpos($path, 'http://') !== false) &&
            strpos($path, 'https://kho-cv.s3.ap-southeast-1.amazonaws.com/') === false
        ) {
            // print_r("Xu ly $path");
            // die;
            $extension   = pathinfo($path, PATHINFO_EXTENSION);
            $name        = pathinfo($path, PATHINFO_FILENAME);
            $file        = file_get_contents($path);
            $target_path = PATH_FOLDER_SAVE_CV;
            $target_path = FileServiceS3::getInstance()->uploadToS3FromLink($path, $target_path, $options);
            // dd($target_path);

            if ($target_path) {
                $this->cv->cv_public = $target_path;
                $this->cv->save();
            }
        }

        $path = $this->cv->cv_private;
        # if path contain "https://rec.hri.com.vn/secure/attachment"
        if (
            $path && (strpos($path, 'https://') !== false || strpos($path, 'http://') !== false) &&
            strpos($path, 'https://kho-cv.s3.ap-southeast-1.amazonaws.com/') === false
        ) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $name      = pathinfo($path, PATHINFO_FILENAME);
            $file      = file_get_contents($path);
            $target_path = PATH_FOLDER_SAVE_CV; // . '/' . md5($path) . '.' . $extension;
            $target_path = FileServiceS3::getInstance()->uploadToS3FromLink($path, $target_path, $options);
            if ($target_path) {
                $this->cv->cv_private = $target_path;
                $this->cv->save();
            }
        } elseif (empty($this->cv->cv_private)) {
            // UploadHideCv::dispatch($this->cv);
        }
    }
}
